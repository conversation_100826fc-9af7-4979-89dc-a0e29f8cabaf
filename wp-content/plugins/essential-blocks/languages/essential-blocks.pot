# Copyright (C) 2024 WPDeveloper
# This file is distributed under the GPL3+.
msgid ""
msgstr ""
"Project-Id-Version: Essential Blocks 5.0.9\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/essential-blocks\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-11-24T07:07:04+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: essential-blocks\n"

#. Plugin Name of the plugin
#: essential-blocks.php
#: includes/Admin/Admin.php:90
#: includes/Admin/Admin.php:150
msgid "Essential Blocks"
msgstr ""

#. Plugin URI of the plugin
#: essential-blocks.php
msgid "https://essential-blocks.com"
msgstr ""

#. Description of the plugin
#: essential-blocks.php
msgid "The Ultimate Gutenberg blocks library to create WordPress sites in the Gutenberg Block Editor with 60+ essential blocks, patterns, templates for WooCommerce, posts, & more."
msgstr ""

#. Author of the plugin
#: essential-blocks.php
msgid "WPDeveloper"
msgstr ""

#. Author URI of the plugin
#: essential-blocks.php
msgid "https://wpdeveloper.com"
msgstr ""

#: includes/Admin/Admin.php:91
msgid "Essential Blocks %s"
msgstr ""

#: includes/Admin/Admin.php:186
msgid "Congratulations, you’ve successfully installed <strong>Essential Blocks for Gutenberg</strong>. We got <strong>2500+ FREE Gutenberg ready Templates</strong> waiting for you <span class=\"gift-icon\">&#127873;</span>"
msgstr ""

#: includes/Admin/Admin.php:187
msgid ""
"We collect non-sensitive diagnostic data and plugin usage information.\n"
"\t\t\tYour site URL, WordPress & PHP version, plugins & themes and email address to send you exciting deals. This data lets us make sure this plugin always stays compatible with the most\n"
"\t\t\tpopular plugins and themes."
msgstr ""

#: includes/Admin/Admin.php:283
msgid "We hope you're enjoying Essential Block for Gutenberg! Could you please do us a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?"
msgstr ""

#: includes/Admin/Admin.php:294
msgid "Sure, you deserve it!"
msgstr ""

#: includes/Admin/Admin.php:303
msgid "I already did"
msgstr ""

#: includes/Admin/Admin.php:310
msgid "Maybe Later"
msgstr ""

#: includes/Admin/Admin.php:322
msgid "I need help"
msgstr ""

#: includes/Admin/Admin.php:326
msgid "Never show again"
msgstr ""

#: includes/Admin/Admin.php:432
#: includes/Admin/Admin.php:476
#: includes/Admin/Admin.php:517
#: includes/Admin/Admin.php:548
#: includes/Admin/Admin.php:568
#: includes/Admin/Admin.php:616
#: includes/Admin/Admin.php:694
#: includes/Integrations/Data.php:26
#: includes/Integrations/Data.php:53
msgid "Nonce Error"
msgstr ""

#: includes/Admin/Admin.php:435
#: includes/Admin/Admin.php:479
#: includes/Admin/Admin.php:520
#: includes/Admin/Admin.php:551
#: includes/Admin/Admin.php:697
msgid "You are not authorized to save this!"
msgstr ""

#: includes/Admin/Admin.php:463
#: includes/Admin/Admin.php:466
msgid "Something went wrong regarding saving options data."
msgstr ""

#: includes/Admin/Admin.php:504
#: includes/Admin/Admin.php:507
msgid "Something went wrong regarding reset options data."
msgstr ""

#: includes/Admin/Admin.php:532
#: includes/Admin/Admin.php:535
msgid "Invalid Key"
msgstr ""

#: includes/Admin/Admin.php:538
#: includes/Integrations/Data.php:43
#: includes/Integrations/Data.php:72
msgid "Something went wrong regarding getting options data."
msgstr ""

#: includes/Admin/Admin.php:556
msgid "Settings Updated Successfully"
msgstr ""

#: includes/Admin/Admin.php:558
msgid "Couldn't Save Settings Data"
msgstr ""

#: includes/Admin/Admin.php:572
#: includes/Admin/Admin.php:619
#: includes/Integrations/Data.php:30
#: includes/Integrations/Data.php:57
#: includes/Integrations/Form.php:173
#: includes/Integrations/Form.php:222
#: includes/Integrations/GlobalStyles.php:69
#: includes/Integrations/GlobalStyles.php:140
#: includes/Integrations/GoogleMap.php:50
#: includes/Integrations/Instagram.php:25
#: includes/Integrations/NFT.php:103
#: includes/Integrations/NFT.php:119
#: includes/Integrations/OpenVerse.php:53
#: includes/Integrations/OpenVerse.php:68
#: includes/Integrations/OpenVerse.php:114
#: includes/Integrations/OpenVerse.php:150
#: includes/Integrations/PluginInstaller.php:28
msgid "You are not authorized!"
msgstr ""

#: includes/Admin/Admin.php:606
#: includes/Admin/Admin.php:646
#: includes/Admin/Admin.php:704
msgid "Something went wrong regarding getting data."
msgstr ""

#: includes/Admin/Admin.php:742
msgid "<p> <i>📣</i> Introducing Essential Blocks <strong>v5.0</strong> with 5 New WooCommerce Blocks, Taxonomy, Text, Post Meta, Breadcrumbs. For more info, check out this <strong><a target='_blank' href='%s'>changelog</a></strong>.</p>"
msgstr ""

#: includes/blocks.php:60
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Accordion"
msgstr ""

#: includes/blocks.php:71
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button"
msgstr ""

#: includes/blocks.php:81
msgid "Call To Action"
msgstr ""

#: includes/blocks.php:91
msgid "Countdown"
msgstr ""

#: includes/blocks.php:101
msgid "Dual Button"
msgstr ""

#: includes/blocks.php:111
msgid "Flipbox"
msgstr ""

#: includes/blocks.php:121
#: assets/admin/editor/editor.js:1
msgid "Advanced Heading"
msgstr ""

#: includes/blocks.php:132
msgid "Image Comparison"
msgstr ""

#: includes/blocks.php:142
msgid "Image Gallery"
msgstr ""

#: includes/blocks.php:153
msgid "Infobox"
msgstr ""

#: includes/blocks.php:164
#: assets/admin/editor/editor.js:1
msgid "Instagram Feed"
msgstr ""

#: includes/blocks.php:174
msgid "Interactive Promo"
msgstr ""

#: includes/blocks.php:184
#: assets/admin/editor/editor.js:1
msgid "Notice"
msgstr ""

#: includes/blocks.php:194
msgid "Parallax Slider"
msgstr ""

#: includes/blocks.php:204
msgid "Pricing Table"
msgstr ""

#: includes/blocks.php:215
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Progress Bar"
msgstr ""

#: includes/blocks.php:226
#: assets/admin/editor/editor.js:1
msgid "Slider"
msgstr ""

#: includes/blocks.php:237
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Social Icons"
msgstr ""

#: includes/blocks.php:247
#: assets/admin/editor/editor.js:1
msgid "Social Share"
msgstr ""

#: includes/blocks.php:257
msgid "Team Member"
msgstr ""

#: includes/blocks.php:267
msgid "Testimonial"
msgstr ""

#: includes/blocks.php:277
msgid "Toggle Content"
msgstr ""

#: includes/blocks.php:287
#: assets/admin/editor/editor.js:1
msgid "Typing Text"
msgstr ""

#: includes/blocks.php:297
msgid "Wrapper"
msgstr ""

#: includes/blocks.php:307
msgid "Number Counter"
msgstr ""

#: includes/blocks.php:317
msgid "Post Grid"
msgstr ""

#: includes/blocks.php:328
msgid "Feature List"
msgstr ""

#: includes/blocks.php:338
msgid "Row"
msgstr ""

#: includes/blocks.php:349
#: assets/admin/editor/editor.js:1
msgid "Table Of Contents"
msgstr ""

#: includes/blocks.php:360
#: assets/admin/editor/editor.js:1
msgid "Fluent Forms"
msgstr ""

#: includes/blocks.php:370
#: assets/admin/editor/editor.js:1
msgid "Advanced Tabs"
msgstr ""

#: includes/blocks.php:381
#: assets/admin/editor/editor.js:1
msgid "Advanced Navigation"
msgstr ""

#: includes/blocks.php:391
msgid "Woo Product Grid"
msgstr ""

#: includes/blocks.php:402
#: assets/admin/editor/editor.js:1
msgid "Advanced Image"
msgstr ""

#: includes/blocks.php:412
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "WPForms"
msgstr ""

#: includes/blocks.php:422
msgid "Post Carousel"
msgstr ""

#: includes/blocks.php:432
#: assets/admin/editor/editor.js:1
msgid "Advanced Video"
msgstr ""

#: includes/blocks.php:442
msgid "Popup"
msgstr ""

#: includes/blocks.php:452
#: assets/admin/editor/editor.js:1
msgid "Openverse"
msgstr ""

#: includes/blocks.php:462
#: assets/admin/editor/editor.js:1
msgid "NFT Gallery"
msgstr ""

#: includes/blocks.php:473
#: assets/admin/dashboard/admin.js:1
msgid "Google Maps"
msgstr ""

#: includes/blocks.php:483
#: assets/admin/editor/editor.js:1
msgid "Shape Divider"
msgstr ""

#: includes/blocks.php:493
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Form"
msgstr ""

#: includes/blocks.php:504
msgid "Icon Picker"
msgstr ""

#: includes/blocks.php:517
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Text"
msgstr ""

#: includes/blocks.php:528
#: assets/admin/editor/editor.js:1
msgid "Taxonomy"
msgstr ""

#: includes/blocks.php:539
#: assets/admin/editor/editor.js:1
msgid "Product Price"
msgstr ""

#: includes/blocks.php:550
msgid "Post Meta"
msgstr ""

#: includes/blocks.php:560
#: assets/admin/editor/editor.js:1
msgid "Product Rating"
msgstr ""

#: includes/blocks.php:571
#: assets/admin/editor/editor.js:1
msgid "Product Details"
msgstr ""

#: includes/blocks.php:582
msgid "Add To Cart"
msgstr ""

#: includes/blocks.php:593
#: assets/admin/editor/editor.js:1
msgid "Product Images"
msgstr ""

#: includes/blocks.php:604
msgid "Breadcrumbs"
msgstr ""

#: includes/blocks.php:618
msgid "Advanced Search"
msgstr ""

#: includes/blocks.php:629
msgid "Data Table"
msgstr ""

#: includes/blocks.php:640
msgid "Timeline Slider"
msgstr ""

#: includes/blocks.php:650
msgid "News Ticker"
msgstr ""

#: includes/blocks.php:660
msgid "Woo Product Carousel"
msgstr ""

#: includes/blocks.php:670
msgid "Multicolumn Pricing Table"
msgstr ""

#: includes/blocks.php:681
msgid "Fancy Chart"
msgstr ""

#: includes/blocks.php:692
msgid "Stacked Cards"
msgstr ""

#: includes/blocks.php:702
msgid "Testimonial Slider"
msgstr ""

#: includes/blocks.php:712
msgid "Offcanvas"
msgstr ""

#: includes/Blocks/FluentForms.php:105
msgid "Select a form"
msgstr ""

#: includes/Blocks/Form.php:25
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Submit"
msgstr ""

#: includes/Blocks/Form.php:81
msgid "Your form has been submitted Successfully!"
msgstr ""

#: includes/Blocks/Form.php:82
msgid "Your form couldn't been submitted! Please try again"
msgstr ""

#: includes/Blocks/GoogleMap.php:86
msgid "Please add your Google Map API to display Google Maps Block"
msgstr ""

#: includes/Blocks/PostMeta.php:23
msgid "Author: "
msgstr ""

#: includes/Blocks/PostMeta.php:25
msgid "Published Date: "
msgstr ""

#: includes/Blocks/PostMeta.php:27
msgid "SKU: "
msgstr ""

#: includes/Blocks/TableOfContents.php:38
msgid "Table of Contents"
msgstr ""

#: includes/Blocks/WooProductGrid.php:75
msgid "Sold "
msgstr ""

#: includes/Blocks/WooProductGrid.php:85
#: includes/Blocks/WooProductGrid.php:88
msgid "Buy Now"
msgstr ""

#: includes/Blocks/WooProductGrid.php:86
msgid "Select Options"
msgstr ""

#: includes/Blocks/WooProductGrid.php:87
msgid "View Products"
msgstr ""

#: includes/Blocks/WooProductGrid.php:89
msgid "Read More"
msgstr ""

#: includes/Blocks/WooProductGrid.php:91
msgid "Visit Product"
msgstr ""

#: includes/Blocks/WPForms.php:57
msgid "Select a WPForm"
msgstr ""

#: includes/Blocks/WPForms.php:64
msgid "Create a Form First"
msgstr ""

#: includes/Core/BlocksPatterns.php:68
msgid "Essential blocks"
msgstr ""

#: includes/Core/PageTemplates.php:34
msgid "Essential Blocks Fullwidth Template"
msgstr ""

#: includes/Core/PageTemplates.php:35
msgid "Essential Blocks Blank Template"
msgstr ""

#: includes/Dependencies/Insights.php:685
msgid "What we collect"
msgstr ""

#: includes/Dependencies/Insights.php:686
msgid "Sure, I'd like to help"
msgstr ""

#: includes/Dependencies/Insights.php:687
msgid "No Thanks."
msgstr ""

#: includes/Dependencies/Insights.php:798
msgid "Sorry to see you go &#128542;"
msgstr ""

#: includes/Dependencies/Insights.php:799
msgid "If you have a moment, please share why you are deactivating this plugin. All submissions are anonymous and we only use this feedback to improve Essential Blocks for Gutenberg."
msgstr ""

#: includes/Dependencies/Insights.php:802
msgid "I no longer need the plugin"
msgstr ""

#: includes/Dependencies/Insights.php:804
msgid "I found a better plugin"
msgstr ""

#: includes/Dependencies/Insights.php:805
msgid "Please share which plugin"
msgstr ""

#: includes/Dependencies/Insights.php:807
msgid "I couldn't get the plugin to work"
msgstr ""

#: includes/Dependencies/Insights.php:808
msgid "It's a temporary deactivation"
msgstr ""

#: includes/Dependencies/Insights.php:810
msgid "Other"
msgstr ""

#: includes/Dependencies/Insights.php:811
msgid "Please share the reason"
msgstr ""

#: includes/Dependencies/Insights.php:1019
msgid "Submitting form"
msgstr ""

#: includes/Dependencies/Insights.php:1075
msgid "Submit and Deactivate"
msgstr ""

#: includes/Dependencies/Insights.php:1076
msgid "Skip & Deactivate"
msgstr ""

#: includes/Integrations/AssetGeneration.php:24
msgid "Sorry, you are not allowed to do this operation."
msgstr ""

#: includes/Integrations/AssetGeneration.php:34
#: includes/Integrations/Form.php:36
#: includes/Integrations/Form.php:169
#: includes/Integrations/Form.php:218
#: includes/Integrations/GoogleMap.php:29
#: includes/Integrations/GoogleMap.php:47
#: includes/Integrations/Instagram.php:22
#: includes/Integrations/NFT.php:39
#: includes/Integrations/NFT.php:100
#: includes/Integrations/NFT.php:116
#: includes/Integrations/OpenVerse.php:65
#: includes/Integrations/OpenVerse.php:111
#: includes/Integrations/OpenVerse.php:146
#: includes/Integrations/OpenVerse.php:195
#: includes/Integrations/Pagination.php:20
#: includes/Integrations/PluginInstaller.php:25
msgid "Nonce did not match"
msgstr ""

#: includes/Integrations/AssetGeneration.php:38
msgid "No post data found!"
msgstr ""

#: includes/Integrations/AssetGeneration.php:46
msgid "Successfully saved data!"
msgstr ""

#: includes/Integrations/Data.php:40
msgid "No Data Found!"
msgstr ""

#: includes/Integrations/Data.php:69
msgid "Couldn't save data"
msgstr ""

#: includes/Integrations/Form.php:33
#: includes/Integrations/Form.php:166
#: includes/Integrations/Form.php:215
msgid "Invalid Data"
msgstr ""

#: includes/Integrations/Form.php:39
msgid "Invalid Request!"
msgstr ""

#: includes/Integrations/Form.php:284
msgid "You do not have permission to create form"
msgstr ""

#: includes/Integrations/GlobalStyles.php:45
#: includes/Integrations/GlobalStyles.php:65
#: includes/Integrations/GlobalStyles.php:116
#: includes/Integrations/GlobalStyles.php:136
msgid "Nonce did not match!"
msgstr ""

#: includes/Integrations/NFT.php:95
msgid "Something went wrong."
msgstr ""

#: includes/Plugin.php:151
msgid "Cloning is forbidden."
msgstr ""

#: includes/Plugin.php:161
msgid "Unserializing instances of this class is forbidden."
msgstr ""

#: includes/Utils/Helper.php:591
msgid "Invalid icon type"
msgstr ""

#: includes/Utils/Helper.php:730
msgid "Heads up!"
msgstr ""

#: includes/Utils/Helper.php:743
msgid "What's new?"
msgstr ""

#: includes/Utils/HttpRequest.php:27
msgid "URL cannot be empty."
msgstr ""

#: views/post-carousel.php:59
#: views/post-grid.php:90
msgid "No Posts Found"
msgstr ""

#: views/product-grid.php:58
msgid "No Product Found"
msgstr ""

#: views/product-images.php:54
msgid "Awaiting product image"
msgstr ""

#: views/welcome.php:16
msgid "Welcome To Essential Blocks 4.0.0"
msgstr ""

#: views/welcome.php:17
msgid "The most useful Gutenberg blocks library, Essential Blocks v4.0.0 brings many advanced changes in design, customization control, and new block series. Let’s take a look"
msgstr ""

#: views/welcome.php:22
msgid "What’s New In"
msgstr ""

#: views/welcome.php:22
msgid " Check From Changelog"
msgstr ""

#: views/welcome.php:23
msgid "Taking into account our customers’ previous demands and recent requests for new features, Essential Blocks v4.0.0 offers exclusive features, block customization controls, and stunning blocks. Visit our product"
msgstr ""

#: views/welcome.php:23
msgid "changelog"
msgstr ""

#: views/welcome.php:23
msgid " to see the latest version updates."
msgstr ""

#: views/welcome.php:29
msgid "Global Controls"
msgstr ""

#: views/welcome.php:30
msgid "The most exclusive addition to"
msgstr ""

#: views/welcome.php:30
msgid " is Global control. Any block can now be saved with customized styling so that it can be reused in other pages or posts. It saves time by not having to recreate the same block styling on all pages or posts."
msgstr ""

#: views/welcome.php:41
msgid "New Block:"
msgstr ""

#: views/welcome.php:42
msgid "Introducing new block:"
msgstr ""

#: views/welcome.php:42
msgid " Using this stunning block, you can easily display location, route, add additional labels, customize the labels with images, icons, etc. The Google Maps block comes with 4 ready-to-use presets for easy customization."
msgstr ""

#: views/welcome.php:57
#: assets/admin/dashboard/admin.js:1
msgid "Assets Generation"
msgstr ""

#: views/welcome.php:58
msgid "This amazing feature, Assets Generation, will help you keep your website’s loading speed fast and secure against heavy scripts and CSS files."
msgstr ""

#: views/welcome.php:59
#: views/welcome.php:65
msgid "Release Note"
msgstr ""

#: views/welcome.php:63
msgid "Google Fonts"
msgstr ""

#: views/welcome.php:64
msgid "Essential Blocks is with now Google Fonts compatible. You can choose your preferred font family in one click from thousands of options."
msgstr ""

#: views/welcome.php:73
msgid "Introducing New ⚙️ Setting Controls"
msgstr ""

#: views/welcome.php:74
msgid "With the new setup of Essential Blocks, configuring API keys, managing blocks as modular controls, and getting the best suitable Gutenberg template have never been easier. Get unlimited customization controls and features for a smoother experience."
msgstr ""

#: views/welcome.php:81
#: assets/admin/dashboard/admin.js:1
msgid "Dashboard"
msgstr ""

#: views/welcome.php:85
msgid "Gutenberg Templates"
msgstr ""

#: views/welcome.php:89
#: assets/admin/dashboard/admin.js:1
msgid "Blocks"
msgstr ""

#: views/welcome.php:93
#: assets/admin/dashboard/admin.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Settings"
msgstr ""

#: views/welcome.php:98
msgid "Go To Dashboard"
msgstr ""

#: views/woocommerce/single-rating.php:34
msgid "%s "
msgid_plural "%s "
msgstr[0] ""
msgstr[1] ""

#: assets/admin/controls/controls.js:1
msgid "Gradient Type"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Radial Type"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "First Color Position"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Second Color Position"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Angle"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Center X Position"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Center Y Position"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Gradient Colors"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Global Gradient Colors"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Custom Gradient Colors"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Background Overlay"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Normal"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hover"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Overlay Type"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Overlay Color"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Overlay Image"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Upload Image"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Default"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Center Center"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Center Left"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Center Right"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Top Center"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Top Left"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Top Right"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bottom Center"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bottom Left"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bottom Right"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Custom"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Scroll"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fixed"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "No-repeat"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Repeat"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Repeat-x"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Repeat-y"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Auto"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Cover"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Contain"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Opacity"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Blend Mode"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Multiply"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Screen"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Overlay"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Darken"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Lighten"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Color Dodge"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Saturation"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Color"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Luminosity"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "CSS Filters"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Blur"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Brightness"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Contrast"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Hue"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Overlay Transition"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Opacity Transition"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "multiply"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "screen"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "overlay"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "darken"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "lighten"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "color-dodge"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "saturation"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "color"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "luminosity"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Css Filters Transition"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Background Type"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Classic"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Gradient"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Background Color"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
msgid "Background Image"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Background Transition"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Enable Overlay"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Add custom value e.g. "
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Border Style"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "None"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dashed"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Solid"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dotted"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Double"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Groove"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Inset"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Outset"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Ridge"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Border Color"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Border Transition"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Box Shadow"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Shadow Color"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Horizontal Offset"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Vertical Offset"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Shadow Blur"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Shadow Spread"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Hover Shadow Color"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Shadow Transition"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Date"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Modified Date"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "ID"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Parent"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Random Order (Pro)"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Menu Order (Pro)"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Comment Count (Pro)"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Desc"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Asc"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Include"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Exclude"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Query"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
msgid "Source"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Order By"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Order"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Load More Settings"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Enable Load More Option?"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Text"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Previous Button Text"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Next Button Text"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Alignment"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Product ID"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
msgid "Product Title"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Product Slug"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Price"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Popular"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
msgid "Rating"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Ascending"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Descending"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Current Product"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Query Type"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Select Product"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Search Product"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "When using \"Current Product\" the block relies on the product context of the live site, which may not be accurately reflected in the editor or templates."
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Exclude Products"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Search Products"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Products Per Page"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Offset"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Include Products"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Product Categories"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Include Categories"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Search Product Categories"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Exclude Categories"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Product Tags"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Include Tags"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Search Product Tags"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Exclude Tags"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "100"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "200"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "300"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "400"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "500"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "600"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "700"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "800"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "900"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Lowercase"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Capitalize"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Uppercase"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Overline"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Line Through"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Underline"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Underline Oveline"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Italic"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Oblique"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Font Family"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Font Size"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Font Weight"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Font Style"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Text Transform"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Text Decoration"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Letter Spacing"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Line Height"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "FadeIn"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "FadeInDown"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "FadeInLeft"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "FadeInRight"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "FadeInUp"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "SlideInUp"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "SlideInDown"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "SlideInLeft"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "SlideInRight"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "ZoomIn"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "ZoomInDown"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "ZoomInLeft"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "ZoomInRight"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "ZoomInUp"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Bounce"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Flash"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Pulse"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "RubberBand"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Swing"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Tada"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Wobble"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Jello"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "BounceIn"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "BounceInDown"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "BounceInUp"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Flip"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "FlipInX"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "FlipInY"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "RotateIn"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Animation"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Select Animation"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Animation Speed"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Animation Delay"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Value"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Manage Options"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Add New Option"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Ocean Wave"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Asymmetric Triangle [Left]"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Abstract Paintbrush"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Asymmetric Curve (Inverted)"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Cloud (Pro)"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Mountain (Pro)"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Fire (Pro)"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Sports (Pro)"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Travel (Pro)"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Type"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Width"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Height"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Invert"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Bring to Front"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Visibility Options"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Z-Index"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
msgid "Overflow"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Margin & Padding"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Background"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Border & Shadow"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Responsive Control"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Hide on Desktop"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Hide on Tab"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hide on Mobile"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Custom CSS"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "add \"eb_selector\" prefix to target block elements"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Info"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Success"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Warning"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Danger"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Full"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Select Hover Effect"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Grow"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Shrink"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Pulse Grow"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Pulse Shrink"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Push"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Pop"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bounce In"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bounce Out"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Rotate"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Grow Rotate"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Float"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sink"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bob"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hang"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Skew"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Skew Forward"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Skew Backward"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Wobble Horizontal"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Wobble Vertical"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Wobble To Bottom Right"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Wobble To Top Right"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Wobble Top"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Wobble Bottom"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Wobble Skew"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Buzz"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Buzz Out"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Forward"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fade"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back Pulse"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sweep To Right"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sweep To Left"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sweep To Bottom"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sweep To Top"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bounce To Right"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bounce To Left"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bounce To Bottom"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bounce To Top"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Radial Out"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Radial In"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Rectangle In"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Rectangle Out"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Shutter In Horizontal"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Shutter Out Horizontal"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Shutter In Vertical"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Shutter Out Vertical"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Back"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Forward"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Down"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Up"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Spin"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Drop"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Fade"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Float Away"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Sink Away"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Grow"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Shrink"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Pulse"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Pulse Grow"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Pulse Shrink"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Push"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Pop"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Bounce"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Rotate"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Grow Rotate"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Float"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Sink"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Bob"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Hang"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Wobble Horizontal"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Wobble Vertical"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Buzz"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Buzz Out"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Curl Top Left"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Curl Top Right"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Curl Bottom Right"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Curl Bottom Left"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Click Me!"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
msgid "Add Text.."
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Settings"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
msgid "URL"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Invalid URL format. Please enter a valid URL."
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Open in New Tab"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Add nofollow"
msgstr ""

#: assets/admin/controls/controls.js:1
msgid "Add Download"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fixed Width"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Add icon"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Postion"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Size"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Gap"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Styles"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Typography"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Text Color"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Text Hover Color"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Border"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Padding"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hover Effect"
msgstr ""

#: assets/admin/controls/controls.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hover Transition"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Free Version: "
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Pro Version: "
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Upgrade To PRO & Enjoy Advanced Features!"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Unleash premium blocks and features from Essential Blocks to power up your Gutenberg website today."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Upgrade to pro"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Upgrade To PRO"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Welcome To Essential Blocks"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Demo pages"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Check out the detailed demo pages to learn about the super useful blocks at a glance."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Preview Demos"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "View Knowledgebase"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Go through the easy documentation to get familiar with Essential Blocks for Gutenberg."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Read Documentation"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Need Helps"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Get in touch with our dedicated support team whenever you face an issue."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Contact us"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Community"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Join Essential Blocks' social media community and interact with other users and developers."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Get Connected"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Show Your Love"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Show your love for Essential Blocks by rating us and helping us grow more."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Write a Review"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Get updates on our new blocks and features by checking out the changelog"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Version: 5.0.9"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "13/11/2024"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Fixed: Pricing Table | Icon control not showing for feature items"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Minor bug fixes and improvements"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "View All Change Log"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Global Control"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Use the toggle button to activate or deactivate all the blocks of Essential Blocks at once."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Disable All"
msgstr ""

#: assets/admin/dashboard/admin.js:1
#: assets/admin/editor/editor.js:1
msgid "Enable All"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "5000+"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid " Stunning Templates for WordPress"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Access Thousands Of Stunning, Ready Website Templates"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Save Your Design Anywhere With MyCloud Storage Space"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Add Team Members & Collaborate On Cloud With Templately WorkSpace"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Popular Templates"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Browse More Templates"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Save Changes"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Google Map API"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "More info"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "API Key"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Place your Google Map API key here"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Saving..."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Saved"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Something went wrong! Please try again."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Instagram Access Token"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "To get started please add an Instagram Access Token."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid " You can follow 1 to 3 "
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "steps"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "to generate token."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Once you have a token, please paste and save."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Access Token"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Place your instagram access token"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Provide your Email ID & unique Project Name to get access to Openverse  using API, these are required field"
msgstr ""

#: assets/admin/dashboard/admin.js:1
#: assets/admin/editor/editor.js:1
msgid "<EMAIL>"
msgstr ""

#: assets/admin/dashboard/admin.js:1
#: assets/admin/editor/editor.js:1
msgid "My amazing project"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Tablet Breakpoints"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Mobile Breakpoints"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Enable Google Fonts to get access to 1400+ exclusive fonts for all the fully customizable blocks of Essential Blocks."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Enable Font Awesome to get access to 2,000+ exclusive icon library and toolkit for all the fully customizable blocks of Essential Blocks."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "You have to retrieve API key to use Google Maps from Essential Blocks."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Instagram"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "To showcase your Instagram feed on your website, collect Instagram access tokens."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "To display your OpenSea NFT items, collections, wallets, etc. connect the API key here."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "To get unlimited access to Openverse images, provide your email & project name to generate API keys."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "API Documentation"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Optimization Options"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Updated"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Asset Regeneration"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Essential Blocks styles & scripts are saved in Uploads folder. This option will clear all those generated files."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Assets Regenerated!"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Regenerate Assets"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Responsive Breakpoints"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Adjust the “Responsive Breakpoint” settings to define the screen widths at which your site will adapt for optimal viewing on tablets and mobile devices."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Configure Breakpoints"
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "BetterDocs will help you to create & organize your documentation page in a beautiful way that will make your visitors find any help article easily."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "EmbedPress lets you embed videos, images, posts, audio, maps and upload PDF, DOC, PPT & all other types of content into your WordPress site."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Best Link Shortening tool to create, shorten and manage any URL to help you cross-promote your brands & products. Gather analytics reports, run successfully marketing campaigns easily & many more."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Best FOMO Social Proof Plugin to boost your sales conversion. Create stunning Sales Popup & Notification Bar With Elementor Support."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Easy solution for the job recruitment to attract, manage & hire right talent faster. The Best Talent Recruitment Suite which lets you manage jobs & career page in Elementor."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Best Content Marketing Tool For WordPress – Schedule, Organize, & Auto Share Blog Posts. Take a quick glance at your content planning with Schedule Calendar, Auto & Manual Scheduler and  more."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Powerful Elementor widgets library with 90+ advanced, fully customizable elements & extensions to enhance your website designing experience."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Better Payment streamlines transactions in Elementor, integrating PayPal, Stripe, advanced analytics, validation, and Elementor forms for the most secure & efficient payments."
msgstr ""

#: assets/admin/dashboard/admin.js:1
msgid "Templates"
msgstr ""

#: assets/admin/dashboard/admin.js:1
#: assets/admin/editor/editor.js:1
msgid "Integrations"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "image"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "none"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "General"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Default Open?"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Title Prefix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Title Suffix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Accordion Background Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Accordion Title Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Accordion Icon Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Toggle"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Material"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Dark"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Royal"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fill"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Border Box"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Padding Box"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Box"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Ghost"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Rounded"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Container"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Initial"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "No Repeat"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Repeat X"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Repeat Y"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Space"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Round"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Local"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "H1"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "H2"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "H3"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "H4"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "H5"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "H6"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Left"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Right"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Liner"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Ease"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Ease In"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Ease Out"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Ease In Out"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Center"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Lighter"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bold"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bolder"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "P"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Accordion Types"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title Level"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Toggle Speed"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Accordions Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable FAQ Schema"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Display Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Tab Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Expanded Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Background "
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Tab"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title Align "
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title Typography"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Prefix Suffix Spacing"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Text Typography"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Text / Icon Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Expanded Tab Colors"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "To see the changes, go to frontend page"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Content "
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Align"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Content Typography"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Accordion Item"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "accordion"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "toggle"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb essential"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Regular Price"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sale Price"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Inline"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Stacked"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Simple Product"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Grouped Product"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "External/Affiliate Product"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Variable Product"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Choose Product Type (Only for Preview)"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Note: This option is only to see how different types of products would look in your Single Product page."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Display Type"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Product Label"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Label"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Quantity"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Active"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add to Cart Button"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Disable"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Background Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "WooCommerce Add To Cart"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Product Add To Cart"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "add to cart"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb add to cart"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Line"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Top"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bottom"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Preset 1"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Preset 2"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Preset 3"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dynamic Title"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Preset Designs"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Display Subtilte"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Subtitle Level"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Separator"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Enable Link?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Link"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Subtitle"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Subtitle Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Subtitle Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Separator"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Separator Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Separator Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Separator Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Separator Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Separator Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Separator Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Separator Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "heading"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "heading block"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "heading box"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "dynamic title"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Middle"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Justify"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "No Effect"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Zoom In"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Zoom Out"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Slide"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Square"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Circle"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Octagon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Rhombus"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Triangle"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Style 1"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Style 2"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Site Logo"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Featured Image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Border won't work"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Caption"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Caption Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Auto Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Auto Fit Image?"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Image Fit Options"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Align"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Text Align"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Horizontal Align"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Vertical Align"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Seems like you haven't added a Featured Image for this post. Please make sure to add a Featured Image and try again."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Replace Image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Image block"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Single image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Featued image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "custom image"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Layout"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Orientation"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Horizontal"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Vertical"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Preset"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Allow to wrap to multiple lines"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dropdown Menus"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Open on Click"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Dropdown Menu Icon?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hamburger Menu"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Hamburger Menu"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Navigation"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Margin"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Divider Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Colors"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Active Colors"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Backgound"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Transition"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Dropdown Menu"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Min Width (PX)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Items"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Caret"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Caret Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Caret Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hover Caret Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Size(PX)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Close Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Menu"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB Navigation"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB Advanced Navigation"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Advanced Navigation Block"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "DIV"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "SPAN"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Tabs List"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Active Initially"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Custom ID"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Custom ID will be added as an anchor tag. For example, if you add ‘test’ as your custom ID, the link will become like the following: https://www.example.com/#test and it will open the respective tab directly."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Tab"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Tabs Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Tabs Layout"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fill Titles' Wrapper"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Responsive Layout"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Select Devices for Horizontal Layout"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Minimum Height Based on Tabs Heading Panel"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Enable Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Close All Tabs Initially"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Layout"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Tab Title"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title Min Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Active Background"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Active Border & Shadow"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Tab Titles' Wrapper"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Margin Padding"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Caret on Active Tab"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB Advanced Tabs"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Advanced Tabs Block"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "YouTube"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Vimeo"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Self Hosted"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sticky"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Lightbox"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Visible"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hidden"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Video Options"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Autoplay"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Mute"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Loop"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Controls"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Download"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Overlay"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Custom Play Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Type"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Additional Option"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sticky Position"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Visibility"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Play Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Video Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Play Icon Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Size (PX)"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Video"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Video block"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Advanced"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Home Page"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Prefix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Prefix Type"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Current Item Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Separators"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Prefix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Right Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Dummy Parent"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Dummy Title"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Breadcrumb"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB Breadcrumb"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Product Breadcrumb"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "breadcrumb"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb breadcrumb"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "button"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "link"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Basic"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Description"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Style"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Subtitle"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Button"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Sorting"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Link"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Use https or http"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title Tag"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Text Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Hover Text Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Hover Background"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add title..."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Description..."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "call to action"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "cta"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Column Width (%)"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Column Order"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Digit Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Label Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Preset 4"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Preset 5"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Preset 6"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Preset 7"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Preset 8 (Pro)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Preset 9 (Pro)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Start"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "End"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Space-Between"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Space-around"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Space-Evenly"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Countdown Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Evergreen Timer?"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Countdown Due Date"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Hours"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Minutes"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Recurring Countdown"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Specify how much time it will take to restart the countdown. If you set 0, the countdown will restart immediately."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Restart After(In Hours)"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Recurring Countdown End Date"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Days"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Custom Label For Days"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Hours"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Custom Label For Hours"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Minutes"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Custom Label For Minutes"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Seconds"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Custom Label For Seconds"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Boxes Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Container Max Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Space Between Boxs"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Contents Direction"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Contents Justify Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Contents Alignment"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Digits"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Labels"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Separator"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Position Top"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Position Right"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Individual Box Styling"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "countdown"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb counter"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb countdown"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Outline"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Buttons"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Width Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Buttons Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Buttons Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Connector"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Connector?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Connector Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Connector Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button One"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Two"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Text/ Icon Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "buttons"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "grouped button"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Button Group"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "dual"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "div"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "span"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "p"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Framed"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Feature"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title HTML Tag"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Shape"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Shape View"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Feature Item Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "List"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Space Between (PX)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Row Space(PX)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Spacing"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title Bottom Space"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Box Padding"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Connector Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Connector Width (PX)"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Feature list"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Feature box"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb feature"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Front"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Flip Left"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Flip Right"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Flip Up"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Flip Bottom"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Box"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Before"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "After"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Style 3"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Mint"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Tangelo"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Small"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Medium"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Large"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Extra Large"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Click"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Selected Side"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Flipbox Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Flip Mode"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Click mode only available in frontend."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Use Mouse Leave when click mode on"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Box Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Box Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Flipbox Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Transition Speed(millisecond)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Front Icon Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Select Front Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Front Image Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Flipbox Image"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Radius"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back Icon Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Select Back Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back Image Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Flipbox Content"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Title?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Front Title"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Content?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Front Content"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Back Title"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Back Content"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Link Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Note: Link settings will only work on back side."
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Link Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Flipbox Style"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Front Title Padding"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Front Side Background"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Items Padding"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back Title Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Back Title Padding"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back Content Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back Side Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Front Icon Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Front Image Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back Icon Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back Image Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Button Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Select Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Invalid HTML Tag"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb flipbox"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "essential"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "box"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Full Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fluent Form"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Select Form"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Labels"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Placeholder"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Error Message"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Input & Textarea"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "NORMAL"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "FOCUS"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Text Indent"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Input Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Input Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Textarea Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Textarea Height"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Placeholder"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Radio & Checkbox"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Custom Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "CHECKED"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Border Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Checkbox"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Border Radius"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Radio Buttons"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Item Spacing"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Section Break"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Custom HTML"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "HOVER"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Position"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Success Message"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Error Message"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB Fluent Form"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Label?"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Label Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Required?"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Advanced Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Default Value"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Leave empty if no default value."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Field Custom Name Attribute"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "This is for the name attributes which is used to submit form data, Name must be unique."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Custom Validation Message"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Requied Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Spacing (PX)"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB Form"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Form Block"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Contact Form"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Subscription Form"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Placeholder Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Icon?"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Invalid Email Validation Message"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Field"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Placeholder Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Validation"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fields Border Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Radio Fields"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Hidden Field?"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Rows"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Modern"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Form Title"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Use Title to recognize in Form Response"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Form Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Template"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Form Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Field Labels?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Field Icons?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Form Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Notification Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Email To"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Default is to Admin Email"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Use commas to separate emails"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Email Cc (Optional)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Recipient Email Address"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Email Bcc (Optional)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Email Subject"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Confirmation Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Confirmation Type"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Default Error Message"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Validation Error Message"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fields Width (%)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fields Gap (PX)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fields Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Input Fields"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Width (%)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Checkbox Fields"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Date Fields"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Selected Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Selected Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Vertical Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Horizontal Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Margin Top (PX)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Message"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Standard"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Silver"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Retro"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Night"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Aubergine"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Simple"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Colorful"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Complex"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Greyscale"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Light"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Monochrome"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "No Labels"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Two Tone"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Google Themes"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
#: assets/blocks/google-map/frontend.js:1
msgid "Snazzy Maps Themes"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Location"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Latitude"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Longitude"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Map Marker"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Red"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Blue"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Yellow"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Green"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Orange"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Google Map"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Please add your own Google Map API "
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Addresses"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Address"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Map Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Map Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Road Map"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Satellite View"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hybrid"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Terrain"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Map Zoom Level"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Map Height"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Marker Size"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Theme Source"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Snazzy Themes"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Info Card"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Description Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Marker 1"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb google map"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "map"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Icon View"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Primary Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Secondary Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "General Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Left Image"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Right Image"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Move on Hover"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Vertical Mode"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Before Label"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "After Label"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Label Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Swap Images"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "No Handle"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Slider Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Slider Line Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Line Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Upload Left Image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Upload Right Image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "image compare"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "comparison"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "compare"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Grid Layout"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Masonry Layout"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Adaptive"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Color Overlay"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "From Top"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "From Bottom"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "From Left"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "From Right"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Zoom In Out"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Layouts"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Overlay Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Caption on Hover"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Columns"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Gap (px)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Disable Light Box"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add custom link?"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Isotope"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Filter"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Enable Filter"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "ALL Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Default Selected Filter"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Filter Items"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Filter"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Gallery Items"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "URL is not valid"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Load More Button"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Loadmore"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Images Per Page"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Max Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Max Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Filter Item 1"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "All"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Edit gallery"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "images"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "photos"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb image gallery"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Infobox Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Layout Preset "
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Title"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Title Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Subtitle"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Subtitle Text"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Subtitle Tag"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable content"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Media"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Clickable Infobox"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "URL (use https:// at the beginning)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show button"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Alignments"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Media alignments"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Contents alignments"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Media"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Use Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Auto Image Height"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Title Padding"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Subtitle Padding"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Content Padding"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB infobox"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "info box"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "infobox block"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Most Recent"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Least Recent"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Card"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Outter"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Inner"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Feed Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sort By"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Number Of Images"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Square thumbnail"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Overlay Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Card Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "User info"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show profile image"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show profile name"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Custom Name"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show captions"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Link?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Open in new window?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Feed Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Caption"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Meta"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Header"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Loading feed"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "here."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Edit Profile Image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb Instagram Feed"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Apollo"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Bubba"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Chico"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dexter"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Duke"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Goliath"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Jazz"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Julia"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Layla"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Lexi"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Lily"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Marley"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Milo"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Ming"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Moses"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Oscar"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Ruby"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Roxy"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Romeo"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sadie"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Selena"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sarah"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Image alt tag"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Promo Effect"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Gradient Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Gradient Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Edit Image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "promo"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "message"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "interactive"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Grid"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "PX"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "EM"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "%"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Collections"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Collection Slug"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "cryptopunks"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Creator Username"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Layout Type"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Grid Preset"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "List Preset"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Items per row"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show NFT Image?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Button?"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Layout Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Columns Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Row Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Item Box Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Vertical Alignment"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hover Text Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hover Background Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "NFT"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "NFT Collection "
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Notice Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dismissible"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show After Dismiss"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB notice"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "notice"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "alert "
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dot"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Reverse"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Media Options"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Counter Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Starting Number"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Ending Number"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Animation Duration"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Number Prefix"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Number Suffix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Content layouts"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Thousand Separator"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Counter Title Level"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Number"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Number prefix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB number counter"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "counter"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "counter up"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Attribution"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Attribution Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "We couldn't find anything"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Try a different query or use one of the external sources to expand your search."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Select Media"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Search Key:"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Loading ..."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Load More"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "FILTER BY"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "LICENSES"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "LICENSE TYPE"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Image Type"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Extension"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Aspect ratio"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Select"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid " By "
msgstr ""

#: assets/admin/editor/editor.js:1
msgid " Is licensed under "
msgstr ""

#: assets/admin/editor/editor.js:1
msgid " ."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Generating ........"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Generate API"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Provide your Email ID & unique project Name to get access to Openverse  using API, these are required field"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Explore more than 600 million creative works"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Openverse block"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Openverse image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB Openverse block"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb openverse block"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "essential blocks"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Parallax Softness"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Custom Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Slider Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Slides Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Slides"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Slides Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Slide Border Radius"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Images"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Parallax"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb Parallax Slider"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Trigger"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Click"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Page Load"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "External Element"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Exit Intent"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Trigger Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Alignment "
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Page Load Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Delay(Seconds)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Use Cookies"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Cookie Expire(In days)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Leave blank if you want to delete cookie after browser closed."
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Identifier"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "You can also use class identifier such as .open-popup"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Exit Intent Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Exit Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Close Button"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Esc to Exit"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Close the modal box by pressing the Esc key"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Click to Exit"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Close the modal box by clicking anywhere outside the modal window"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Auto Exit"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Auto Exit Delay (Seconds)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Size & Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Middle Left"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Middle Center"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Middle Right"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Close Button"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Use close Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button/Icon Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Popup Design"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Container Padding"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "popup"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "modal"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Style 3 (List View)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Style 4 (Content Overlay)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Style 4"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Modern 1"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Modern 2"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Modern 3"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Carousel"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Arrows"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Equal Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dots"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Infinite"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Pause on Hover"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Slides to Show"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Autoplay Speed"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dot Preset"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Thumbnail Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Thumbnail Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Vertical Alignment"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Author Prefix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Example: by John Doe"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Published Date Prefix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Example: on 01/01/2023"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Thumbnail"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Overlay Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Excerpt"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Read More Button"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Header Meta Alignment"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Header Meta Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Footer Meta Alignment"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Footer Meta Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Author Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Date Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Common Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Common Divider Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Category Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Category Divider Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Tag Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Tag BG Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Tag Divider Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Dynamic Data Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Dynamic Data BG Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Author Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Common Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Category Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Tag Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Tag BG Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Meta Typography"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Arrow"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Left Arrow Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Right Arrow Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Arrow Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Arrow Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Active Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dots Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dots Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dots Position (PX)"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "posts"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "post carousel"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "posts block"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "carousel"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "slider"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "post slider"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Style 6 (Pro)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Style 7 (Pro)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Style 8 (Pro)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Layout Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Column Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Filter By Taxonomy"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Meta Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Common Meta Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Common Meta BG Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Category BG Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Read Time Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Load More Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Active Background Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Active Text Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Items Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Items Typography"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "post grid"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Meta Display"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Author"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Published Date"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Product SKU"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Meta Content"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Meta Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Label Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Meta Label"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Meta Value"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "meta"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "post meta"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "product meta"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Price Currency"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Currency Placement"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Price View"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Original Price"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Pricing Period"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Sale Pricing Period"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb price"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "px"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sub Title"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Period Separator"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Features"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Features"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Spacing"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Ribbon"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Featured"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Ribbon Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Featured Tag Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Price Table Box"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Pricing Top Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Subtitle Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Original Price Currency"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sale Price Currency"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Border Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Area Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Area Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "table"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb price table"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Active Tab Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Active Tab Background"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Ative Tab Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Ative Tab Background"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "WooCommerce Product Details"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Product summary"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Product review"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Additional information"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Gallery Position"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Thumbnails Space"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Disable Thumbnail Navigation Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Adaptive Height"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Display Size"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Thumbnails"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Thumbnails Border"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Active Thumbnails Border"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Navigations"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Arrow Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Arrow Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Arrow Background Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Arrow Background Hover Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "This block is supported in single product template"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "product image gallery"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "product image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "product image slider"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "product variation image"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Regular"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Sale"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Basline"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Sub"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Super"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Text Top"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Text Bottom"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Sale Price Position"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Prefix & Suffix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Suffix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Sufix Type"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Currency Sign"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "80.00"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "50.00"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "WooCommerce Product"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "WooCommerce Product Price"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "sale price"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Rated"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Block"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Block Reverse"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Inline Reverse"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Styles 1"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Styles 2"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Styles 3"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Styles 4"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Styles 5"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Stars Type"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Reviews Link"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Count Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Stars"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Stars Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Stars Size"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Stars Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "WooCommerce Product Rating"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb rating"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Line Rainbow"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Circle Fill"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Half Circle"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Half Circle Fill"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Postfix"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Inline"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Counter Value"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Stripe"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Stripe Animation"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Suffix"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Stroke Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fill Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Fill Gradient"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Stroke Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Counter"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "progress"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "bar"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Boxed"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fit To Screen"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Min Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Row settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Max Width (px)"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Columns Number"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Minimum height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Columns Vertical Align"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB row"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "row"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Options"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Shape"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Divider"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB shape divider"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Only"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image with Content"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Overlay"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Bottom"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Right"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Left"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Slider Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Adaptive Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Vertical Slide"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Arrow Prev Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Arrow Next Icon"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show Lightbox"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Content Tag"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Button URL"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Second Button"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Second Button Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Second Button URL"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Second Button"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Dots Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Circular"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Share Buttons"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Title"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Floating"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Social Media"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Icon Background Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icons Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Floating Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Floating Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Rows Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Hover Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Hover Effect"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Facebook"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Twitter"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Linkedin"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "WhatsApp"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "social share"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "icons"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Social Profiles"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show link in new tab"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Social Profile"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icons Devider"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Position From Right"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Icons Shadow"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Blur Radius"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "social"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Scroll to the top of page"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Scroll to the TOC"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Title"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Supported Heading Tags"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Display Underline"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Collapsible"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Collapsed initially"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sticky contents"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Item Collapsed"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Highlight On Scroll"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Copy Link"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Offset Top"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable List Style"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "List Style"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Unordered"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Ordered"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Smooth Scroll"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Scroll To Top"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Scroll Target"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Arrow Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Arrow Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Arrow Background"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Exclude Headings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Title Separator"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Seperator Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Indent"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Gap"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Separator Size"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Item Background Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content min-height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Visible on frontend only"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Top Space"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add a header to begin generating the table of contents"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "table of content"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Table of content"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "TOC"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb table of contents"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Limit"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Select 0 to show all terms."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Additional"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Sufix"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enter character(s) used to separate terms."
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Taxonomy Style"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Display"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Untitled"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "taxonomy block"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "category"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "tags"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Presets"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Reverse Layout"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Hover Alignment"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Enable Designation"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Enable Description"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Enable Social Profiles"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Designation"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Enable Content Separator"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Enable Social Separator"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Container width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Max Width"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Overlay Contents"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Use Background Gradient"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Avatar"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Enable Background before Image"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Name"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Job Title"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Icons Border"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Container Background "
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Container Margin Padding "
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Separator"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Social Separator"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "team"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "member"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "One"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Two"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Three"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Layout Settings"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "User Info Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "User Info Align"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Description Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Description Align"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "User Name Align"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Quote Horizontal Align"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Quote Vertical Position"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Rating Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Avatar Inline"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Position"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Setting"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Round Avatar"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Username Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Company Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Quote Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Rating Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Quote Size"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Rating Size"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "testimonial"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "about"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb quote"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Span"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Dynamic Content"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Dynamic Excerpt"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Tag"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Column Count"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Text Styles"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Column Style"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Column Min Width"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Column Rule Style"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Column Rule Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Column Rule Width"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "paragraph"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "paragraph column"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Primary"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Secondary"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Rectangle"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Diamond"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Initial Content"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Switch Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Switch Size"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Height"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Button Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sepetator Type"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Heading Space"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Label Space"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Primary Text"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Secondary Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Switch Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Gradient Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Background Gradient"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Controller Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Gradient Controller"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Controller Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Controller Gradient"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Shadow"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid " Spread"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "First"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Second"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb Toggle Content"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Prefix Text"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Add prefix text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Typed Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Add Typed Item"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Suffix Text"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Add suffix text"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Smart Backspace"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Cursor"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fade Out"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Type Speed"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Start Delay"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back Speed"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Back Delay"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Fade Delay"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Prefix Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Typed Text Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Suffix Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "animated Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "eb typing"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Rating"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Rating Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Price"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Show Sale Badge"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Show View Button"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Cart Text"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Use Custom Cart Button Text"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "External Product"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Default Product"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sale Badge"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sale Text"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Products"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Alignment"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Content Background Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Background Overlay Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Wrapper Background"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Overlay Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Image Space"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Product Description"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Description length"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Price Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sale Price Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "View Button"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sale Badge Style"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sale Text Color"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Sale Text Background Color"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "No product found"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Form Max Width"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "EB WPForms"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Wide"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Wrapper Width"
msgstr ""

#: assets/admin/editor/editor.js:1
#: assets/admin/global-styles/global-styles.js:1
msgid "Use Width In Wrapper"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "Use Custom Height"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "wrapper"
msgstr ""

#: assets/admin/editor/editor.js:1
msgid "container"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Tab Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Wrapper Margin & Padding"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Wrapper Background "
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Display Subtitle"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Title Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Subtitle Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Subtitle Typography"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Wrapper Background"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Image Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Tab Title Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Tab Titles' Wrapper Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Caret Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Navigation Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Dropdown Menu Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Caret Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Hamburger Menu Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Wrapper Margin"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Description Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Button One Text"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Button Two Text"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Buttons Settings"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Connector Settings"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Buttons Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Button One Background"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Button Two Background"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Button One Hover"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Button Two Hover"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Button One Border"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Button Two Border"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Connector Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Flibbox Side"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Wrapper Border & Shadow"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Labels Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Input & Textarea Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Placeholder Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Radio & Checkbox Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Submit Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Success Message Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Error Message Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Wrapper Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "General Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Media Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Enable"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Show content"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Text color"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Hover text color"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "More Effects"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Button Hover Effect"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Header Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "General "
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Show Current Owner?"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Show Creator?"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Show Price?"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Show Last Sale?"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Image Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Creator/Owner Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Show Name?"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Show Image?"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Creator Label"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Owner Label"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Link Color"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Price Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Text Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Wrapper Margin Padding"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid " WrapperBackground"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Color Controls"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Close Button Settings"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Columns Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Thumbnail Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Excerpt Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Price Table Box Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Header Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Features Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Icon Settings Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Counter Styles"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Arrow Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Dot Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Icons Border & Box-Shadow Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Icons Shadow Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Visible Headers"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Sticky settings"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Title Settings"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Title separator settings"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Avatar Horizontal Alignments"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Content Separator Alignment"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Username"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Company"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Quote"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Switch Background Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Prefix Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Typed Text Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Suffix Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Products Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "List Layout"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Product Title Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Product Description Style(List Layout)"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Product Price Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Product Rating Style"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Wrapper Background Color"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Wrapper Border"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Height & Zoom"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "EB Global Controls"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Global Color"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Global Colors"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Global Typography"
msgstr ""

#: assets/admin/global-styles/global-styles.js:1
msgid "Block Defaults"
msgstr ""

#: assets/admin/templately/templately.js:1
msgid "Pattern Library"
msgstr ""

#: assets/admin/templately/templately.js:1
msgid "I want access to FREE Templates"
msgstr ""

#: assets/admin/templately/templately.js:1
msgid "Something went wrong!"
msgstr ""

#: assets/blocks/accordion-item/block.json
msgctxt "block title"
msgid "Accordion Item"
msgstr ""

#: assets/blocks/accordion/block.json
msgctxt "block title"
msgid "Accordion"
msgstr ""

#: assets/blocks/accordion/block.json
msgctxt "block description"
msgid "Display your FAQs & improve user experience with Accordion/Toggle block"
msgstr ""

#: assets/blocks/add-to-cart/block.json
msgctxt "block title"
msgid "Add To Cart"
msgstr ""

#: assets/blocks/add-to-cart/block.json
msgctxt "block description"
msgid "Display the 'add to cart' option for all your WooCommerce products to ensure easy navigation for customers."
msgstr ""

#: assets/blocks/advanced-heading/block.json
msgctxt "block title"
msgid "Advanced Heading"
msgstr ""

#: assets/blocks/advanced-heading/block.json
msgctxt "block description"
msgid "Create advanced heading with title, subtitle and separator controls"
msgstr ""

#: assets/blocks/advanced-image/block.json
msgctxt "block title"
msgid "Advanced Image"
msgstr ""

#: assets/blocks/advanced-image/block.json
msgctxt "block description"
msgid "Customise images in Gutenberg to fit your exact needs."
msgstr ""

#: assets/blocks/advanced-navigation/block.json
msgctxt "block title"
msgid "Advanced Navigation"
msgstr ""

#: assets/blocks/advanced-navigation/block.json
msgctxt "block description"
msgid "Display pages & posts with easy navigation in your website with stunning & organised appearance."
msgstr ""

#: assets/blocks/advanced-tabs/block.json
msgctxt "block title"
msgid "Advanced Tabs"
msgstr ""

#: assets/blocks/advanced-tabs/block.json
msgctxt "block description"
msgid "Display nested tabs to display key information on an instance in an interactive manner."
msgstr ""

#: assets/blocks/advanced-video/block.json
msgctxt "block title"
msgid "Advanced Video"
msgstr ""

#: assets/blocks/advanced-video/block.json
msgctxt "block description"
msgid "Portray any attractive video content on your website and grow engagement"
msgstr ""

#: assets/blocks/breadcrumbs/block.json
msgctxt "block title"
msgid "Breadcrumbs"
msgstr ""

#: assets/blocks/breadcrumbs/block.json
msgctxt "block description"
msgid "Make smooth navigation for your posts or pages by adding Breadcrumb"
msgstr ""

#: assets/blocks/button/block.json
msgctxt "block title"
msgid "Button"
msgstr ""

#: assets/blocks/button/block.json
msgctxt "block description"
msgid "Create a stunning button through the button block. You can make it look outstanding by adding exclusive button styles & effects and add links to redirect your visitors to a specific page."
msgstr ""

#: assets/blocks/call-to-action/block.json
msgctxt "block title"
msgid "Call To Action"
msgstr ""

#: assets/blocks/call-to-action/block.json
msgctxt "block description"
msgid "Call to action lets you create an interactive CTA for the website to draw visitor's attention on the spot."
msgstr ""

#: assets/blocks/column/block.json
msgctxt "block title"
msgid "Column"
msgstr ""

#: assets/blocks/column/block.json
msgctxt "block description"
msgid "A single column within a Row block."
msgstr ""

#: assets/blocks/countdown/block.json
msgctxt "block title"
msgid "Countdown"
msgstr ""

#: assets/blocks/countdown/block.json
msgctxt "block description"
msgid "Highlight upcoming events with countdown timer"
msgstr ""

#: assets/blocks/dual-button/block.json
msgctxt "block title"
msgid "Dual Button"
msgstr ""

#: assets/blocks/dual-button/block.json
msgctxt "block description"
msgid "Create two buttons to be stacked together"
msgstr ""

#: assets/blocks/feature-list/block.json
msgctxt "block title"
msgid "Feature List"
msgstr ""

#: assets/blocks/feature-list/block.json
msgctxt "block description"
msgid "Make your website interactive with feature list."
msgstr ""

#: assets/blocks/flipbox/block.json
msgctxt "block title"
msgid "Flipbox"
msgstr ""

#: assets/blocks/flipbox/block.json
msgctxt "block description"
msgid "Deliver your content beautifully to grab site visitor's attention."
msgstr ""

#: assets/blocks/fluent-forms/block.json
msgctxt "block title"
msgid "Fluent Forms"
msgstr ""

#: assets/blocks/fluent-forms/block.json
msgctxt "block description"
msgid "Design your Forms container, fields and choose preferred form layout to style Fluent Forms"
msgstr ""

#: assets/blocks/form-checkbox-field/block.json
msgctxt "block title"
msgid "Checkbox Field"
msgstr ""

#: assets/blocks/form-checkbox-field/block.json
msgctxt "block description"
msgid "Form Block Checkbox Field"
msgstr ""

#: assets/blocks/form-email-field/block.json
msgctxt "block title"
msgid "Email Field"
msgstr ""

#: assets/blocks/form-email-field/block.json
#: assets/blocks/form-text-field/block.json
msgctxt "block description"
msgid "Form Block Text Field"
msgstr ""

#: assets/blocks/form-number-field/block.json
msgctxt "block title"
msgid "Number Field"
msgstr ""

#: assets/blocks/form-number-field/block.json
msgctxt "block description"
msgid "Form Block Number Field"
msgstr ""

#: assets/blocks/form-radio-field/block.json
msgctxt "block title"
msgid "Radio Field"
msgstr ""

#: assets/blocks/form-radio-field/block.json
msgctxt "block description"
msgid "Form Block Radio Field"
msgstr ""

#: assets/blocks/form-select-field/block.json
msgctxt "block title"
msgid "Select Field"
msgstr ""

#: assets/blocks/form-select-field/block.json
msgctxt "block description"
msgid "Form Block Select Field"
msgstr ""

#: assets/blocks/form-text-field/block.json
msgctxt "block title"
msgid "Text Field"
msgstr ""

#: assets/blocks/form-textarea-field/block.json
msgctxt "block title"
msgid "Textarea Field"
msgstr ""

#: assets/blocks/form-textarea-field/block.json
msgctxt "block description"
msgid "Form Block Textarea Field"
msgstr ""

#: assets/blocks/form/block.json
msgctxt "block title"
msgid "Form"
msgstr ""

#: assets/blocks/form/block.json
msgctxt "block description"
msgid "Create Beautiful and Professional Contact, Subscription or any other custom Forms easily with a few clicks."
msgstr ""

#: assets/blocks/google-map/block.json
msgctxt "block title"
msgid "Google Maps"
msgstr ""

#: assets/blocks/google-map/block.json
msgctxt "block description"
msgid "Display locations from Google Maps seamlessly and style how they look to match your site design."
msgstr ""

#: assets/blocks/icon/block.json
msgctxt "block title"
msgid "Icon Picker"
msgstr ""

#: assets/blocks/icon/block.json
msgctxt "block description"
msgid "Icon Picker Block lets you choose any Font Awesome icon to customize your design as per your preference."
msgstr ""

#: assets/blocks/image-comparison/block.json
msgctxt "block title"
msgid "Image Comparison"
msgstr ""

#: assets/blocks/image-comparison/block.json
msgctxt "block description"
msgid "Let the visitors compare images & make your website interactive."
msgstr ""

#: assets/blocks/image-gallery/block.json
msgctxt "block title"
msgid "Image Gallery"
msgstr ""

#: assets/blocks/image-gallery/block.json
msgctxt "block description"
msgid "Impress your audience with high-resolution images"
msgstr ""

#: assets/blocks/infobox/block.json
msgctxt "block title"
msgid "Infobox"
msgstr ""

#: assets/blocks/infobox/block.json
msgctxt "block description"
msgid "Deliver your content beautifully to grab attention with an animated Infobox block."
msgstr ""

#: assets/blocks/instagram-feed/block.json
msgctxt "block title"
msgid "Instagram Feed"
msgstr ""

#: assets/blocks/instagram-feed/block.json
msgctxt "block description"
msgid "Showcase instagram posts for your web visitors."
msgstr ""

#: assets/blocks/interactive-promo/block.json
msgctxt "block title"
msgid "Interactive Promo"
msgstr ""

#: assets/blocks/interactive-promo/block.json
msgctxt "block description"
msgid "EB Interactive Promo lets you decorate your website outlook with its amazing Promo effects"
msgstr ""

#: assets/blocks/nft-gallery/block.json
msgctxt "block title"
msgid "NFT Gallery"
msgstr ""

#: assets/blocks/nft-gallery/block.json
msgctxt "block description"
msgid "Display your NFT items & collections in a stunning gallery view without any coding"
msgstr ""

#: assets/blocks/notice/block.json
msgctxt "block title"
msgid "Notice"
msgstr ""

#: assets/blocks/notice/block.json
msgctxt "block description"
msgid "Put spotlight on news, announcements & let the visitors find it easily"
msgstr ""

#: assets/blocks/number-counter/block.json
msgctxt "block title"
msgid "Number Counter"
msgstr ""

#: assets/blocks/number-counter/block.json
msgctxt "block description"
msgid "Put spotlight in important data using Counter block for Gutenberg. Customize the designs by adding proper Animation effects with flexibility and many more!"
msgstr ""

#: assets/blocks/openverse/block.json
msgctxt "block title"
msgid "Openverse"
msgstr ""

#: assets/blocks/openverse/block.json
msgctxt "block description"
msgid "Easily search & use royalty free images, stock photos, CC-licensed images from Openverse for your website"
msgstr ""

#: assets/blocks/parallax-slider/block.json
msgctxt "block title"
msgid "Parallax Slider"
msgstr ""

#: assets/blocks/parallax-slider/block.json
msgctxt "block description"
msgid "Create a captivating visual experience & impress your audience"
msgstr ""

#: assets/blocks/popup/block.json
msgctxt "block title"
msgid "Popup"
msgstr ""

#: assets/blocks/popup/block.json
msgctxt "block description"
msgid "Showcase your videos, images or other content with popup & trigger actions."
msgstr ""

#: assets/blocks/post-carousel/block.json
msgctxt "block title"
msgid "Post Carousel"
msgstr ""

#: assets/blocks/post-carousel/block.json
msgctxt "block description"
msgid "Showcase your posts & blogs in a stylish way on your website and make it more engaging"
msgstr ""

#: assets/blocks/post-grid/block.json
msgctxt "block title"
msgid "Post Grid"
msgstr ""

#: assets/blocks/post-grid/block.json
msgctxt "block description"
msgid "Create a stunning and interactive visualization for your blogs in a grid layout"
msgstr ""

#: assets/blocks/post-meta/block.json
msgctxt "block title"
msgid "Post Meta"
msgstr ""

#: assets/blocks/post-meta/block.json
msgctxt "block description"
msgid "Attractively showcase your current post metadata like author, date, excerpt & more."
msgstr ""

#: assets/blocks/price/block.json
msgctxt "block title"
msgid "Price"
msgstr ""

#: assets/blocks/price/block.json
msgctxt "block description"
msgid "Price Block lets you add both the regular price and a discounted price for your pricing package."
msgstr ""

#: assets/blocks/pricing-table/block.json
msgctxt "block title"
msgid "Pricing Table"
msgstr ""

#: assets/blocks/pricing-table/block.json
msgctxt "block description"
msgid "The pricing table block will let you create an effective product pricing table with perfect styling to get more sales from your prospective buyers."
msgstr ""

#: assets/blocks/product-details/block.json
msgctxt "block title"
msgid "Product Details"
msgstr ""

#: assets/blocks/product-details/block.json
msgctxt "block description"
msgid "Display WooCommerce product details with an organized layout that helps grab customers' attention"
msgstr ""

#: assets/blocks/product-images/block.json
msgctxt "block title"
msgid "Product Images"
msgstr ""

#: assets/blocks/product-images/block.json
msgctxt "block description"
msgid "Showcase eye-catching WooCommerce product images that draw in customers and are easy to customize."
msgstr ""

#: assets/blocks/product-price/block.json
msgctxt "block title"
msgid "Product Price"
msgstr ""

#: assets/blocks/product-price/block.json
msgctxt "block description"
msgid "Display price of the WooCommerce product and style it using custom colors and typography."
msgstr ""

#: assets/blocks/product-rating/block.json
msgctxt "block title"
msgid "Product Rating"
msgstr ""

#: assets/blocks/product-rating/block.json
msgctxt "block description"
msgid "Display WooCommerce product ratings to build credibility among visitors and customers."
msgstr ""

#: assets/blocks/progress-bar/block.json
msgctxt "block title"
msgid "Progress Bar"
msgstr ""

#: assets/blocks/progress-bar/block.json
msgctxt "block description"
msgid "Make your website interactive with stunning progress bars."
msgstr ""

#: assets/blocks/row/block.json
msgctxt "block title"
msgid "Row"
msgstr ""

#: assets/blocks/row/block.json
msgctxt "block description"
msgid "Create complex Row layouts with plenty of styling controls & responsive options"
msgstr ""

#: assets/blocks/shape-divider/block.json
msgctxt "block title"
msgid "Shape Divider"
msgstr ""

#: assets/blocks/shape-divider/block.json
msgctxt "block description"
msgid "Make your website designs stand out by showcasing different sections with stunning shape dividers"
msgstr ""

#: assets/blocks/slider/block.json
msgctxt "block title"
msgid "Slider"
msgstr ""

#: assets/blocks/slider/block.json
msgctxt "block description"
msgid "Display multiple images and content in beautiful slider & reduce page scroll"
msgstr ""

#: assets/blocks/social-share/block.json
msgctxt "block title"
msgid "Social Share"
msgstr ""

#: assets/blocks/social-share/block.json
msgctxt "block description"
msgid "Share your posts & pages instantly on popular social platforms in one click from your website."
msgstr ""

#: assets/blocks/social/block.json
msgctxt "block title"
msgid "Social Icons"
msgstr ""

#: assets/blocks/social/block.json
msgctxt "block description"
msgid "Add icon links to your social media profiles and grow your audience with animated social icons"
msgstr ""

#: assets/blocks/tab/block.json
msgctxt "block title"
msgid "Tab"
msgstr ""

#: assets/blocks/table-of-contents/block.json
msgctxt "block title"
msgid "Table Of Contents"
msgstr ""

#: assets/blocks/table-of-contents/block.json
msgctxt "block description"
msgid "Insert Table of Contents on your posts/pages and enhance user experience on your WordPress website"
msgstr ""

#: assets/blocks/taxonomy/block.json
msgctxt "block title"
msgid "Taxonomy"
msgstr ""

#: assets/blocks/taxonomy/block.json
msgctxt "block description"
msgid "Display and organize content by categories, tags, product SKUs, etc. to make it easy for users to navigate"
msgstr ""

#: assets/blocks/team-member/block.json
msgctxt "block title"
msgid "Team Members"
msgstr ""

#: assets/blocks/team-member/block.json
msgctxt "block description"
msgid "Present your team members beautifully & gain instant credibility"
msgstr ""

#: assets/blocks/testimonial/block.json
msgctxt "block title"
msgid "Testimonial"
msgstr ""

#: assets/blocks/testimonial/block.json
msgctxt "block description"
msgid "Display testimonials & gain instant credibility"
msgstr ""

#: assets/blocks/text/block.json
msgctxt "block title"
msgid "Text"
msgstr ""

#: assets/blocks/text/block.json
msgctxt "block description"
msgid "Add and customize text content on your website that catches the attention of the visitors."
msgstr ""

#: assets/blocks/toggle-content/block.json
msgctxt "block title"
msgid "Toggle Content"
msgstr ""

#: assets/blocks/toggle-content/block.json
msgctxt "block description"
msgid "Toggle Contents or blocks with a beautiful switcher."
msgstr ""

#: assets/blocks/typing-text/block.json
msgctxt "block title"
msgid "Typing Text"
msgstr ""

#: assets/blocks/typing-text/block.json
msgctxt "block description"
msgid "Make your website interactive with typing text animation."
msgstr ""

#: assets/blocks/woo-product-grid/block.json
msgctxt "block title"
msgid "Woo Product Grid"
msgstr ""

#: assets/blocks/woo-product-grid/block.json
msgctxt "block description"
msgid "Display WooCommerce products in a stunning, organized layout & help customers with EB Woo Product Grid."
msgstr ""

#: assets/blocks/wpforms/block.json
msgctxt "block title"
msgid "WPForms"
msgstr ""

#: assets/blocks/wpforms/block.json
msgctxt "block description"
msgid "Design stunning WPForms in minutes with plenty of controls & styling options"
msgstr ""

#: assets/blocks/wrapper/block.json
msgctxt "block title"
msgid "Wrapper"
msgstr ""

#: assets/blocks/wrapper/block.json
msgctxt "block description"
msgid "This is a wrapper block for other blocks within it."
msgstr ""
