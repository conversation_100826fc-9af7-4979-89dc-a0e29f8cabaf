/**
 * The following styles get applied both on the front of your site
 * and in the editor.
 *
 * Replace them with your own styles or remove the file completely.
 */

.eb-advanced-navigation-wrapper {

	&.preset-3 {
		.wp-block-navigation__container>.wp-block-navigation-item>*:not(.wp-block-navigation__submenu-container) {
			z-index: 1;
		}

		.wp-block-navigation__container>.wp-block-navigation-item.current-menu-item,
		.wp-block-navigation__container>.wp-block-navigation-item:hover {
			position: relative;

			&::before {
				content: "";
				position: absolute;
				right: 0;
				top: 50%;
				width: 100%;
				height: 70%;
				// background-color: #2673ff;
				z-index: 0;
				transform: translateY(-50%);
			}
		}
	}

	&.preset-4 {
		.wp-block-navigation__responsive-container:not(.is-menu-open) {
			.wp-block-navigation__container>.wp-block-navigation-item:not(:last-child) {
				position: relative;

				&::after {
					content: "";
					position: absolute;
					left: auto;
					right: 0;
					top: 50%;
					width: 1px;
					height: 10px;
					// background-color: #d9d9d9;
					transform: translateY(-50%);
				}
			}

			.wp-block-navigation__submenu-container .wp-block-navigation-item {
				border-bottom: 1px solid #e7f0ff;
			}
		}
	}

	&:not(.v2) {
		.wp-block-navigation:not(ul.wp-block-navigation) {
			box-shadow: 0px 22px 35px rgba(98, 103, 145, 0.15);
		}
	}

	.wp-block-navigation:not(ul.wp-block-navigation) {
		gap: 0;
	}

	.wp-block-navigation__responsive-container:not(.is-menu-open) .has-child .wp-block-navigation-submenu__toggle[aria-expanded=true]~.wp-block-navigation__submenu-container,
	.wp-block-navigation__responsive-container:not(.is-menu-open) .has-child:not(.open-on-click):hover>.wp-block-navigation__submenu-container,
	.wp-block-navigation__responsive-container:not(.is-menu-open) .has-child:not(.open-on-click):not(.open-on-hover-click):focus-within>.wp-block-navigation__submenu-container {
		display: block;
	}

	.wp-block-navigation-item {
		&:focus {
			outline: none;
		}
	}

	.wp-block-navigation-item__content {
		line-height: 12px;
		text-decoration: none;

		&:hover {
			text-decoration: none !important;
		}

		// padding: 20px 10px;
	}

	// dropdown menu
	.wp-block-navigation__submenu-container {
		z-index: 99;

		.wp-block-navigation-item:first-child {
			border-top-left-radius: inherit;
			border-top-right-radius: inherit;
		}

		.wp-block-navigation-item:last-child {
			border-bottom-left-radius: inherit;
			border-bottom-right-radius: inherit;
		}
	}

	&.is-vertical {
		&.items-justified-center {
			.wp-block-navigation__responsive-container.is-menu-open {
				.wp-block-navigation__submenu-container {
					left: 0 !important;
					transform: none !important;
				}
			}
		}

		&.items-justified-center .wp-block-navigation__container>.wp-block-navigation-item {
			justify-content: center;

			.wp-block-navigation__submenu-container {
				left: 50%;
				transform: translateX(-50%);

				.wp-block-navigation__submenu-container {
					left: 100%;
					transform: none;
				}
			}
		}

		&.items-justified-right .wp-block-navigation__container>.wp-block-navigation-item {
			justify-content: right;
		}

		&.items-justified-left .wp-block-navigation__container>.wp-block-navigation-item {
			justify-content: left;
		}

		// &.vertical-preset-1 {
		// 	.wp-block-navigation-item:not(:last-child) {
		// 		border-bottom: 1px solid red;
		// 	}
		// }

		&.vertical-preset-2 {
			.wp-block-navigation {
				padding: 20px;
			}

			// .wp-block-navigation-item:hover,
			// .current-menu-item {
			// 	background-color: #f5f9ff;

			// 	.wp-block-navigation-item__content {
			// 		color: #2673ff;
			// 	}
			// }
		}

		.wp-block-navigation__container {
			width: 100%;

			>.wp-block-navigation-item {
				// width: 100%;
				width: -webkit-fill-available;
			}
		}
	}

	&.remove-dropdown-icon .wp-block-navigation__submenu-icon {
		display: none;
	}

	.wp-block-navigation__toggle_button_label {
		font-size: inherit;
		font-weight: inherit;
	}

	// hamburger
	.wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__container {
		width: 100%;
	}

	// open
	.wp-block-navigation__responsive-container-open {
		opacity: 1;
	}

	// close icon
	&.close-icon-left .wp-block-navigation__responsive-container-close {
		left: 0;
		right: auto;
	}

	&.close-icon-center .wp-block-navigation__responsive-container-close {
		left: 50%;
		right: auto;
		transform: translateX(-50%);
	}

	.wp-block-navigation__responsive-container:not(.is-menu-open.is-menu-open) {
		border-radius: inherit;
	}


	// responsive
	.eb-menu-indicator {
		position: absolute;
		top: 0;
		right: 0;
		width: 25px;
		height: 25px;
		display: block;
		background-color: #fff;
		border: 1px solid #ddd;
		box-sizing: content-box;
		cursor: pointer;
		display: none;

		&.eb-menu-indicator-open:before {
			height: 0;
		}

		&::before {
			content: "";
			position: absolute;
			top: calc(50% - 4px);
			right: calc(50% - 1px);
			width: 2px;
			height: 8px;
			background: #ddd;
			transition: all .25s ease;
		}

		&::after {
			content: "";
			position: absolute;
			top: calc(50% - 1px);
			right: calc(50% - 4px);
			width: 8px;
			height: 2px;
			background: #ddd;
		}
	}

	.wp-block-navigation.is-responsive ul.wp-block-navigation-submenu {
		display: none;
		padding: 0 0 0 30px;
	}
}
