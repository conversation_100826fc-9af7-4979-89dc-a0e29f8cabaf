/**
 * The following styles get applied both on the front of your site
 * and in the editor.
 */

 @import '../../../controls/src/components/Image/imageComponent.scss';

.eb-advanced-image-wrapper {
	margin: 0;

	&.img-style-triangle .eb-image-wrapper,
	&.img-style-triangle .image-wrapper {
		clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
	}

	&.img-style-rhombus .eb-image-wrapper,
	&.img-style-rhombus .image-wrapper {
		clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
	}

	&.img-style-octagon .eb-image-wrapper,
	&.img-style-octagon .image-wrapper {
		clip-path: polygon(30% 0%,
				70% 0%,
				100% 30%,
				100% 70%,
				70% 100%,
				30% 100%,
				0% 70%,
				0% 30%);
	}

	&.caption-style-1 {
		overflow: visible;

		&.caption-horizontal-left {
			&.caption-vertical-top figcaption {
				left: 0px;
				top: 0px;
				bottom: auto;
			}

			&.caption-vertical-middle figcaption {
				left: 0px;
				top: 50%;
				transform: translateY(-50%);
			}

			&.caption-vertical-bottom figcaption {
				left: 0px;
				bottom: 0px;
			}
		}

		&.caption-horizontal-center {
			&.caption-vertical-top figcaption {
				left: 50%;
				top: 0px;
				transform: translateX(-50%);
				bottom: auto;
			}

			&.caption-vertical-middle figcaption {
				left: 50%;
				top: 50%;
				bottom: auto !important;
				transform: translate(-50%, -50%);
			}

			&.caption-vertical-bottom figcaption {
				left: 50%;
				bottom: 0px;
				transform: translateX(-50%);
			}
		}

		&.caption-horizontal-right {
			&.caption-vertical-top figcaption {
				right: 0px;
				top: 0px;
				bottom: auto;
			}

			&.caption-vertical-middle figcaption {
				right: 0px;
				top: 50%;
				transform: translateY(-50%);
			}

			&.caption-vertical-bottom figcaption {
				right: 0px;
				bottom: 0px;
			}
		}

		figcaption {
			background: rgba(0, 0, 0, 0.8);
			bottom: 0;
			box-sizing: border-box;
			position: absolute;
			width: 100%;
			transition: max-height 0.3s ease-out;
			overflow: hidden;
		}
	}

	&.caption-style-2 {
		display: flex;
		flex-direction: column;

		&.top {
			flex-direction: column-reverse;
		}
	}

	.eb-image-wrapper, .image-wrapper {
		overflow: hidden;
		position: relative;
		max-width: 100%;
	}

	img {
		display: block;
		width: 100%;
		max-width: 100%;
		max-height: 100%;
		height: 100%;
		margin: 0  auto !important;
	}

	.eb-image-caption-wrap {
		line-height: 1;
		padding: 15px;
	}

	/* Zoom In #2 */
	&.zoom-in {
		img {
			transform: scale(1);
		}

		.eb-image-wrapper:hover img, .image-wrapper:hover img  {
			transform: scale(1.3);
		}
	}

	&.zoom-out {
		img {
			transform: scale(1.5);
		}

		.eb-image-wrapper:hover img, .image-wrapper:hover img {
			transform: scale(1);
		}
	}

	&.slide {
		img {
			margin-left: 30px !important;
			transform: scale(1.3);
			transition: 0.3s ease-in-out !important;
		}

		.eb-image-wrapper:hover img, .image-wrapper:hover img {
			margin-left: 0 !important;
		}
	}

	&.blur {
		img {
			filter: blur(3px);
		}

		.eb-image-wrapper:hover img, .image-wrapper:hover img {
			filter: blur(0);
		}
	}

	.eb-advimg-link {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		z-index: 999;
	}
}

.wp-block-essential-blocks-advanced-image .eb-loading {
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0 auto;
}

.feature-image-placeholder {
	width: 100%;
	background: #EAEEF9;
	text-align: center;

	img {
		width: 300px;
	}
}
