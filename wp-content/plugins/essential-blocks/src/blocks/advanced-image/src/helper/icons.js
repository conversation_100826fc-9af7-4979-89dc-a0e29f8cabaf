export const CustomIcon = () => (
    <svg width="129" height="90" viewBox="0 0 129 90" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="129" height="90" rx="4.27251" fill="#F5F7FA" />
        <path d="M77.6599 28.7519C76.7718 27.43 74.8404 27.3922 73.9013 28.6782L55.5987 53.7423C54.728 54.9346 52.9742 55.0066 52.0088 53.8895L44.3175 44.9904C43.3305 43.8484 41.5287 43.9538 40.6815 45.2032L23.4296 70.6464C22.3962 72.1703 23.4879 74.2295 25.3291 74.2295H103.906C105.743 74.2295 106.836 72.1792 105.811 70.6545L77.6599 28.7519Z" fill="#DEE2ED" />
        <path d="M54.1104 28.5914L52.2118 27.1068C52.2359 26.8778 52.2565 26.6102 52.2565 26.327C52.2565 26.0439 52.2367 25.7762 52.2118 25.5473L54.1121 24.0618C54.465 23.7829 54.5614 23.288 54.3368 22.8758L52.3624 19.4598C52.1515 19.0743 51.6894 18.8763 51.2203 19.0493L48.98 19.9487C48.5506 19.6388 48.1004 19.3772 47.6348 19.1681L47.294 16.7918C47.238 16.3408 46.8456 16 46.3817 16H42.4227C41.9588 16 41.5672 16.3408 41.5121 16.7849L41.1705 19.1698C40.7195 19.3729 40.2762 19.6311 39.8278 19.9504L37.5815 19.0485C37.1606 18.8858 36.6571 19.0682 36.448 19.4521L34.4711 22.8723C34.2379 23.2665 34.3343 23.7786 34.6949 24.0644L36.5935 25.549C36.5633 25.839 36.5487 26.0912 36.5487 26.3279C36.5487 26.5646 36.5634 26.8167 36.5935 27.1077L34.6931 28.5931C34.3403 28.8728 34.2447 29.3677 34.4694 29.7791L36.4437 33.1951C36.6546 33.5798 37.1124 33.7794 37.5858 33.6056L39.8261 32.7062C40.2547 33.0152 40.7048 33.2768 41.1705 33.4868L41.5113 35.8622C41.5672 36.3149 41.9588 36.6557 42.4236 36.6557H46.3826C46.8465 36.6557 47.2389 36.3149 47.294 35.8708L47.6357 33.4868C48.0867 33.2828 48.529 33.0255 48.9783 32.7053L51.2246 33.6073C51.333 33.6494 51.4458 33.671 51.562 33.671C51.8959 33.671 52.2032 33.4885 52.3581 33.2045L54.341 29.7705C54.5614 29.3677 54.465 28.8728 54.1104 28.5914ZM44.4022 29.7705C42.5036 29.7705 40.9596 28.2265 40.9596 26.3279C40.9596 24.4293 42.5036 22.8853 44.4022 22.8853C46.3008 22.8853 47.8448 24.4293 47.8448 26.3279C47.8448 28.2265 46.3008 29.7705 44.4022 29.7705Z" fill="#CBD0DD" />
    </svg>
);
export const SiteLogoIcon = () => (
    <svg width="129" height="90" viewBox="0 0 129 90" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="129" height="90" rx="4.27251" fill="#F5F7FA" />
        <rect x="33" y="18" width="30" height="4" rx="2" fill="#CBD0DD" />
        <rect x="69" y="18" width="40" height="4" rx="2" fill="#CBD0DD" />
        <rect x="8.03278" y="40.7869" width="111.311" height="4.59016" rx="2.29508" fill="#DEE2ED" />
        <rect x="8.03278" y="52.2623" width="111.311" height="4.59016" rx="2.29508" fill="#DEE2ED" />
        <rect x="8.03278" y="63.7377" width="76.8853" height="4.59016" rx="2.29508" fill="#DEE2ED" />
        <path fillRule="evenodd" clipRule="evenodd" d="M9.19149 20.6281C9.23506 21.2983 9.30834 21.8283 9.49305 22.512C9.54316 22.6975 9.70561 22.8219 9.89774 22.8219H12.473C12.723 22.8219 12.9158 22.6081 12.89 22.3595C12.7379 20.8946 12.7216 19.3711 12.8427 17.8934C12.8628 17.6478 12.6713 17.44 12.425 17.44H9.89778C9.70569 17.44 9.5432 17.5644 9.49309 17.7499C9.36846 18.2114 9.2785 18.6871 9.22848 19.1739C9.18347 19.6107 9.17845 19.8943 9.1808 20.3256L9.19149 20.6281ZM13.1183 15.7531C13.2878 14.8035 13.5151 13.8946 13.7953 13.0458C13.9206 12.6663 13.4881 12.3424 13.1592 12.5694C12.0621 13.3264 11.1367 14.3157 10.4554 15.4658C10.2894 15.746 10.4904 16.0986 10.8161 16.0986H12.7057C12.9106 16.0986 13.0823 15.9548 13.1183 15.7531ZM22.2071 15.598C21.8963 14.03 21.4158 12.6012 20.7892 11.419C20.731 11.3091 20.6383 11.2371 20.5175 11.2079C19.1023 10.865 17.6146 10.8653 16.2003 11.2079C16.0795 11.2371 15.9868 11.3091 15.9286 11.419C15.3021 12.6012 14.8215 14.03 14.5108 15.598C14.4592 15.8585 14.6564 16.0986 14.922 16.0986H21.7959C22.0615 16.0986 22.2587 15.8585 22.2071 15.598ZM24.0122 16.0986H25.9018C26.2275 16.0986 26.4284 15.7461 26.2625 15.4658C25.5812 14.3156 24.6557 13.3259 23.5585 12.5691C23.2296 12.3422 22.7971 12.6661 22.9224 13.0456C23.2026 13.8944 23.43 14.8034 23.5995 15.7531C23.6355 15.9548 23.8073 16.0986 24.0122 16.0986ZM24.2449 22.8219H26.8202C27.0123 22.8219 27.1747 22.6976 27.2248 22.5121C27.3495 22.0507 27.4394 21.575 27.4894 21.088C27.5382 20.6108 27.537 20.3923 27.537 19.9179C27.537 19.7278 27.5063 19.3377 27.4894 19.1739C27.4394 18.6871 27.3494 18.2114 27.2248 17.7499C27.1747 17.5644 27.0122 17.44 26.8201 17.44H24.2929C24.0466 17.44 23.8551 17.6478 23.8752 17.8934C23.9963 19.3711 23.98 20.8945 23.8279 22.3595C23.8021 22.6082 23.9949 22.8219 24.2449 22.8219ZM23.5021 24.4981C23.3016 25.475 23.0281 26.4524 22.6801 27.3638C22.5361 27.7408 22.9581 28.084 23.2978 27.8663C24.5095 27.0898 25.5273 26.0364 26.2622 24.7961C26.4282 24.5159 26.2273 24.1632 25.9016 24.1632H23.9127C23.7116 24.1633 23.5426 24.3011 23.5021 24.4981ZM14.0377 27.3636C13.6897 26.4521 13.4163 25.475 13.2157 24.4982C13.1753 24.3011 13.0063 24.1633 12.8052 24.1633H10.8163C10.4906 24.1633 10.2897 24.5159 10.4557 24.7962C11.1906 26.0363 12.2083 27.0895 13.42 27.8661C13.7597 28.0838 14.1817 27.7406 14.0377 27.3636ZM21.6789 24.1633H15.039C14.7685 24.1633 14.5701 24.4122 14.6304 24.6759C15.0074 26.3241 15.5751 27.7949 16.298 28.9476C16.3616 29.0491 16.4541 29.1128 16.5716 29.1361C17.7516 29.3698 18.9653 29.37 20.1462 29.1361C20.2637 29.1128 20.3562 29.0491 20.4199 28.9476C21.1428 27.7949 21.7105 26.3244 22.0875 24.6759C22.1478 24.4122 21.9494 24.1633 21.6789 24.1633ZM14.1041 19.8622C14.1041 20.7417 14.1559 21.6094 14.2529 22.4508C14.2775 22.6634 14.4553 22.8219 14.6693 22.8219H22.0486C22.2626 22.8219 22.4405 22.6634 22.465 22.4508C22.6413 20.9229 22.6615 19.3611 22.5226 17.8215C22.503 17.6047 22.3227 17.44 22.1051 17.44H14.6128C14.3951 17.44 14.2149 17.6047 14.1953 17.8215C14.135 18.4899 14.1041 19.1724 14.1041 19.8622Z" fill="#CBD0DD" />
    </svg>

);
export const FeaturedImgIcon = () => (
    <svg width="129" height="90" viewBox="0 0 129 90" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="129" height="90" rx="4.27251" fill="#F5F7FA" />
        <rect x="8.03278" y="64" width="111.311" height="4.59016" rx="2.29508" fill="#DEE2ED" />
        <rect x="8" y="75" width="91" height="5" rx="2.5" fill="#DEE2ED" />
        <rect x="8" y="8" width="111" height="51" rx="4.27251" fill="#DEE2ED" />
        <path d="M72.0127 18.2375C71.6274 17.6446 70.7652 17.6279 70.3573 18.2055L57.873 35.8832C57.4942 36.4196 56.7101 36.4515 56.2889 35.9477L50.5803 29.1179C50.15 28.603 49.3448 28.6497 48.9768 29.2108L37.0153 47.4516C36.5793 48.1167 37.0563 49 37.8516 49H90.1577C90.9511 49 91.4285 48.1204 90.9963 47.4552L72.0127 18.2375Z" fill="#F6F8FA" />
    </svg>
);

export const AdvancedImageIcon = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="60"
        height="60"
        fill="none"
        viewBox="0 0 60 60"
    >
        <g fill="#6C3BFF" clipPath="url(#clip0_2_22605)">
            <path d="M30.384 22.561a6.12 6.12 0 100 12.24 6.12 6.12 0 000-12.24zm0 9.326a3.206 3.206 0 110-6.412 3.206 3.206 0 010 6.412z"></path>
            <path d="M54.793 8.572L15.011 4.054a5.464 5.464 0 00-4.298 1.24A5.537 5.537 0 008.6 9.081l-.729 5.974H5.612c-3.206 0-5.61 2.842-5.61 6.048v29.8a5.538 5.538 0 005.61 5.683h40c3.207 0 6.121-2.477 6.121-5.683v-1.166a7.285 7.285 0 002.769-1.166 6.048 6.048 0 002.113-3.934l3.351-29.582a5.902 5.902 0 00-5.173-6.484zM48.82 50.904c0 1.603-1.603 2.768-3.206 2.768h-40a2.623 2.623 0 01-2.697-2.768v-5.392l11.294-8.306a3.497 3.497 0 014.517.219l7.942 6.994a6.85 6.85 0 004.299 1.603 6.63 6.63 0 003.497-.947l14.354-8.306v14.135zm0-17.56l-15.884 9.254a3.716 3.716 0 01-4.299-.365l-8.014-7.067a6.485 6.485 0 00-8.088-.292L2.916 41.87V21.104c0-1.603 1.093-3.133 2.696-3.133h40a3.352 3.352 0 013.207 3.133v12.24zm8.236-18.681l-.003.029-3.425 29.581a2.477 2.477 0 01-.947 1.968c-.291.291-.947.437-.947.583v-25.72a6.266 6.266 0 00-6.12-6.048H10.785l.656-5.683a3.352 3.352 0 011.093-1.894 3.351 3.351 0 012.186-.583l39.71 4.59a2.914 2.914 0 012.625 3.177z"></path>
        </g>
        <defs>
            <clipPath id="clip0_2_22605">
                <path fill="#fff" d="M0 0H60V60H0z"></path>
            </clipPath>
        </defs>
    </svg>
);
