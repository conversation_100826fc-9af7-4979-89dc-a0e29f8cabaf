.eb-adv-img-editor-source-select {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 40px;
	flex-wrap: wrap;
	padding: 20px 0 30px;
	border: 1px solid #d5d5db;

	h2 {
		width: 100%;
		text-align: center;
		font-size: 16px !important;
		color: #4b4b4b !important;
		font-weight: 600 !important;
	}

	.eb-adv-img-editor-source-item {
		display: flex;
		flex-flow: column;
		align-items: center;

		.eb-adv-img-editor-source-icon {
			padding: 5px;
			background-color: #fff;
			height: 102px;
			display: flex;
			align-items: center;
			border: 1px solid #eaeaee;
			border-radius: 6px;
			box-sizing: border-box;
		}

		span {
			font-size: 12px;
			color: #aaa;
			line-height: 1.4;
			margin-top: 10px;
		}

		&:hover {
			cursor: pointer;

			.eb-adv-img-editor-source-icon {
				border-color: #bebebe;
			}

			span {
				color: #888;
			}
		}
	}
}
