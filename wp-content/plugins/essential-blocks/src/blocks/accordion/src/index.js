/**
 * WordPress dependencies
 */
import { __ } from "@wordpress/i18n";

/**
 * Internal dependencies
 */
import "./style.scss";
import "./editor.scss";
import Edit from "./edit";
import Save from "./save";
import example from "./example";
import metadata from "../block.json";
import attributes from "./attributes";
import deprecated from "./deprecated";
import { ebConditionalRegisterBlockType } from '@essential-blocks/controls';

import { ReactComponent as Icon } from "./icon.svg";


ebConditionalRegisterBlockType(metadata, {
    icon: Icon,
    attributes,
    keywords: [
        __("accordion", "essential-blocks"),
        __("toggle", "essential-blocks"),
        __("eb essential", "essential-blocks"),
    ],
    edit: Edit,
    save: Save,
    example: example,
    deprecated,
});
