body {
	background-color: #f6f7fe;
}

#adminmenuwrap {
	z-index: 9990 !important;
}

#adminmenuback {
	z-index: 9980 !important;
}

.eb-settings-container {
	font-family: $dm-sans;
	margin-right: 30px;

	a {
		color: $eb-admin-link-color;

		&:hover {
			color: $eb-admin-link-hover-color;
		}
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6,
	p {
		margin: 0;
		padding: 0;
	}
}

.eb-admin-block {
	background: #ffffff;
	border-radius: 5px;
	box-shadow: 0px 1px 2px rgb(23 57 97 / 10%);
	padding: $eb-gap;
	// margin-bottom: $eb-gap;

	&.block-flex {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-start;

		img {
			margin-right: 20px;
		}
	}

	h2 {
		font-style: normal;
		font-weight: 700;
		font-size: 21px;
		line-height: 27px;

		color: $eb-admin-title-color;
	}

	.eb-admin-block__title {
		font-weight: 500;
		font-size: 16px;
		line-height: 1.2rem;
		color: $eb-admin-title-color;
		margin-bottom: 15px;

		display: flex;

		img {
			margin-right: 10px;
		}
	}

	.eb-admin-block__text {
		font-weight: 400;
		font-size: 14px;
		line-height: 1.2rem;
		color: $eb-admin-text-color;
		margin-bottom: 15px;
	}

	.eb-admin-block__link {
		font-weight: 500;
		font-size: 12px;
		line-height: 16px;
		color: $eb-admin-link-color;
		text-decoration: none;
	}
}

//

.p0 {
	padding: 0 !important;
}

.mb30 {
	margin-bottom: $eb-gap;
}

.mb20 {
	margin-bottom: 20px !important;
}

.mt10 {
	margin-top: 10px;
}

.eb-admin-block-title {
	font-weight: 700;
	font-size: 21px;
	line-height: 27px;
	margin: 0 0 16px 0 !important;
	padding: 0;
	color: #211c70;
}

.eb-admin-checkboxes-group-wrapper {

	.eb-group-title-wrapper {
		margin: 30px 0;

		.eb-block-group-title {

			font-weight: 500;
			font-size: 20px;
			line-height: 26px;
			margin: 0 0 15px 0;
			padding: 0;
			color: #211c70;
			border-bottom: 1px solid rgba(23, 57, 97, 0.1);
			padding-bottom: 10px;
			text-transform: capitalize;
		}
	}

	.eb-admin-checkboxes-wrapper {
		margin-bottom: 60px;
	}
}

//
.eb-admin-grid {
	display: grid;
	grid-template-columns: repeat(12, 1fr);
	gap: $eb-gap;
	margin-bottom: 30px;

	.eb-admin-inner-grid {
		background-color: #F5F7F9;
		padding: 24px;
		border-radius: 4px;
	}

	// &>div>div {
	// 	height: 100%;
	// 	box-sizing: border-box;
	// }

	@media all and (max-width: 767px) {
		display: block;

		// .eb-col-12,
		// .eb-col-8,
		// .eb-col-7,
		// .eb-col-6,
		// .eb-col-5,
		// .eb-col-4,
		// .eb-col-3 {
		// 	margin-bottom: 20px;
		// }
	}
}

@media (min-width: 768px) {
	.eb-admin-grid {
		.eb-col-12 {
			grid-column: span 12;
		}

		.eb-col-8 {
			grid-column: span 8;
		}

		.eb-col-7 {
			grid-column: span 7;
		}

		.eb-col-6 {
			grid-column: span 6;
		}

		.eb-col-5 {
			grid-column: span 5;
		}

		.eb-col-4 {
			grid-column: span 4;
		}

		.eb-col-3 {
			grid-column: span 3;
		}
	}
}

.embed-responsive {
	position: relative;
	display: block;
	width: 100%;
	padding: 0;
	overflow: hidden;

	&::before {
		display: block;
		content: "";
	}
}

.embed-responsive-16by9::before {
	padding-top: 56.25%;
}

.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border: 0;
}

// Button

.eb-btn {
	display: inline-block;
	font-weight: 400;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	border: 1px solid transparent;
	padding: 11px 20px;
	// padding: 0.375rem 0.75rem;
	font-size: 1rem;
	line-height: 1.5;
	text-decoration: none;
	border-radius: 0.25rem;
	transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
		border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	cursor: pointer;

	&-primary {
		color: #fff !important;
		background-color: #007bff;
		border-color: #007bff;
	}

	&-link {
		font-weight: 400;
		color: #007bff;
		background-color: transparent;
	}

	&-border {
		border: 1px solid $eb-admin-theme-color;
		border-radius: 5px;
		color: $eb-admin-theme-color;
		background-color: transparent;

		&:hover {
			color: #fff !important;
			background-color: $eb-admin-theme-color;
			border-color: $eb-admin-theme-color;
		}
	}

	&-sm {
		font-weight: 500;
		font-size: 12px;
		line-height: 16px;
		padding: 9px 14px;
	}

	&-md {
		font-weight: 500;
		font-size: 15px;
		line-height: 1.2em;
		padding: 9px 14px;
	}

	&-center {
		display: block;
		width: max-content;
		margin: 0 auto !important;
	}

	&-reset {
		margin: 0 15px;
		color: #e41c1c;
		background-color: #fdf2f2;
	}

	display: inline-flex;
	justify-content: center;
	align-items: center;

	.eb-install-loader {
		width: 18px;
		margin-right: 5px;
	}
}

// form

.eb-form-control {
	margin-bottom: 20px;

	label {
		display: inline-block;
		margin-bottom: 0.5rem;
		font-weight: 500;
		font-size: 14px;
		line-height: 24px;
		/* identical to box height, or 171% */

		color: #211c70;
	}

	.eb-input-control {
		display: block;
		width: 100%;
		padding: 11px 15px;
		font-size: 1rem;
		line-height: 1.5;
		color: #6a72a5;
		background-color: #fff;
		background-clip: padding-box;
		border: 1px solid #cfdcf1;
		border-radius: 3px;
		transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

		&::placeholder {
			font-weight: 400;
			font-size: 12px;
			line-height: 24px;

			color: #6a72a5;
		}
	}

	.eb-form-radio-wrapper {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		gap: 20px;
		font-size: .85rem;
		line-height: 1.5;
		color: #6a72a5;
	}
}

.mp0 {
	padding: 0;
	margin: 0;
}

.eb-alert-error {
	font-size: 12px;
	line-height: 1.2em;
	color: #ff0000;
	margin-top: 5px;
	display: inline-block;
}

.eb-alert {
	padding: 0.5rem 0.7rem;
	margin-bottom: 1rem;
	border: 1px solid transparent;
	// border-radius: 0.25rem;

	&.eb-alert-success {
		color: #0f5132;
		background-color: #d1e7dd;
		border-color: #badbcc;
		font-size: 14px;
		line-height: 1.2rem;
	}

	.eb-alert-warning {
		color: #ff0000;
		font-size: 12px;
		line-height: 1.2rem;
		font-weight: 400;
		text-transform: capitalize;
		display: inline-block;
		margin-top: 5px;
	}
}

// responsive

@media all and (max-width: 1024px) {
	.eb-block-md {
		display: block;
	}

	.eb-block-md {
		display: block;
	}

	.eb-col-4-md {
		grid-column: span 4 !important;
	}
}

@media all and (max-width: 767px) {
	.eb-block-xs {
		display: block !important;
	}
}