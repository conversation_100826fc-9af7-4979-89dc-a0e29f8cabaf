.configuration-intro-content {
	gap: 25px;
	margin: 60px 20% 40px;

	@media all and (max-width: 768px) {
		margin: 15px 5% 40px;
	}

	img {
		width: 30px;
		padding: 10px;
		background: #FFFFFF;
		border: 0.518868px solid #FFFFFF;
		box-shadow: 0px 0px 1px rgba(31, 38, 50, 0.35);
		border-radius: 8px;
	}
}

.eb-configuration-content-wrap {
	gap: 30px;
	width: 80%;
	margin: 0 auto 80px;

	@media all and (max-width: 768px) {
		flex-direction: column;
		margin-bottom: 0;
	}

	.eb-configuration-content {
		display: flex;
		flex-direction: column;
		align-items: flex-start;

		label {
			background: linear-gradient(309.52deg, #F6F3FF 0%, #FFFEFF 46.38%);
			border: 1px solid #EBE0F8;
			border-radius: 8px;
			text-align: left;
			cursor: pointer;

			&.selected {
				border-color: #2673FF;
			}

		}

		.eb-configuration-content-title {
			padding: 24px;
			border-bottom: 1px solid #EBE0F8;

			h4 {
				font-family: $inter;
				font-style: normal;
				font-weight: 600;
				font-size: 18px;
				line-height: 110%;

				color: #1D2939;

				display: flex;
				justify-content: center;
				align-items: center;
				gap: 5px;

				span {
					color: #D047DF;
					background: #FDF5FF;
					border: 1px solid #F4E8F7;
					border-radius: 12px;
					padding: 4px 8px;
					font-family: 'Inter';
					font-style: normal;
					font-weight: 500;
					font-size: 12px;
					line-height: 110%;
				}
			}
		}

		p {
			padding: 24px 24px 50px;
			font-family: $inter;
			font-style: normal;
			font-weight: 400;
			font-size: 14px;
			line-height: 1.5rem;
			color: #4C5C7A;

			@media all and (max-width: 1024px) {
				padding: 20px;
			}
		}

	}
}

.step-wrapper {
	margin-top: 20px;
	gap: 20px;
}