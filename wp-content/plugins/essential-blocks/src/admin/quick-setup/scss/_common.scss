body {
	background-color: #f6f7fe;
}

#adminmenuwrap {
	z-index: 9990 !important;
}

#adminmenuback {
	z-index: 9980 !important;
}

.eb-admin-checkboxes-group-wrapper {

	.eb-group-title-wrapper {
		margin: 30px 0;

		.eb-block-group-title {

			font-weight: 500;
			font-size: 20px;
			line-height: 26px;
			margin: 0 0 15px 0;
			padding: 0;
			color: #211c70;
			border-bottom: 1px solid rgba(23, 57, 97, 0.1);
			padding-bottom: 10px;
			text-transform: capitalize;
		}
	}

	.eb-admin-checkboxes-wrapper {
		margin-bottom: 60px;

		@media all and (max-width: 767px) {
			margin-bottom: 0;
		}
	}
}

//
.eb-admin-grid {
	display: grid;
	grid-template-columns: repeat(12, 1fr);
	gap: $eb-gap;
	margin-bottom: 30px;

	// &>div>div {
	// 	height: 100%;
	// 	box-sizing: border-box;
	// }

	@media all and (max-width: 767px) {
		display: block;

		// .eb-col-12,
		// .eb-col-8,
		// .eb-col-7,
		// .eb-col-6,
		// .eb-col-5,
		// .eb-col-4,
		// .eb-col-3 {
		// 	margin-bottom: 20px;
		// }
	}
}

@media (min-width: 768px) {
	.eb-admin-grid {
		.eb-col-12 {
			grid-column: span 12;
		}

		.eb-col-8 {
			grid-column: span 8;
		}

		.eb-col-7 {
			grid-column: span 7;
		}

		.eb-col-6 {
			grid-column: span 6;
		}

		.eb-col-5 {
			grid-column: span 5;
		}

		.eb-col-4 {
			grid-column: span 4;
		}

		.eb-col-3 {
			grid-column: span 3;
		}
	}
}


.eb-setup-settings-container {
	font-family: $inter;
	// margin-right: 30px;

	a {
		color: $eb-admin-link-color;

		&:hover {
			color: $eb-admin-link-hover-color;
		}
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6,
	p {
		margin: 0;
		padding: 0;
	}
}


// setup
.eb-flex-row-center {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
}

.eb-flex-row-between {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}

.eb-flex-row-end {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: flex-end;
}

.eb-flex-column-center {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.eb-setup-settings-container {
	max-width: 1170px;
	margin: 0 auto;
	padding: 80px 20px 0 0;

	@media all and (max-width: 1024px) {
		padding: 20px 20px 0 0;
	}

	@media all and (max-width: 768px) {
		padding: 20px 10px 0 0;
	}
}

.eb-quick-setup-content {
	box-sizing: border-box;
	background: #FFFFFF;
	border: 1px solid #F0F2F5;
	box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.35);
	border-radius: 8px;

	text-align: center;
	padding: 25px;

	&.eb-text-left {
		text-align: left;
	}


	h3 {
		font-family: $inter;
		font-style: normal;
		font-weight: 600;
		font-size: 24px;
		line-height: 1.5rem;

		color: #15253E;
	}

	p {
		font-family: $inter;
		font-style: normal;
		font-weight: 400;
		font-size: 16px;
		line-height: 1.4rem;

		color: #4C5C7A;
	}
}

// Button
.eb-setup-btn {
	padding: 12px 20px;
	gap: 8px;
	border: none;
	border-radius: 4px;
	font-family: $inter;
	font-style: normal;
	font-weight: 500;
	font-size: 14px;
	line-height: 120%;
	cursor: pointer;

	&:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	svg {
		width: 20px;
		height: 20px;
	}

	.eb-install-loader {
		width: 16px;
	}

	&.eb-setup-btn-next {
		background: #6C3BFF;
		color: #fff;

		svg g {
			stroke: #fff;
		}
	}

	&.eb-pro-upgrade {
		background: #6C3BFF;
		color: #fff;
		display: inline-block;
		margin-top: 20px;
		text-decoration: none;

		&:hover {
			color: #fff;
		}
	}

	&.eb-setup-btn-previous,
	&.eb-setup-btn-secondry {
		background: #FFFFFF;
		border: 1px solid #E0E7F1;
		color: #6C3BFF;
	}

	&.eb-setup-btn-link {
		color: #6C3BFF;
		text-decoration: none;
		padding: 0;
		border-radius: 0;
	}
}

.skip-setup-btn {
	font-family: $inter;
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 1.2rem;
	text-decoration-line: underline;
	// margin-top: 15px;
	color: #707E95;

	background: none;
	border: none;
	padding: 0;
	outline: inherit;
	cursor: pointer;

	&.no-underline {
		text-decoration: none;
	}
}


.setup-cta-section {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: start;
	gap: 20px;
	background: linear-gradient(77.15deg, #FDFCFF 30.96%, #EEE1FF 218.05%);
	border: 1px solid #EBE0F8;
	border-radius: 8px;
	padding: 25px;
	margin-bottom: 25px;

	@media all and (max-width: 768px) {
		flex-direction: column;
		justify-content: center;
		text-align: center;

		.eb-setup-btn,
		.eb-setup-cta-btn {
			margin-left: 0 !important;
		}
	}

	img {
		width: 30px;
		padding: 10px;
		background: #FFFFFF;
		border: 0.518868px solid #FFFFFF;
		box-shadow: 0px 0px 1px rgba(31, 38, 50, 0.35);
		border-radius: 8px;
	}

	h3 {
		font-family: 'Inter';
		font-style: normal;
		font-weight: 550;
		font-size: 24px;
		line-height: 100%;
		color: #1F242C;
		margin-bottom: 10px;

		span {
			color: #6C3BFF;
			position: relative;
		}
	}

	p {
		font-family: $inter;
		font-style: normal;
		font-weight: 400;
		font-size: 12px;
		line-height: 1.2rem;
		color: #4C5C7A;
	}

	.eb-setup-btn,
	.eb-setup-cta-btn {
		margin-left: auto;
		display: flex;
		justify-content: center;
		align-items: center;
		text-decoration: none;
	}
}

// option-card
.eb-setup-option-card-wrap {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16px;

	@media all and (max-width: 768px) {
		grid-template-columns: repeat(1, 1fr);
	}

	.eb-setup-option-card {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-start;
		background: #FFFFFF;
		border: 1px solid #DCE0E7;
		border-radius: 8px;

		.option-block-header {
			padding: 21px 24px;
			display: flex;
			align-items: center;
			gap: 10px;
			border-bottom: 1px solid #DCE0E7;
			width: 100%;
			box-sizing: border-box;
			background-color: #F9FBFF;
			border-radius: 8px 8px 0 0;

			img {
				width: 30px;
				height: 30px;
			}

			h5 {
				font-family: 'Inter';
				font-style: normal;
				font-weight: 600;
				font-size: 16px;
				line-height: 110%;

				color: #1D2939;
			}
		}

		.option-block-content {
			padding: 24px;
		}

		.option-block-footer {
			border-top: 1px solid #DCE0E7;
			display: flex;
			gap: 10px;
			justify-content: space-between;
			align-items: center;
			box-sizing: border-box;
			width: -webkit-fill-available;
			width: -moz-available;
			margin: auto 25px 25px;
			padding-top: 15px;

			h5 {
				font-family: 'Inter';
				font-style: normal;
				font-weight: 500;
				font-size: 14px;
				line-height: 120%;
				color: #1D2939;
			}
		}
	}
}


.option-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.2);
	z-index: 160000;
	display: flex;
	justify-content: center;
	align-items: center;

	&.setup-complete-modal {
		.option-modal__inner {
			text-align: center;
			width: 650px;
			padding: 70px 30px;
			box-sizing: border-box;

			@media all and (max-width: 767px) {
				padding: 20px;
			}

			.option-modal-content svg {
				width: 220px;
				height: 210px;

				@media all and (max-width: 767px) {
					width: 120px;
					height: 110px;
				}
			}
		}

		.option-modal__title {
			font-size: 40px;
			line-height: 2rem;
			color: #1D2939;
			margin-top: 20px;

			@media all and (max-width: 767px) {
				font-size: 30px;
				margin-top: 0;
			}
		}

		.option-modal__content {
			font-size: 16px;
			line-height: 1.5rem;
			color: #475467;
			margin: 20px 0 0 0;
		}


	}

	.option-modal__inner {
		overflow: hidden;
		position: relative;
		top: 25px;
		left: 0;
		width: 40%;
		// height: 70vh;
		padding: 30px;
		background-color: #fff;
		box-shadow: 0px 45px 95px rgba(7, 8, 41, 0.14);
		border-radius: 16px;

		@media all and (max-width: 767px) {
			width: 70%;
		}
	}

	.close-btn {
		position: absolute;
		top: 16px;
		right: 16px;
		cursor: pointer;
		margin: 0;
		padding: 0;
		border: 1px solid transparent;
		background: 0 0;
		color: #646970;
		z-index: 1000;
		cursor: pointer;
		outline: 0;
		transition: color 0.1s ease-in-out, background 0.1s ease-in-out;

		svg {
			width: 24px;
			height: 24px;
		}
	}

	.option-modal__title {
		font-weight: 700;
		font-size: 26px;
		line-height: 34px;
		/* identical to box height */

		color: #211c70;

		padding: 0;
		margin: 0 0 5px 0;
	}

	.option-modal__content {
		font-weight: 400;
		font-size: 14px;
		line-height: 24px;
		/* or 171% */

		color: #6a72a5;

		padding: 0;
		margin: 0 0 25px 0;
	}
}