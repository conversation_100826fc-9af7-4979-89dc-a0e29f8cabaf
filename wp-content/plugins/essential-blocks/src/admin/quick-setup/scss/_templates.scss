.eb-setup-templates {
	.eb-templates-intro-block {
		margin: 45px;
		gap: 30px;

		flex-wrap: wrap;

		@media all and (max-width: 1024px) {
			margin: 0px;
		}

		@media all and (max-width: 768) {
			flex-direction: column;
		}

		>div {
			flex: 0 0 46%;
		}

		h3 {
			font-family: $inter;
			font-style: normal;
			font-weight: 600;
			font-size: 32px;
			line-height: 2.5rem;
			color: #1D2939;
			margin-bottom: 15px;

			span {
				font-family: $inter;
				font-style: normal;
				font-weight: 700;
				font-size: 32px;
				line-height: 130%;
				color: #6C3BFF;
				position: relative;

				&::after {
					position: absolute;
					bottom: -4px;
					left: 0;
					content: url("data:image/svg+xml,%3Csvg width='108' height='6' viewBox='0 0 108 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 2.01621L107 1C98.767 1 78.1845 3 62.7476 5' stroke='%236C3BFF' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
				}
			}
		}

		p {
			font-size: 16px;
			line-height: 1.5rem;
			color: #475467;
			margin-bottom: 25px;
		}

		ul {
			margin: 15px 0 0 0;

			li {
				display: flex;
				gap: 10px;
				align-items: center;
				margin: 0;
				font-family: 'Inter';
				font-style: normal;
				font-weight: 400;
				font-size: 16px;
				line-height: 40px;
				color: #1D2939;

				img {
					width: 20px;
				}
			}
		}

		.eb-templates-frame {
			background: linear-gradient(124.05deg, rgba(249, 248, 255, 0.4) 0%, rgba(255, 246, 241, 0.4) 5%, rgba(249, 248, 255, 0.4) 100%);
			border: 1px solid #EBE0F8;
			backdrop-filter: blur(49.1192px);
			border-radius: 10px;
			padding: 13px;
			box-sizing: border-box;

			img {
				width: 100%;
			}
		}
	}

	.skip-setup-btn {
		margin: 0;
		color: #1F242C;
	}
}