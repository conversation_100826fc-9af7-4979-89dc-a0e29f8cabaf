.rc-switch {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
  width: 70px;
  height: 31px;
  line-height: 29px;
  padding: 0;
  vertical-align: middle;
  border-radius: 30px;
  border: 1px solid #ccc;
  background-color: #ccc;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.35, 0, 0.25, 1);
}
.rc-switch-inner {
  color: #fff;
  font-size: 12px;
  position: absolute;
  left: 31px;
  top: 0;
}
.rc-switch:after {
  position: absolute;
  width: 20px;
  height: 20px;
  left: 6px;
  top: 4px;
  border-radius: 50% 50%;
  background-color: #fff;
  content: " ";
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.26);
  -webkit-transform: scale(1);
          transform: scale(1);
  transition: left 0.3s cubic-bezier(0.35, 0, 0.25, 1);
  -webkit-animation-timing-function: cubic-bezier(0.35, 0, 0.25, 1);
          animation-timing-function: cubic-bezier(0.35, 0, 0.25, 1);
  -webkit-animation-duration: 0.3s;
          animation-duration: 0.3s;
  -webkit-animation-name: rcSwitchOff;
          animation-name: rcSwitchOff;
}
.rc-switch:hover:after {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  -webkit-animation-name: rcSwitchOn;
          animation-name: rcSwitchOn;
}
.rc-switch:focus {
  box-shadow: 0 0 0 2px #d5f1fd;
  outline: none;
}
.rc-switch-checked {
  border: 1px solid #36d66b;
  background-color: rgba(54,214,107, .8);
}
.rc-switch-checked .rc-switch-inner {
  left: 12px;
}
.rc-switch-checked:after {
  left: 40px;
}
.rc-switch-disabled {
  cursor: no-drop;
  background: #ccc;
  border-color: #ccc;
}
.rc-switch-disabled:after {
  background: #9e9e9e;
  -webkit-animation-name: none;
          animation-name: none;
  cursor: no-drop;
}
.rc-switch-disabled:hover:after {
  -webkit-transform: scale(1);
          transform: scale(1);
  -webkit-animation-name: none;
          animation-name: none;
}
.rc-switch-label {
  display: inline-block;
  line-height: 20px;
  font-size: 14px;
  padding-left: 10px;
  vertical-align: middle;
  white-space: normal;
  pointer-events: none;
  -webkit-user-select: text;
     -moz-user-select: text;
      -ms-user-select: text;
          user-select: text;
}
@-webkit-keyframes rcSwitchOn {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.25);
            transform: scale(1.25);
  }
  100% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
}
@keyframes rcSwitchOn {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.25);
            transform: scale(1.25);
  }
  100% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
}
@-webkit-keyframes rcSwitchOff {
  0% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@keyframes rcSwitchOff {
  0% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}


/* Global enable disable button styles */
.eb-admin-global-control {
  display: flex;
}

.eb-admin-button {
  border: 0px solid;
  color: #fff;
  padding: 13px 25px;
  border-radius: 3px;
  font-size: 14px;
  letter-spacing: 1px;
  margin: 0 3px;
  cursor: pointer;
  transition: 300ms;
}

.eb-admin-button-enable {
  background:#0099cc;
}

.eb-admin-button-disable {
  background: #dc4444;
}
