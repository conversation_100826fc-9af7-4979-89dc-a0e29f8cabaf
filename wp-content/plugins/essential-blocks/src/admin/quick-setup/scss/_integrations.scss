.eb-setup-integrations {
	.setup-cta-section h3 {
		font-size: 21px;

		span {
			&::after {
				position: absolute;
				bottom: -5px;
				left: 6px;
				content: url("data:image/svg+xml,%3Csvg width='115' height='6' viewBox='0 0 115 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 2.01562L114 1C106 1 77 2.99941 62 4.99941' stroke='%236C3BFF' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
			}
		}
	}


	.eb-setup-cta-btn {
		gap: 14px;
		font-weight: 500;
		font-size: 14px;
		line-height: 120%;
		color: #475467;
	}

	.eb-setup-btn.eb-setup-btn-next {
		padding: 14px 35px;
	}

	.option-block-footer {
		border-top: 1px solid #DCE0E7;
		display: flex;
		flex-direction: column;
		align-items: flex-start !important;
		gap: 10px;
		box-sizing: border-box;
		width: 100%;
		margin: auto 25px 25px;
		padding-top: 15px;

		.option-block-footer-content {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
		}

		.integration-error {
			color: red;
			font-size: 14px;
		}
	}

	.rc-switch:not(.eb-custom-disabled) {
		&.rc-switch-disabled {
			&::after {
				// cursor: pointer;
				background: #fff;
			}
		}
	}

	.eb-custom-disabled {
		cursor: pointer;

		&.rc-switch-checked {
			background-color: #2673ff !important;
			border-color: #2673ff;
		}

		&::after {
			cursor: pointer;
			background: #fff;
		}
	}
}

.eb-integrations-content-wrap {
	max-height: 450px;
	overflow-y: scroll;
}