.eb-setup-nav-wrapper {
	padding: 20px 30px;
	display: flex;
	align-items: center;
	margin-bottom: 5px;

	background: #FFFFFF;
	border: 1px solid #F0F2F5;
	box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.35);
	border-radius: 8px;

	@media all and (max-width: 1024px) {
		flex-wrap: wrap;
	}

	.eb-setup-nav {
		margin: 0;
		padding: 0;
		flex-wrap: wrap;
		gap: 10px;
		width: 100%;

		@media all and (max-width: 1024px) {
			// order: 2;
			// flex-basis: 100%;
			justify-content: center;
			// border-top: 1px dashed #d6d8e4;
			// padding-top: 15px;
			// margin-top: 10px;
		}

		li {
			margin: 0;
			padding: 0;
			gap: 8px;


			&.active {
				.eb-setup-count {
					color: #15253E;
					border-color: #6C3BFF;
					box-shadow: 0px 0px 0px 3px rgba(108, 59, 255, 0.3);
				}

				.eb-setup-title {
					color: #15253E;
				}
			}

			&.complete {
				.eb-setup-count {
					color: #15253E;
					font-size: 0;
					border-color: #6C3BFF;
					background-color: #6C3BFF;
					background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9IkZyYW1lIiBjbGlwLXBhdGg9InVybCgjY2xpcDBfMjA2MV8xMjc1MikiPgo8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMy43NSA5TDcuNSAxMi43NUwxNSA1LjI1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIuNzUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L2c+CjxkZWZzPgo8Y2xpcFBhdGggaWQ9ImNsaXAwXzIwNjFfMTI3NTIiPgo8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIGZpbGw9IndoaXRlIi8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPC9zdmc+Cg==);
					background-position: 50%;
					background-repeat: no-repeat;
					background-size: 16px;
				}

				.eb-setup-title {
					color: #15253E;
				}
			}
		}

		.eb-setup-count {
			border: 1.5px solid #CBD1DA;
			color: #7C8798;
			font-family: $inter;
			font-style: normal;
			font-weight: 500;
			font-size: 18px;
			line-height: 28px;
			width: 28px;
			height: 28px;
			display: inline-block;
			border-radius: 50%;
			text-align: center;
		}

		.eb-setup-title {
			font-family: $inter;
			font-style: normal;
			font-weight: 500;
			font-size: 18px;
			line-height: 16px;

			color: #707E95;
		}


	}
}