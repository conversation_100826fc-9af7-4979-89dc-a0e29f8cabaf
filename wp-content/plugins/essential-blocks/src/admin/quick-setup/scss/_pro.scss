.eb-setup-pro {
	.setup-cta-section h3 span {
		&::after {
			position: absolute;
			bottom: -5px;
			right: 9px;
			content: url("data:image/svg+xml,%3Csvg width='105' height='6' viewBox='0 0 105 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 2.01621L104 1C96 1 76 3 61 5' stroke='%236C3BFF' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
		}
	}


	.btn-upgrade-pro {
		text-decoration: none;
		display: inline-flex;
		font-size: 12px;
		font-weight: 500;
		line-height: 1.5rem;
		color: #fff;
		display: flex;
		gap: 10px;
		align-items: center;
		justify-content: center;
		padding: 10px 22px;
		background: #FF9437;
		border-radius: 8px;
		border-bottom: 1px solid #ED7206;
		transition: all .35s ease-in-out;
		cursor: pointer;

		&:hover {
			color: #fff;
		}
	}

	.eb-pro-content-wrap {
		gap: 10%;

		@media all and (max-width: 768px) {
			flex-direction: column-reverse;
			gap: 30px;
		}

		.eb-pro-content {

			h3 {
				font-family: $inter;
				font-style: normal;
				font-weight: 600;
				font-size: 24px;
				line-height: 2.5rem;
				color: #1D2939;
				margin-bottom: 15px;

				span {
					color: #6C3BFF;
				}
			}

			p {
				font-size: 16px;
				line-height: 1.5rem;
				color: #475467;
				margin-bottom: 25px;
			}

			ul {
				margin: 15px 0 0 0;

				li {
					margin: 0;
					font-family: 'Inter';
					font-style: normal;
					font-weight: 500;
					font-size: 16px;
					line-height: 40px;
					color: #344054;
					position: relative;
					padding: 0 0 0 25px;

					&::before {
						position: absolute;
						left: 0;
						top: 1px;
						content: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 7L6.33333 8.33333L9 5.66667M1 7C1 7.78793 1.15519 8.56815 1.45672 9.2961C1.75825 10.0241 2.20021 10.6855 2.75736 11.2426C3.31451 11.7998 3.97595 12.2417 4.7039 12.5433C5.43185 12.8448 6.21207 13 7 13C7.78793 13 8.56815 12.8448 9.2961 12.5433C10.0241 12.2417 10.6855 11.7998 11.2426 11.2426C11.7998 10.6855 12.2417 10.0241 12.5433 9.2961C12.8448 8.56815 13 7.78793 13 7C13 6.21207 12.8448 5.43185 12.5433 4.7039C12.2417 3.97595 11.7998 3.31451 11.2426 2.75736C10.6855 2.20021 10.0241 1.75825 9.2961 1.45672C8.56815 1.15519 7.78793 1 7 1C6.21207 1 5.43185 1.15519 4.7039 1.45672C3.97595 1.75825 3.31451 2.20021 2.75736 2.75736C2.20021 3.31451 1.75825 3.97595 1.45672 4.7039C1.15519 5.43185 1 6.21207 1 7Z' stroke='%232673FF' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
					}
				}
			}

			.eb-setup-btn-link {
				margin-top: 40px;
				font-size: 16px;
				width: max-content;
				border-bottom: 1px solid #6C3BFF;

				&:hover {
					color: #000;
					border-bottom-color: #000;
				}
			}
		}

		.eb-pro-blocks-wrap {
			background: linear-gradient(124.05deg, #F9F8FF 0%, #FFF6F1 25.45%, #F9F8FF 100%);
			border: 1px solid #F3ECFA;
			border-radius: 9px;
			padding: 40px 25px;
			display: grid;
			grid-template-columns: repeat(5, 1fr);
			gap: 5px;

			@media all and (max-width: 768px) {
				width: 100%;
				box-sizing: border-box;
			}

			.eb-pro-block {
				text-decoration: none;
				position: relative;
				padding: 20px;
				background: #FFFFFF;
				border-radius: 15px;


				&:hover {
					.tooltip-text {
						opacity: 1;
					}
				}

				img {
					display: block;
					width: 38px;
				}

				.tooltip-text {
					position: absolute;
					top: -20px;
					left: 50%;
					-webkit-transform: translateX(-50%);
					transform: translateX(-50%);
					background: #fff;
					border-radius: 3px;
					font-weight: 500;
					font-size: 12px;
					line-height: 16px;
					color: #000;
					padding: 8px;
					-webkit-transition: opacity 0.3s ease;
					transition: opacity 0.3s ease;
					opacity: 0;
					z-index: 1;
					width: max-content;
					box-shadow: 0px 8px 16px -2px #1b212c1f;

					&::before {
						position: absolute;
						top: 100%;
						left: 50%;
						-webkit-transform: translateX(-50%);
						transform: translateX(-50%);
						border-top: 5px solid #fff;
						border-right: 5px solid transparent;
						border-left: 5px solid transparent;
						content: "";
					}
				}
			}
		}
	}
}