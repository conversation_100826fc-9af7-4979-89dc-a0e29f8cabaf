(()=>{"use strict";var e,t={9096:(e,t,r)=>{var n={};r.r(n),r.d(n,{fetchBlockData:()=>fe,fetchBlockDefaults:()=>$,fetchCustomColor:()=>re,fetchCustomGradientColor:()=>se,fetchGlobalColor:()=>W,fetchGlobalTypography:()=>be,fetchGradientColor:()=>ae,fetchIsSaving:()=>ie,saveBlockDefault:()=>Z,saveCustomColors:()=>te,saveCustomGradientColors:()=>ue,saveGlobalColors:()=>Q,saveGlobalTypography:()=>ve,saveGradientColors:()=>oe,setBlockData:()=>pe,setBlockDefault:()=>X,setCustomColors:()=>ee,setCustomGradientColors:()=>ce,setGlobalColors:()=>q,setGlobalTypography:()=>Oe,setGradientColors:()=>ne,setIsSaving:()=>le});var o={};r.r(o),r.d(o,{getBlockData:()=>me,getBlockDefaults:()=>Se,getBlockDefaultsByItem:()=>Te,getCustomColors:()=>_e,getCustomGradientColors:()=>Ce,getGlobalColors:()=>de,getGlobalColorsByItem:()=>he,getGlobalTypography:()=>ye,getGradientColors:()=>ge,getIsSaving:()=>ke});var a={};r.r(a),r.d(a,{FETCH_BLOCK_DEFAULTS:()=>De,FETCH_CUSTOM_COLORS:()=>we,FETCH_CUSTOM_GRADIENT_COLORS:()=>Le,FETCH_GLOBAL_COLORS:()=>Ee,FETCH_GLOBAL_TYPOGRAPHY:()=>Ge,FETCH_GRADIENT_COLORS:()=>je});var c={};r.r(c),r.d(c,{getBlockDefaults:()=>Ve,getCustomColors:()=>He,getCustomGradientColors:()=>Ue,getGlobalColors:()=>Fe,getGlobalTypography:()=>Me,getGradientColors:()=>Ie});const u=window.wp.data;var s=r(4467),l=r(2284),i=r(467);const p=window.regeneratorRuntime;var f=r.n(p),O={globalColors:[],customColors:[],gradientColors:[],customGradientColors:[],globalTypography:{},blockDefaults:{},isSaving:!1,isChanged:!1,blockData:{}},v="global_colors",b="custom_colors",d="gradient_colors",h="custom_gradient_colors",_="global_typography",g="SET_GLOBAL_COLORS",C="SAVE_GLOBAL_COLORS",y="FETCH_GLOBAL_COLORS",S="SET_BLOCK_DEFAULTS",T="SAVE_BLOCK_DEFAULTS",k="FETCH_BLOCK_DEFAULTS",m="SET_CUSTOM_COLORS",A="SAVE_CUSTOM_COLORS",E="FETCH_CUSTOM_COLORS",w="SET_GRADIENT_COLORS",j="SAVE_GRADIENT_COLORS",L="FETCH_GRADIENT_COLORS",G="SET_CUSTOM_GRADIENT_COLORS",D="SAVE_CUSTOM_GRADIENT_COLORS",x="FETCH_CUSTOM_GRADIENT_COLORS",P="SET_IS_SAVING",B="FETCH_IS_SAVING",R="SET_BLOCK_DATA",N="FETCH_BLOCK_DATA",J="SET_GLOBAL_TYPOGRAPHY",F="SAVE_GLOBAL_TYPOGRAPHY",H="FETCH_GLOBAL_TYPOGRAPHY",I=EssentialBlocksLocalize.admin_nonce,U=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all",r=new FormData;return r.append("action","global_styles_update"),r.append("admin_nonce",I),r.append("eb_global_style_key",t),r.append("eb_global_style_value",JSON.stringify(e)),fetch(EssentialBlocksLocalize.ajax_url,{method:"POST",body:r}).then(function(e){return e.text()}).then(function(e){var t=JSON.parse(e);if(t.success)return t.data;console.log("Update failed! ",t.data)}).catch(function(e){return console.log(e)})},M=function(){var e=new FormData;return e.append("action","global_styles_get"),e.append("admin_nonce",I),fetch(EssentialBlocksLocalize.ajax_url,{method:"POST",body:e}).then(function(e){return e.text()}).then(function(e){var t=JSON.parse(e);return!!t.success&&t.data}).catch(function(e){return console.log(e)})},V=function(e){var t=new FormData;return t.append("action","block_defaults_update"),t.append("admin_nonce",I),t.append("eb_block_defaults_value",JSON.stringify(e)),fetch(EssentialBlocksLocalize.ajax_url,{method:"POST",body:t}).then(function(e){return e.text()}).then(function(e){var t=JSON.parse(e);if(t.success)return t.data;console.log("failed update",e)}).catch(function(e){return console.log(e)})},Y=function(){var e=new FormData;return e.append("action","block_defaults_get"),e.append("admin_nonce",I),fetch(EssentialBlocksLocalize.ajax_url,{method:"POST",body:e}).then(function(e){return e.text()}).then(function(e){var t=JSON.parse(e);return!!t.success&&t.data}).catch(function(e){return console.log(e)})};function K(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?K(Object(r),!0).forEach(function(t){(0,s.A)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function q(e){return{type:g,value:e}}function Q(e){return function(){var t=(0,i.A)(f().mark(function t(r){var n;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(r.select,r.resolveSelect,n=r.dispatch,!(Object.keys(e).length>0)){t.next=2;break}return t.next=1,U(e,v);case 1:t.sent;case 2:n({type:C,value:e});case 3:case"end":return t.stop()}},t)}));return function(e){return t.apply(this,arguments)}}()}function W(){return{type:y}}function X(e){return{type:S,value:e}}function Z(e){return function(){var t=(0,i.A)(f().mark(function t(r){var n;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(r.select,r.resolveSelect,n=r.dispatch,"object"!==(0,l.A)(e)){t.next=2;break}return t.next=1,V(e);case 1:t.sent;case 2:n({type:T,value:e});case 3:case"end":return t.stop()}},t)}));return function(e){return t.apply(this,arguments)}}()}function $(){return{type:k}}function ee(e){return{type:m,value:e}}function te(e){return function(){var t=(0,i.A)(f().mark(function t(r){var n;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r.select,r.resolveSelect,n=r.dispatch,t.next=1,U(e,b);case 1:n({type:A,value:e});case 2:case"end":return t.stop()}},t)}));return function(e){return t.apply(this,arguments)}}()}function re(){return{type:E}}function ne(e){return{type:w,value:e}}function oe(e){return function(){var t=(0,i.A)(f().mark(function t(r){var n;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(r.select,r.resolveSelect,n=r.dispatch,!(Object.keys(e).length>0)){t.next=2;break}return t.next=1,U(e,d);case 1:t.sent;case 2:n({type:j,value:e});case 3:case"end":return t.stop()}},t)}));return function(e){return t.apply(this,arguments)}}()}function ae(){return{type:L}}function ce(e){return{type:G,value:e}}function ue(e){return function(){var t=(0,i.A)(f().mark(function t(r){var n;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r.select,r.resolveSelect,n=r.dispatch,t.next=1,U(e,h);case 1:n({type:D,value:e});case 2:case"end":return t.stop()}},t)}));return function(e){return t.apply(this,arguments)}}()}function se(){return{type:x}}function le(e){return e&&(0,u.dispatch)("core/editor").editPost({meta:{_eb_meta_data:1}}),{type:P,value:e}}function ie(){return{type:B}}function pe(e,t){return{type:R,value:(0,s.A)({},e,z({},t))}}function fe(){return{type:N}}function Oe(e){return{type:J,value:e}}function ve(e){return function(){var t=(0,i.A)(f().mark(function t(r){var n;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(r.select,r.resolveSelect,n=r.dispatch,!(Object.keys(e).length>0)){t.next=2;break}return t.next=1,U(e,_);case 1:t.sent;case 2:n({type:F,value:e});case 3:case"end":return t.stop()}},t)}));return function(e){return t.apply(this,arguments)}}()}function be(){return{type:H}}function de(e){return e.globalColors}function he(e,t){return e.globalColors[t]}function _e(e){return e.customColors}function ge(e){return e.gradientColors}function Ce(e){return e.customGradientColors}function ye(e){return e.globalTypography}function Se(e){return e.blockDefaults}function Te(e,t){return e.blockDefaults[t]}function ke(e){return e.isSaving}function me(e,t){var r=e.blockData;return t?r[t]||{}:r}const Ae=EBControls;function Ee(){return M().then(function(e){return!(!e||"object"!==(0,l.A)(e))&&!(!e[v]||!(0,Ae.ebJsonStringCheck)(e[v])||"object"!==(0,l.A)(JSON.parse(e[v])))&&JSON.parse(e[v])})}function we(){return M().then(function(e){return!(!e||"object"!==(0,l.A)(e))&&!(!e[b]||!(0,Ae.ebJsonStringCheck)(e[b])||"object"!==(0,l.A)(JSON.parse(e[b])))&&JSON.parse(e[b])})}function je(){return M().then(function(e){return!(!e||"object"!==(0,l.A)(e))&&!(!e[d]||!(0,Ae.ebJsonStringCheck)(e[d])||"object"!==(0,l.A)(JSON.parse(e[d])))&&JSON.parse(e[d])})}function Le(){return M().then(function(e){return!(!e||"object"!==(0,l.A)(e))&&!(!e[h]||!(0,Ae.ebJsonStringCheck)(e[h])||"object"!==(0,l.A)(JSON.parse(e[h])))&&JSON.parse(e[h])})}function Ge(){return M().then(function(e){return!(!e||"object"!==(0,l.A)(e))&&!(!e[_]||!(0,Ae.ebJsonStringCheck)(e[_])||"object"!==(0,l.A)(JSON.parse(e[_])))&&JSON.parse(e[_])})}function De(){return Y().then(function(e){return!(!e||!(0,Ae.ebJsonStringCheck)(e)||"object"!==(0,l.A)(JSON.parse(e)))&&JSON.parse(e)})}var xe=f().mark(Fe),Pe=f().mark(He),Be=f().mark(Ie),Re=f().mark(Ue),Ne=f().mark(Me),Je=f().mark(Ve);function Fe(){var e;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,W();case 1:return e=t.sent,t.abrupt("return",q(e));case 2:case"end":return t.stop()}},xe)}function He(){var e;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,re();case 1:return e=t.sent,t.abrupt("return",ee(e));case 2:case"end":return t.stop()}},Pe)}function Ie(){var e;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,ae();case 1:return e=t.sent,t.abrupt("return",ne(e));case 2:case"end":return t.stop()}},Be)}function Ue(){var e;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,se();case 1:return e=t.sent,t.abrupt("return",ce(e));case 2:case"end":return t.stop()}},Re)}function Me(){var e;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,be();case 1:return e=t.sent,t.abrupt("return",Oe(e));case 2:case"end":return t.stop()}},Ne)}function Ve(){var e;return f().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=1,$();case 1:return e=t.sent,t.abrupt("return",X(e));case 2:case"end":return t.stop()}},Je)}var Ye=r(9394);function Ke(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ze(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ke(Object(r),!0).forEach(function(t){(0,s.A)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ke(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var qe=(0,u.createReduxStore)("essential-blocks",{reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:O,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case g:return ze(ze({},e),{},{globalColors:(0,Ye.A)(t.value)});case y:return e;case S:return ze(ze({},e),{},{blockDefaults:t.value});case T:return e;case m:return ze(ze({},e),{},{customColors:(0,Ye.A)(t.value)});case E:return e;case w:return ze(ze({},e),{},{gradientColors:(0,Ye.A)(t.value)});case L:return e;case G:return ze(ze({},e),{},{customGradientColors:(0,Ye.A)(t.value)});case C:case A:case j:case D:case F:case x:return e;case J:return ze(ze({},e),{},{globalTypography:ze({},t.value)});case H:return e;case P:return ze(ze({},e),{},{isSaving:t.value});case B:return e;case R:return ze(ze({},e),{},{blockData:ze(ze({},e.blockData),t.value)});default:return e}},actions:n,selectors:o,controls:a,resolvers:c});(0,u.register)(qe)}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}};return t[e](a,a.exports,n),a.exports}n.m=t,e=[],n.O=(t,r,o,a)=>{if(!r){var c=1/0;for(i=0;i<e.length;i++){for(var[r,o,a]=e[i],u=!0,s=0;s<r.length;s++)(!1&a||c>=a)&&Object.keys(n.O).every(e=>n.O[e](r[s]))?r.splice(s--,1):(u=!1,a<c&&(c=a));if(u){e.splice(i--,1);var l=o();void 0!==l&&(t=l)}}return t}a=a||0;for(var i=e.length;i>0&&e[i-1][2]>a;i--)e[i]=e[i-1];e[i]=[r,o,a]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.j=4104,(()=>{var e={4104:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var o,a,[c,u,s]=r,l=0;if(c.some(t=>0!==e[t])){for(o in u)n.o(u,o)&&(n.m[o]=u[o]);if(s)var i=s(n)}for(t&&t(r);l<c.length;l++)a=c[l],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(i)},r=globalThis.webpackChunkessential_blocks=globalThis.webpackChunkessential_blocks||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var o=n.O(void 0,[3935],()=>n(9096));o=n.O(o)})();