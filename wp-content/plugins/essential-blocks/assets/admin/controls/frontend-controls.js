(()=>{"use strict";var e={d:(t,n)=>{for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{EBGetIconClass:()=>c,EBGetIconType:()=>n,EBRenderIcon:()=>o,SetEqualHeightOfMultiColumnBlock:()=>a,sanitizeIconValue:()=>r});var n=function(e){return e.includes("fa-")?"fontawesome":"dashicon"},o=function(e,t,n){return"dashicon"===e?'<span class="dashicon dashicons '+n+" "+t+'"></span>':"fontawesome"===e?'<i class="'+n+" "+t+'"></i>':"Invalid icon type"},c=function(e){return e?e.includes("fa-")?e:"dashicon dashicons "+e:""},a=function(e){if(e){var t={};e.querySelectorAll(".eb-mcpt-feature-list").forEach(function(e){e.querySelectorAll(".eb-mcpt-cell").forEach(function(e,n){var o=e.offsetHeight;0!==o&&(void 0!==t[n]&&t[n]<o||void 0===t[n])&&(t[n]=o)})}),Object.keys(t).length>0&&e.querySelectorAll(".eb-mcpt-feature-list").forEach(function(e){e.querySelectorAll(".eb-mcpt-cell").forEach(function(e,n){e.style.height=t[n]+"px"})})}},r=function(e){return e?e.includes("fa-")?/^(fa[srb]?)\s+(fa-[a-z0-9-]+)(\s+fa-[a-z0-9-]+)*$/.test(e)?e:"":e.includes("dashicons-")&&/^dashicons-[a-z0-9-]+$/.test(e)?e:"":""};window.eb_frontend=t})();