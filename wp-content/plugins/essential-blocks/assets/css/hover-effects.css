.hover-effect figure img {
	display: block;
	max-width: 100%;
	min-height: 100%;
	position: relative;
}
.hover-effect figure figcaption {
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
	color: #fff;
	font-size: 1.25em;
	padding: 2em;
	text-transform: uppercase;
	/* Anchor will cover the whole item by default */
	/* For some effects it will show as a button */
}
.hover-effect figure figcaption::before,
.hover-effect figure figcaption::after {
	pointer-events: none;
}
.hover-effect figure figcaption,
.hover-effect figure figcaption > a {
	height: 100%;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
}
.hover-effect figure figcaption > a {
	font-size: 0;
	opacity: 0;
	text-indent: 200%;
	white-space: nowrap;
	z-index: 1000;
}
.hover-effect figure h2 {
	font-weight: 300;
	word-spacing: 0.25em;
}
.hover-effect figure h2 span {
	font-weight: 800;
}
.hover-effect figure h2,
.hover-effect figure p {
	margin: 0;
}
.hover-effect figure p {
	font-size: 68.5%;
	letter-spacing: 1px;
}

.hover-effect figure.effect-apollo {
	background: #528cb3;
}
.hover-effect figure.effect-apollo img {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: scale3d(1.05, 1.05, 1);
	-ms-transform: scale3d(1.05, 1.05, 1);
	-o-transform: scale3d(1.05, 1.05, 1);
	transform: scale3d(1.05, 1.05, 1);
	opacity: 0.95;
}
.hover-effect figure.effect-apollo figcaption::before {
	-webkit-transition: -webkit-transform 0.6s;
	-moz-transition: -moz-transform 0.6s;
	-o-transition: -o-transform 0.6s;
	transition: transform 0.6s;
	-moz-transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 45deg)
		translate3d(0, -110%, 0);
	-ms-transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 45deg)
		translate3d(0, -110%, 0);
	-o-transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 45deg)
		translate3d(0, -110%, 0);
	-webkit-transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 45deg)
		translate3d(0, -110%, 0);
	transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 45deg)
		translate3d(0, -110%, 0);
	background: rgba(82, 140, 179, 0.5);
	content: "";
	height: 100%;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
}
.hover-effect figure.effect-apollo p {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
	border-right: 4px solid #fff;
	bottom: 0;
	margin: 3em;
	max-width: 150px;
	opacity: 0;
	padding: 0 1em;
	position: absolute;
	right: 0;
	text-align: right;
}
.hover-effect figure.effect-apollo h2 {
	text-align: left;
}
.hover-effect figure.effect-apollo:hover img {
	-webkit-transform: scale3d(1, 1, 1);
	-ms-transform: scale3d(1, 1, 1);
	-o-transform: scale3d(1, 1, 1);
	transform: scale3d(1, 1, 1);
	opacity: 0.6;
}
.hover-effect figure.effect-apollo:hover figcaption::before {
	-moz-transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 45deg)
		translate3d(0, 110%, 0);
	-ms-transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 45deg)
		translate3d(0, 110%, 0);
	-o-transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 45deg)
		translate3d(0, 110%, 0);
	-webkit-transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 45deg)
		translate3d(0, 110%, 0);
	transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 45deg)
		translate3d(0, 110%, 0);
}
.hover-effect figure.effect-apollo:hover p {
	-webkit-transition-delay: 0.1s;
	transition-delay: 0.1s;
	opacity: 1;
}

.hover-effect figure.effect-bubba {
	background: #528cb3;
}
.hover-effect figure.effect-bubba img {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
	opacity: 0.7;
}
.hover-effect figure.effect-bubba figcaption::before,
.hover-effect figure.effect-bubba figcaption::after {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	bottom: 30px;
	content: "";
	left: 30px;
	opacity: 0;
	position: absolute;
	right: 30px;
	top: 30px;
}
.hover-effect figure.effect-bubba figcaption::before {
	-webkit-transform: scale(0, 1);
	-ms-transform: scale(0, 1);
	-o-transform: scale(0, 1);
	transform: scale(0, 1);
	border-bottom: 1px solid #fff;
	border-top: 1px solid #fff;
}
.hover-effect figure.effect-bubba figcaption::after {
	-webkit-transform: scale(1, 0);
	-ms-transform: scale(1, 0);
	-o-transform: scale(1, 0);
	transform: scale(1, 0);
	border-left: 1px solid #fff;
	border-right: 1px solid #fff;
}
.hover-effect figure.effect-bubba h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0, -20px, 0);
	transform: translate3d(0, -20px, 0);
	/* padding-top: 30%; */
}
.hover-effect figure.effect-bubba p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(0, 20px, 0);
	transform: translate3d(0, 20px, 0);
	opacity: 0;
	padding: 20px 2.5em;
}
.hover-effect figure.effect-bubba:hover img {
	opacity: 0.4;
}
.hover-effect figure.effect-bubba:hover figcaption::before,
.hover-effect figure.effect-bubba:hover figcaption::after {
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	-o-transform: scale(1);
	transform: scale(1);
	opacity: 1;
}
.hover-effect figure.effect-bubba:hover h2,
.hover-effect figure.effect-bubba:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-chico img {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: scale(1.12);
	-ms-transform: scale(1.12);
	-o-transform: scale(1.12);
	transform: scale(1.12);
}
.hover-effect figure.effect-chico figcaption {
	padding: 3em;
}
.hover-effect figure.effect-chico figcaption::before {
	-webkit-transform: scale(1.1);
	-ms-transform: scale(1.1);
	-o-transform: scale(1.1);
	transform: scale(1.1);
	border: 1px solid #fff;
	bottom: 30px;
	content: "";
	left: 30px;
	position: absolute;
	right: 30px;
	top: 30px;
}
.hover-effect figure.effect-chico figcaption::before,
.hover-effect figure.effect-chico p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	opacity: 0;
}
.hover-effect figure.effect-chico h2 {
	padding: 20% 0 20px;
}
.hover-effect figure.effect-chico p {
	-webkit-transform: scale(1.5);
	-ms-transform: scale(1.5);
	-o-transform: scale(1.5);
	transform: scale(1.5);
	margin: 0 auto;
	max-width: 200px;
}
.hover-effect figure.effect-chico:hover img {
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	-o-transform: scale(1);
	transform: scale(1);
	opacity: 0.5;
}
.hover-effect figure.effect-chico:hover figcaption::before,
.hover-effect figure.effect-chico:hover p {
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	-o-transform: scale(1);
	transform: scale(1);
	opacity: 1;
}

.hover-effect figure.effect-dexter {
	background: -webkit-linear-gradient(top, #111f28 0%, #528cb3 100%);
	background: linear-gradient(to bottom, #111f28 0%, #528cb3 100%);
}
.hover-effect figure.effect-dexter img {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
}
.hover-effect figure.effect-dexter figcaption {
	padding: 3em;
	text-align: left;
}
.hover-effect figure.effect-dexter figcaption::after {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0, -100%, 0);
	transform: translate3d(0, -100%, 0);
	border: 7px solid #fff;
	bottom: 30px;
	content: "";
	height: -webkit-calc(50% - 30px);
	height: calc(50% - 30px);
	left: 30px;
	position: absolute;
	right: 30px;
}
.hover-effect figure.effect-dexter p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(0, -100px, 0);
	transform: translate3d(0, -100px, 0);
	bottom: 60px;
	left: 60px;
	opacity: 0;
	position: absolute;
	right: 60px;
}
.hover-effect figure.effect-dexter:hover img {
	opacity: 0.4;
}
.hover-effect figure.effect-dexter:hover figcaption::after {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-dexter:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-duke {
	background: -webkit-linear-gradient(-45deg, black 0%, #528cb3 100%);
	background: linear-gradient(-45deg, black 0%, #528cb3 100%);
}
.hover-effect figure.effect-duke img,
.hover-effect figure.effect-duke p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
}
.hover-effect figure.effect-duke h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: scale3d(0.8, 0.8, 1);
	-ms-transform: scale3d(0.8, 0.8, 1);
	-o-transform: scale3d(0.8, 0.8, 1);
	transform: scale3d(0.8, 0.8, 1);
	-webkit-transform-origin: 50% 100%;
	-moz-transform-origin: 50% 100%;
	-ms-transform-origin: 50% 100%;
	transform-origin: 50% 100%;
}
.hover-effect figure.effect-duke p {
	-webkit-transform: scale3d(0.8, 0.8, 1);
	-ms-transform: scale3d(0.8, 0.8, 1);
	-o-transform: scale3d(0.8, 0.8, 1);
	transform: scale3d(0.8, 0.8, 1);
	-webkit-transform-origin: 50% -100%;
	-moz-transform-origin: 50% -100%;
	-ms-transform-origin: 50% -100%;
	transform-origin: 50% -100%;
	border: 2px solid #fff;
	bottom: 0;
	font-size: 90%;
	left: 0;
	margin: 20px;
	opacity: 0;
	padding: 30px;
	position: absolute;
	text-transform: none;
}
.hover-effect figure.effect-duke:hover img {
	-webkit-transform: scale3d(2, 2, 1);
	-ms-transform: scale3d(2, 2, 1);
	-o-transform: scale3d(2, 2, 1);
	transform: scale3d(2, 2, 1);
	opacity: 0.1;
}
.hover-effect figure.effect-duke:hover h2,
.hover-effect figure.effect-duke:hover p {
	-webkit-transform: scale3d(1, 1, 1);
	-ms-transform: scale3d(1, 1, 1);
	-o-transform: scale3d(1, 1, 1);
	transform: scale3d(1, 1, 1);
	opacity: 1;
}

.hover-effect figure.effect-goliath {
	background: #528cb3;
}
.hover-effect figure.effect-goliath img,
.hover-effect figure.effect-goliath h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
}
.hover-effect figure.effect-goliath img {
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
}
.hover-effect figure.effect-goliath h2,
.hover-effect figure.effect-goliath p {
	bottom: 0;
	left: 0;
	padding: 30px;
	position: absolute;
}
.hover-effect figure.effect-goliath p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(0, 50px, 0);
	transform: translate3d(0, 50px, 0);
	font-size: 90%;
	opacity: 0;
	text-transform: none;
}
.hover-effect figure.effect-goliath:hover img {
	-webkit-transform: translate3d(0, -80px, 0);
	transform: translate3d(0, -80px, 0);
}
.hover-effect figure.effect-goliath:hover h2 {
	-webkit-transform: translate3d(0, -100px, 0);
	transform: translate3d(0, -100px, 0);
}
.hover-effect figure.effect-goliath:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-hera {
	background: #528cb3;
}
.hover-effect figure.effect-hera h2 {
	font-size: 158.75%;
}
.hover-effect figure.effect-hera h2,
.hover-effect figure.effect-hera p {
	-webkit-transform-origin: 50%;
	-moz-transform-origin: 50%;
	-ms-transform-origin: 50%;
	transform-origin: 50%;
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(-50%, -50%, 0);
	transform: translate3d(-50%, -50%, 0);
	left: 50%;
	position: absolute;
	top: 50%;
}
.hover-effect figure.effect-hera figcaption::before {
	-moz-transform: translate3d(-50%, -50%, 0) rotate3d(0, 0, 1, -45deg)
		scale3d(0, 0, 1);
	-ms-transform: translate3d(-50%, -50%, 0) rotate3d(0, 0, 1, -45deg)
		scale3d(0, 0, 1);
	-o-transform: translate3d(-50%, -50%, 0) rotate3d(0, 0, 1, -45deg)
		scale3d(0, 0, 1);
	-webkit-transform: translate3d(-50%, -50%, 0) rotate3d(0, 0, 1, -45deg)
		scale3d(0, 0, 1);
	transform: translate3d(-50%, -50%, 0) rotate3d(0, 0, 1, -45deg)
		scale3d(0, 0, 1);
	-webkit-transform-origin: 50%;
	-moz-transform-origin: 50%;
	-ms-transform-origin: 50%;
	transform-origin: 50%;
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	border: 2px solid #fff;
	content: "";
	height: 200px;
	left: 50%;
	opacity: 0;
	position: absolute;
	top: 50%;
	width: 200px;
}
.hover-effect figure.effect-hera p {
	font-size: 121%;
	line-height: 2;
	text-transform: none;
	width: 100px;
}
.hover-effect figure.effect-hera p a {
	color: #fff;
	display: inline-block;
	width: 30px;
}
.hover-effect figure.effect-hera p a:hover,
.hover-effect figure.effect-hera p a:focus {
	opacity: 0.6;
}
.hover-effect figure.effect-hera p a i {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	display: inline-block;
	opacity: 0;
}
.hover-effect figure.effect-hera p a:first-child i {
	-webkit-transform: translate3d(-60px, -60px, 0);
	transform: translate3d(-60px, -60px, 0);
}
.hover-effect figure.effect-hera p a:nth-child(2) i {
	-webkit-transform: translate3d(60px, -60px, 0);
	transform: translate3d(60px, -60px, 0);
}
.hover-effect figure.effect-hera p a:nth-child(3) i {
	-webkit-transform: translate3d(-60px, 60px, 0);
	transform: translate3d(-60px, 60px, 0);
}
.hover-effect figure.effect-hera p a:nth-child(4) i {
	-webkit-transform: translate3d(60px, 60px, 0);
	transform: translate3d(60px, 60px, 0);
}
.hover-effect figure.effect-hera:hover figcaption::before {
	-moz-transform: translate3d(-50%, -50%, 0) rotate3d(0, 0, 1, -45deg)
		scale3d(1, 1, 1);
	-ms-transform: translate3d(-50%, -50%, 0) rotate3d(0, 0, 1, -45deg)
		scale3d(1, 1, 1);
	-o-transform: translate3d(-50%, -50%, 0) rotate3d(0, 0, 1, -45deg)
		scale3d(1, 1, 1);
	-webkit-transform: translate3d(-50%, -50%, 0) rotate3d(0, 0, 1, -45deg)
		scale3d(1, 1, 1);
	transform: translate3d(-50%, -50%, 0) rotate3d(0, 0, 1, -45deg)
		scale3d(1, 1, 1);
	opacity: 1;
}
.hover-effect figure.effect-hera:hover h2 {
	-moz-transform: translate3d(-50%, -50%, 0) scale3d(0.8, 0.8, 1);
	-ms-transform: translate3d(-50%, -50%, 0) scale3d(0.8, 0.8, 1);
	-o-transform: translate3d(-50%, -50%, 0) scale3d(0.8, 0.8, 1);
	-webkit-transform: translate3d(-50%, -50%, 0) scale3d(0.8, 0.8, 1);
	transform: translate3d(-50%, -50%, 0) scale3d(0.8, 0.8, 1);
	opacity: 0;
}
.hover-effect figure.effect-hera:hover p a i {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-honey {
	background: #528cb3;
}
.hover-effect figure.effect-honey img {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
	opacity: 0.9;
}
.hover-effect figure.effect-honey figcaption::before {
	-webkit-transform: translate3d(0, 10px, 0);
	transform: translate3d(0, 10px, 0);
	background: #fff;
	bottom: 0;
	content: "";
	height: 10px;
	left: 0;
	position: absolute;
	width: 100%;
}
.hover-effect figure.effect-honey h2 {
	-webkit-transform: translate3d(0, -30px, 0);
	transform: translate3d(0, -30px, 0);
	bottom: 0;
	left: 0;
	padding: 1em 1.5em;
	position: absolute;
	text-align: left;
	width: 100%;
}
.hover-effect figure.effect-honey h2 i {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(0, -30px, 0);
	transform: translate3d(0, -30px, 0);
	font-style: normal;
	opacity: 0;
}
.hover-effect figure.effect-honey figcaption::before,
.hover-effect figure.effect-honey h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
}
.hover-effect figure.effect-honey:hover img {
	opacity: 0.5;
}
.hover-effect figure.effect-honey:hover figcaption::before,
.hover-effect figure.effect-honey:hover h2,
.hover-effect figure.effect-honey:hover h2 i {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-jazz {
	background: -webkit-linear-gradient(-45deg, #528cb3 0%, #487fa4 100%);
	background: linear-gradient(-45deg, #528cb3 0%, #487fa4 100%);
}
.hover-effect figure.effect-jazz img {
	opacity: 0.9;
}
.hover-effect figure.effect-jazz figcaption::after,
.hover-effect figure.effect-jazz img,
.hover-effect figure.effect-jazz p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
}
.hover-effect figure.effect-jazz figcaption::after {
	-moz-transform: rotate3d(0, 0, 1, 45deg) scale(1, 0, 1);
	-ms-transform: rotate3d(0, 0, 1, 45deg) scale(1, 0, 1);
	-o-transform: rotate3d(0, 0, 1, 45deg) scale(1, 0, 1);
	-webkit-transform: rotate3d(0, 0, 1, 45deg) scale(1, 0, 1);
	transform: rotate3d(0, 0, 1, 45deg) scale(1, 0, 1);
	-webkit-transform-origin: 50% 50%;
	-moz-transform-origin: 50% 50%;
	-ms-transform-origin: 50% 50%;
	transform-origin: 50% 50%;
	border-bottom: 1px solid #fff;
	border-top: 1px solid #fff;
	content: "";
	height: 100%;
	left: 0;
	opacity: 0;
	position: absolute;
	top: 0;
	width: 100%;
}
.hover-effect figure.effect-jazz h2,
.hover-effect figure.effect-jazz p {
	-webkit-transform: scale3d(0.8, 0.8, 1);
	-ms-transform: scale3d(0.8, 0.8, 1);
	-o-transform: scale3d(0.8, 0.8, 1);
	transform: scale3d(0.8, 0.8, 1);
	opacity: 1;
}
.hover-effect figure.effect-jazz h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	/* padding-top: 26%; */
}
.hover-effect figure.effect-jazz p {
	font-size: 0.85em;
	opacity: 0;
	padding: 0.5em 2em;
	text-transform: none;
}
.hover-effect figure.effect-jazz:hover img {
	-webkit-transform: scale3d(1.05, 1.05, 1);
	-ms-transform: scale3d(1.05, 1.05, 1);
	-o-transform: scale3d(1.05, 1.05, 1);
	transform: scale3d(1.05, 1.05, 1);
	opacity: 0.7;
}
.hover-effect figure.effect-jazz:hover figcaption::after {
	-moz-transform: rotate3d(0, 0, 1, 45deg) scale(1, 1, 1);
	-ms-transform: rotate3d(0, 0, 1, 45deg) scale(1, 1, 1);
	-o-transform: rotate3d(0, 0, 1, 45deg) scale(1, 1, 1);
	-webkit-transform: rotate3d(0, 0, 1, 45deg) scale(1, 1, 1);
	transform: rotate3d(0, 0, 1, 45deg) scale(1, 1, 1);
	opacity: 1;
}
.hover-effect figure.effect-jazz:hover h2,
.hover-effect figure.effect-jazz:hover p {
	-webkit-transform: scale3d(1, 1, 1);
	-ms-transform: scale3d(1, 1, 1);
	-o-transform: scale3d(1, 1, 1);
	transform: scale3d(1, 1, 1);
	opacity: 1;
}

.hover-effect figure.effect-julia {
	background: #528cb3;
}
.hover-effect figure.effect-julia img {
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
	-webkit-transition: opacity 1s, -webkit-transform 1s;
	-moz-transition: opacity 1s, -moz-transform 1s;
	-o-transition: opacity 1s, -o-transform 1s;
	transition: opacity 1s, transform 1s;
	height: 400px;
	max-width: none;
}
.hover-effect figure.effect-julia figcaption {
	text-align: left;
}
.hover-effect figure.effect-julia h2 {
	padding: 0.5em 0;
	position: relative;
}
.hover-effect figure.effect-julia p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(-360px, 0, 0);
	transform: translate3d(-360px, 0, 0);
	background: rgba(255, 255, 255, 0.9);
	color: #2f3238;
	display: inline-block;
	font-size: 75%;
	font-weight: 500;
	margin: 0 0 0.25em;
	padding: 0.4em 1em;
	text-transform: none;
}
.hover-effect figure.effect-julia p:first-child {
	-webkit-transition-delay: 0.15s;
	transition-delay: 0.15s;
}
.hover-effect figure.effect-julia p:nth-of-type(2) {
	-webkit-transition-delay: 0.1s;
	transition-delay: 0.1s;
}
.hover-effect figure.effect-julia p:nth-of-type(3) {
	-webkit-transition-delay: 0.05s;
	transition-delay: 0.05s;
}
.hover-effect figure.effect-julia:hover img {
	-webkit-transform: scale3d(1.1, 1.1, 1.1);
	-ms-transform: scale3d(1.1, 1.1, 1.1);
	-o-transform: scale3d(1.1, 1.1, 1.1);
	transform: scale3d(1.1, 1.1, 1.1);
	opacity: 0.4;
}
.hover-effect figure.effect-julia:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}
.hover-effect figure.effect-julia:hover p:first-child {
	-webkit-transition-delay: 0s;
	transition-delay: 0s;
}
.hover-effect figure.effect-julia:hover p:nth-of-type(2) {
	-webkit-transition-delay: 0.05s;
	transition-delay: 0.05s;
}
.hover-effect figure.effect-julia:hover p:nth-of-type(3) {
	-webkit-transition-delay: 0.1s;
	transition-delay: 0.1s;
}

.hover-effect figure.effect-kira {
	background: #528cb3;
	text-align: left;
}
.hover-effect figure.effect-kira img {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
}
.hover-effect figure.effect-kira figcaption {
	z-index: 1;
}
.hover-effect figure.effect-kira figcaption::before {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-moz-transform: translate3d(0, 4em, 0) scale3d(1, 0.023, 1);
	-ms-transform: translate3d(0, 4em, 0) scale3d(1, 0.023, 1);
	-o-transform: translate3d(0, 4em, 0) scale3d(1, 0.023, 1);
	-webkit-transform: translate3d(0, 4em, 0) scale3d(1, 0.023, 1);
	transform: translate3d(0, 4em, 0) scale3d(1, 0.023, 1);
	-webkit-transform-origin: 50% 0;
	-moz-transform-origin: 50% 0;
	-ms-transform-origin: 50% 0;
	transform-origin: 50% 0;
	background: #fff;
	content: "";
	height: 3.5em;
	left: 2em;
	position: absolute;
	right: 2em;
	top: 0;
	z-index: -1;
}
.hover-effect figure.effect-kira p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(0, -10px, 0);
	transform: translate3d(0, -10px, 0);
	font-size: 100%;
	font-weight: 600;
	line-height: 1.5;
	opacity: 0;
	padding: 2.25em 0.5em;
}
.hover-effect figure.effect-kira p a {
	color: #101010;
	margin: 0 0.5em;
}
.hover-effect figure.effect-kira p a:hover,
.hover-effect figure.effect-kira p a:focus {
	opacity: 0.6;
}
.hover-effect figure.effect-kira:hover img {
	opacity: 0.5;
}
.hover-effect figure.effect-kira:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}
.hover-effect figure.effect-kira:hover figcaption::before {
	-moz-transform: translate3d(0, 5em, 0) scale3d(1, 1, 1);
	-ms-transform: translate3d(0, 5em, 0) scale3d(1, 1, 1);
	-o-transform: translate3d(0, 5em, 0) scale3d(1, 1, 1);
	-webkit-transform: translate3d(0, 5em, 0) scale3d(1, 1, 1);
	transform: translate3d(0, 5em, 0) scale3d(1, 1, 1);
	opacity: 0.7;
}

.hover-effect figure.effect-layla {
	background: #528cb3;
}
.hover-effect figure.effect-layla figcaption {
	padding: 3em;
}
.hover-effect figure.effect-layla figcaption::before,
.hover-effect figure.effect-layla figcaption::after {
	content: "";
	opacity: 0;
	position: absolute;
}
.hover-effect figure.effect-layla figcaption::before {
	-webkit-transform: scale(0, 1);
	-ms-transform: scale(0, 1);
	-o-transform: scale(0, 1);
	transform: scale(0, 1);
	-webkit-transform-origin: 0 0;
	-moz-transform-origin: 0 0;
	-ms-transform-origin: 0 0;
	transform-origin: 0 0;
	border-bottom: 1px solid #fff;
	border-top: 1px solid #fff;
	bottom: 50px;
	left: 30px;
	right: 30px;
	top: 50px;
}
.hover-effect figure.effect-layla figcaption::after {
	-webkit-transform: scale(1, 0);
	-ms-transform: scale(1, 0);
	-o-transform: scale(1, 0);
	transform: scale(1, 0);
	-webkit-transform-origin: 100%, 0;
	-moz-transform-origin: 100%, 0;
	-ms-transform-origin: 100%, 0;
	transform-origin: 100%, 0;
	border-left: 1px solid #fff;
	border-right: 1px solid #fff;
	bottom: 30px;
	left: 50px;
	right: 50px;
	top: 30px;
}
.hover-effect figure.effect-layla h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	/* padding-top: 26%; */
}
.hover-effect figure.effect-layla p {
	-webkit-transform: translate3d(0, -10px, 0);
	transform: translate3d(0, -10px, 0);
	opacity: 0;
	padding: 0.5em 2em;
	text-transform: none;
}
.hover-effect figure.effect-layla img,
.hover-effect figure.effect-layla h2 {
	-webkit-transform: translate3d(0, -30px, 0);
	transform: translate3d(0, -30px, 0);
}
.hover-effect figure.effect-layla img,
.hover-effect figure.effect-layla figcaption::before,
.hover-effect figure.effect-layla figcaption::after,
.hover-effect figure.effect-layla p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
}
.hover-effect figure.effect-layla:hover img {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 0.7;
}
.hover-effect figure.effect-layla:hover figcaption::before,
.hover-effect figure.effect-layla:hover figcaption::after {
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	-o-transform: scale(1);
	transform: scale(1);
	opacity: 1;
}
.hover-effect figure.effect-layla:hover h2,
.hover-effect figure.effect-layla:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}
.hover-effect figure.effect-layla:hover figcaption::after,
.hover-effect figure.effect-layla:hover h2,
.hover-effect figure.effect-layla:hover p,
.hover-effect figure.effect-layla:hover img {
	-webkit-transition-delay: 0.15s;
	transition-delay: 0.15s;
}

.hover-effect figure.effect-lexi {
	background: -webkit-linear-gradient(-45deg, black 0%, #528cb3 100%);
	background: linear-gradient(-45deg, black 0%, #528cb3 100%);
}
.hover-effect figure.effect-lexi img {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(10px, 10px, 0);
	transform: translate3d(10px, 10px, 0);
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
	margin: -10px 0 0 -10px;
	max-width: none;
	opacity: 0.9;
	width: -webkit-calc(100% + 10px);
	width: calc(100% + 10px);
}
.hover-effect figure.effect-lexi figcaption::before,
.hover-effect figure.effect-lexi p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
}
.hover-effect figure.effect-lexi figcaption::before {
	-webkit-transform: scale3d(0.5, 0.5, 1);
	-ms-transform: scale3d(0.5, 0.5, 1);
	-o-transform: scale3d(0.5, 0.5, 1);
	transform: scale3d(0.5, 0.5, 1);
	-webkit-transform-origin: 50% 50%;
	-moz-transform-origin: 50% 50%;
	-ms-transform-origin: 50% 50%;
	transform-origin: 50% 50%;
	border: 2px solid #fff;
	border-radius: 50%;
	bottom: -100px;
	box-shadow: 0 0 0 900px rgba(255, 255, 255, 0.2);
	content: "";
	height: 300px;
	opacity: 0;
	position: absolute;
	right: -100px;
	width: 300px;
}
.hover-effect figure.effect-lexi h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(5px, 5px, 0);
	transform: translate3d(5px, 5px, 0);
	text-align: left;
}
.hover-effect figure.effect-lexi p {
	-webkit-transform: translate3d(20px, 20px, 0);
	transform: translate3d(20px, 20px, 0);
	bottom: 0;
	opacity: 0;
	padding: 0 1.5em 1.5em 0;
	position: absolute;
	right: 0;
	text-align: right;
	width: 140px;
}
.hover-effect figure.effect-lexi:hover img {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 0.6;
}
.hover-effect figure.effect-lexi:hover figcaption::before {
	-webkit-transform: scale3d(1, 1, 1);
	-ms-transform: scale3d(1, 1, 1);
	-o-transform: scale3d(1, 1, 1);
	transform: scale3d(1, 1, 1);
	opacity: 1;
}
.hover-effect figure.effect-lexi:hover h2,
.hover-effect figure.effect-lexi:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-lily img {
	-webkit-transform: translate3d(-40px, 0, 0);
	transform: translate3d(-40px, 0, 0);
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	max-width: none;
	opacity: 0.7;
	width: -webkit-calc(100% + 50px);
	width: calc(100% + 50px);
}
.hover-effect figure.effect-lily figcaption {
	text-align: left;
}
.hover-effect figure.effect-lily figcaption > div {
	bottom: 0;
	height: 50%;
	left: 0;
	padding: 2em;
	position: absolute;
	width: 100%;
}
.hover-effect figure.effect-lily h2,
.hover-effect figure.effect-lily p {
	-webkit-transform: translate3d(0, 40px, 0);
	transform: translate3d(0, 40px, 0);
}
.hover-effect figure.effect-lily h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
}
.hover-effect figure.effect-lily p {
	-webkit-transition: opacity 0.2s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.2s, -moz-transform 0.35s;
	-o-transition: opacity 0.2s, -o-transform 0.35s;
	transition: opacity 0.2s, transform 0.35s;
	color: rgba(255, 255, 255, 0.8);
	opacity: 0;
}
.hover-effect figure.effect-lily:hover img,
.hover-effect figure.effect-lily:hover p {
	opacity: 1;
}
.hover-effect figure.effect-lily:hover img,
.hover-effect figure.effect-lily:hover h2,
.hover-effect figure.effect-lily:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-lily:hover p {
	-webkit-transition-delay: 0.05s;
	transition-delay: 0.05s;
	-webkit-transition-duration: 0.35s;
	transition-duration: 0.35s;
}

.hover-effect figure.effect-marley figcaption {
	text-align: right;
}
.hover-effect figure.effect-marley h2,
.hover-effect figure.effect-marley p {
	left: 30px;
	padding: 10px 0;
	position: absolute;
	right: 30px;
}
.hover-effect figure.effect-marley p {
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0);
	bottom: 30px;
	line-height: 1.5;
}
.hover-effect figure.effect-marley h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0, 20px, 0);
	transform: translate3d(0, 20px, 0);
	top: 30px;
}
.hover-effect figure.effect-marley h2::after {
	-webkit-transform: translate3d(0, 40px, 0);
	transform: translate3d(0, 40px, 0);
	background: #fff;
	content: "";
	height: 4px;
	left: 0;
	position: absolute;
	top: 100%;
	width: 100%;
}
.hover-effect figure.effect-marley h2::after,
.hover-effect figure.effect-marley p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	opacity: 0;
}
.hover-effect figure.effect-marley:hover h2 {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-marley:hover h2::after,
.hover-effect figure.effect-marley:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-milo {
	background: #528cb3;
}
.hover-effect figure.effect-milo img {
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-moz-transform: translate3d(-30px, 0, 0) scale(1.12);
	-ms-transform: translate3d(-30px, 0, 0) scale(1.12);
	-o-transform: translate3d(-30px, 0, 0) scale(1.12);
	-webkit-transform: translate3d(-30px, 0, 0) scale(1.12);
	transform: translate3d(-30px, 0, 0) scale(1.12);
	max-width: none;
	opacity: 1;
	width: -webkit-calc(100% + 60px);
	width: calc(100% + 60px);
}
.hover-effect figure.effect-milo h2 {
	bottom: 0;
	padding: 1em 1.2em;
	position: absolute;
	right: 0;
}
.hover-effect figure.effect-milo p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(-40px, 0, 0);
	transform: translate3d(-40px, 0, 0);
	border-right: 1px solid #fff;
	opacity: 0;
	padding: 0 10px 0 0;
	text-align: right;
	width: 50%;
}
.hover-effect figure.effect-milo:hover img {
	-moz-transform: translate3d(0, 0, 0) scale(1);
	-ms-transform: translate3d(0, 0, 0) scale(1);
	-o-transform: translate3d(0, 0, 0) scale(1);
	-webkit-transform: translate3d(0, 0, 0) scale(1);
	transform: translate3d(0, 0, 0) scale(1);
	opacity: 0.5;
}
.hover-effect figure.effect-milo:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-ming {
	background: #528cb3;
}
.hover-effect figure.effect-ming img {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
	opacity: 0.9;
}
.hover-effect figure.effect-ming figcaption::before {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: scale3d(1.4, 1.4, 1);
	-ms-transform: scale3d(1.4, 1.4, 1);
	-o-transform: scale3d(1.4, 1.4, 1);
	transform: scale3d(1.4, 1.4, 1);
	border: 2px solid #fff;
	bottom: 30px;
	box-shadow: 0 0 0 30px rgba(255, 255, 255, 0.2);
	content: "";
	left: 30px;
	opacity: 0;
	position: absolute;
	right: 30px;
	top: 30px;
}
.hover-effect figure.effect-ming h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	/* margin: 20% 0 10px; */
}
.hover-effect figure.effect-ming p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: scale(1.5);
	-ms-transform: scale(1.5);
	-o-transform: scale(1.5);
	transform: scale(1.5);
	opacity: 0;
	padding: 1em;
}
.hover-effect figure.effect-ming:hover h2 {
	-webkit-transform: scale(0.9);
	-ms-transform: scale(0.9);
	-o-transform: scale(0.9);
	transform: scale(0.9);
}
.hover-effect figure.effect-ming:hover figcaption::before,
.hover-effect figure.effect-ming:hover p {
	-webkit-transform: scale3d(1, 1, 1);
	-ms-transform: scale3d(1, 1, 1);
	-o-transform: scale3d(1, 1, 1);
	transform: scale3d(1, 1, 1);
	opacity: 1;
}
.hover-effect figure.effect-ming:hover figcaption {
	background-color: rgba(82, 140, 179, 0);
}
.hover-effect figure.effect-ming:hover img {
	opacity: 0.4;
}

.hover-effect figure.effect-moses {
	background: -webkit-linear-gradient(-45deg, #528cb3 0%, black 100%);
	background: linear-gradient(-45deg, #528cb3 0%, black 100%);
}
.hover-effect figure.effect-moses img {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
	opacity: 0.85;
}
.hover-effect figure.effect-moses h2,
.hover-effect figure.effect-moses p {
	border: 2px solid #fff;
	height: 50%;
	padding: 20px;
	width: 50%;
}
.hover-effect figure.effect-moses h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(10px, 10px, 0);
	transform: translate3d(10px, 10px, 0);
	height: 50%;
	padding: 20px;
	text-align: left;
	width: 50%;
}
.hover-effect figure.effect-moses p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(-50%, -50%, 0);
	transform: translate3d(-50%, -50%, 0);
	float: right;
	opacity: 0;
	padding: 20px;
	text-align: right;
}
.hover-effect figure.effect-moses:hover h2 {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-moses:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}
.hover-effect figure.effect-moses:hover img {
	opacity: 0.6;
}

.hover-effect figure.effect-oscar {
	background: -webkit-linear-gradient(
		45deg,
		black 0%,
		#528cb3 40%,
		#111f28 100%
	);
	background: linear-gradient(45deg, black 0%, #528cb3 40%, #111f28 100%);
}
.hover-effect figure.effect-oscar img {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
	opacity: 0.9;
}
.hover-effect figure.effect-oscar figcaption {
	-webkit-transition: background-color 0.35s;
	-moz-transition: background-color 0.35s;
	-o-transition: background-color 0.35s;
	transition: background-color 0.35s;
	background-color: rgba(82, 140, 179, 0.7);
	padding: 3em;
}
.hover-effect figure.effect-oscar figcaption::before {
	border: 1px solid #fff;
	bottom: 30px;
	content: "";
	left: 30px;
	position: absolute;
	right: 30px;
	top: 30px;
}
.hover-effect figure.effect-oscar h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0);
	/* margin: 20% 0 10px; */
}
.hover-effect figure.effect-oscar figcaption::before,
.hover-effect figure.effect-oscar p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: scale(0);
	-ms-transform: scale(0);
	-o-transform: scale(0);
	transform: scale(0);
	opacity: 0;
}
.hover-effect figure.effect-oscar:hover h2 {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-oscar:hover figcaption::before,
.hover-effect figure.effect-oscar:hover p {
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	-o-transform: scale(1);
	transform: scale(1);
	opacity: 1;
}
.hover-effect figure.effect-oscar:hover figcaption {
	background-color: rgba(82, 140, 179, 0);
}
.hover-effect figure.effect-oscar:hover img {
	opacity: 0.4;
}

.hover-effect figure.effect-phoebe {
	background: #528cb3;
}
.hover-effect figure.effect-phoebe img {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
	opacity: 0.85;
}
.hover-effect figure.effect-phoebe figcaption::before {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-moz-transform: scale3d(5, 2.5, 1) scale(0.999) rotate(0.01deg);
	-ms-transform: scale3d(5, 2.5, 1) scale(0.999) rotate(0.01deg);
	-o-transform: scale3d(5, 2.5, 1) scale(0.999) rotate(0.01deg);
	-webkit-transform: scale3d(5, 2.5, 1) scale(0.999) rotate(0.01deg);
	transform: scale3d(5, 2.5, 1) scale(0.999) rotate(0.01deg);
	-webkit-transform-origin: 50% 50%;
	-moz-transform-origin: 50% 50%;
	-ms-transform-origin: 50% 50%;
	transform-origin: 50% 50%;
	border-bottom: 20px solid rgba(255, 255, 255, 0.8);
	border-left: 240px solid rgba(255, 255, 255, 0.8);
	border-right: 240px solid rgba(255, 255, 255, 0.8);
	border-top: 305px solid rgba(255, 255, 255, 0);
	content: "";
	height: 100%;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
}
.hover-effect figure.effect-phoebe h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0, 40px, 0);
	transform: translate3d(0, 40px, 0);
	margin-top: 1em;
}
.hover-effect figure.effect-phoebe p a {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	color: #fff;
	display: inline-block;
	font-size: 140%;
	opacity: 0;
	position: relative;
}
.hover-effect figure.effect-phoebe p a:first-child {
	-webkit-transform: translate3d(-60px, -60px, 0);
	transform: translate3d(-60px, -60px, 0);
}
.hover-effect figure.effect-phoebe p a:nth-child(2) {
	-webkit-transform: translate3d(0, 60px, 0);
	transform: translate3d(0, 60px, 0);
}
.hover-effect figure.effect-phoebe p a:nth-child(3) {
	-webkit-transform: translate3d(60px, -60px, 0);
	transform: translate3d(60px, -60px, 0);
}
.hover-effect figure.effect-phoebe:hover img {
	opacity: 0.6;
}
.hover-effect figure.effect-phoebe:hover figcaption::before {
	-moz-transform: scale3d(1, 1, 1) scale(0.999) rotate(0.01deg);
	-ms-transform: scale3d(1, 1, 1) scale(0.999) rotate(0.01deg);
	-o-transform: scale3d(1, 1, 1) scale(0.999) rotate(0.01deg);
	-webkit-transform: scale3d(1, 1, 1) scale(0.999) rotate(0.01deg);
	transform: scale3d(1, 1, 1) scale(0.999) rotate(0.01deg);
	opacity: 0.6;
}
.hover-effect figure.effect-phoebe:hover h2 {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-phoebe:hover p a {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-romeo {
	-webkit-perspective: 1000px;
	-moz-perspective: 1000px;
	perspective: 1000px;
}
.hover-effect figure.effect-romeo img {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(0, 0, 300px);
	transform: translate3d(0, 0, 300px);
}
.hover-effect figure.effect-romeo figcaption::before,
.hover-effect figure.effect-romeo figcaption::after {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(-50%, -50%, 0);
	transform: translate3d(-50%, -50%, 0);
	background: #fff;
	content: "";
	height: 1px;
	left: 50%;
	position: absolute;
	top: 50%;
	width: 80%;
}
.hover-effect figure.effect-romeo h2,
.hover-effect figure.effect-romeo p {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	left: 0;
	position: absolute;
	top: 50%;
	width: 100%;
}
.hover-effect figure.effect-romeo h2 {
	-moz-transform: translate3d(0, -50%, 0) translate3d(0, -150%, 0);
	-ms-transform: translate3d(0, -50%, 0) translate3d(0, -150%, 0);
	-o-transform: translate3d(0, -50%, 0) translate3d(0, -150%, 0);
	-webkit-transform: translate3d(0, -50%, 0) translate3d(0, -150%, 0);
	transform: translate3d(0, -50%, 0) translate3d(0, -150%, 0);
}
.hover-effect figure.effect-romeo p {
	-moz-transform: translate3d(0, -50%, 0) translate(0, 150%, 0);
	-ms-transform: translate3d(0, -50%, 0) translate(0, 150%, 0);
	-o-transform: translate3d(0, -50%, 0) translate(0, 150%, 0);
	-webkit-transform: translate3d(0, -50%, 0) translate(0, 150%, 0);
	transform: translate3d(0, -50%, 0) translate(0, 150%, 0);
	padding: 0.25em 2em;
}
.hover-effect figure.effect-romeo:hover img {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 0.6;
}
.hover-effect figure.effect-romeo:hover figcaption::before {
	-moz-transform: translate3d(-50%, -50%, 0) rotate(45deg);
	-ms-transform: translate3d(-50%, -50%, 0) rotate(45deg);
	-o-transform: translate3d(-50%, -50%, 0) rotate(45deg);
	-webkit-transform: translate3d(-50%, -50%, 0) rotate(45deg);
	transform: translate3d(-50%, -50%, 0) rotate(45deg);
	opacity: 0.5;
}
.hover-effect figure.effect-romeo:hover figcaption::after {
	-moz-transform: translate3d(-50%, -50%, 0) rotate(-45deg);
	-ms-transform: translate3d(-50%, -50%, 0) rotate(-45deg);
	-o-transform: translate3d(-50%, -50%, 0) rotate(-45deg);
	-webkit-transform: translate3d(-50%, -50%, 0) rotate(-45deg);
	transform: translate3d(-50%, -50%, 0) rotate(-45deg);
	opacity: 0.5;
}
.hover-effect figure.effect-romeo:hover h2 {
	-moz-transform: translate3d(0, -50%, 0) translate3d(0, -100%, 0);
	-ms-transform: translate3d(0, -50%, 0) translate3d(0, -100%, 0);
	-o-transform: translate3d(0, -50%, 0) translate3d(0, -100%, 0);
	-webkit-transform: translate3d(0, -50%, 0) translate3d(0, -100%, 0);
	transform: translate3d(0, -50%, 0) translate3d(0, -100%, 0);
}
.hover-effect figure.effect-romeo:hover p {
	-moz-transform: translate3d(0, -50%, 0) translate3d(0, 100%, 0);
	-ms-transform: translate3d(0, -50%, 0) translate3d(0, 100%, 0);
	-o-transform: translate3d(0, -50%, 0) translate3d(0, 100%, 0);
	-webkit-transform: translate3d(0, -50%, 0) translate3d(0, 100%, 0);
	transform: translate3d(0, -50%, 0) translate3d(0, 100%, 0);
}

.hover-effect figure.effect-roxy {
	background: -webkit-linear-gradient(45deg, #528cb3 0%, black 100%);
	background: linear-gradient(45deg, #528cb3 0%, black 100%);
}
.hover-effect figure.effect-roxy img {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(-50px, 0, 0);
	transform: translate3d(-50px, 0, 0);
	max-width: none;
	width: -webkit-calc(100% + 60px);
	width: calc(100% + 60px);
}
.hover-effect figure.effect-roxy figcaption {
	padding: 3em;
	text-align: left;
}
.hover-effect figure.effect-roxy figcaption::before {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(-20px, 0, 0);
	transform: translate3d(-20px, 0, 0);
	border: 1px solid #fff;
	bottom: 30px;
	content: "";
	left: 30px;
	opacity: 0;
	position: absolute;
	right: 30px;
	top: 30px;
}
.hover-effect figure.effect-roxy h2 {
	padding: 30% 0 10px;
}
.hover-effect figure.effect-roxy p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(-10px, 0, 0);
	transform: translate3d(-10px, 0, 0);
	opacity: 0;
}
.hover-effect figure.effect-roxy:hover img {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 0.7;
}
.hover-effect figure.effect-roxy:hover figcaption::before,
.hover-effect figure.effect-roxy:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-ruby {
	background-color: #528cb3;
}
.hover-effect figure.effect-ruby img {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: scale(1.15);
	-ms-transform: scale(1.15);
	-o-transform: scale(1.15);
	transform: scale(1.15);
	opacity: 0.7;
}
.hover-effect figure.effect-ruby h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0, 20px, 0);
	transform: translate3d(0, 20px, 0);
	/* margin-top: 20%; */
}
.hover-effect figure.effect-ruby p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(0, 20px, 0);
	transform: translate3d(0, 20px, 0);
	-webkit-transform: scale(1.1);
	-ms-transform: scale(1.1);
	-o-transform: scale(1.1);
	transform: scale(1.1);
	border: 1px solid #fff;
	margin: 1em 0 0;
	opacity: 0;
	/* padding: 3em; */
	padding: 2em;
}
.hover-effect figure.effect-ruby:hover img {
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	-o-transform: scale(1);
	transform: scale(1);
	opacity: 0.5;
}
.hover-effect figure.effect-ruby:hover h2 {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-ruby:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	-o-transform: scale(1);
	transform: scale(1);
	opacity: 1;
}

.hover-effect figure.effect-sadie figcaption::before {
	-webkit-transform: translate3d(0, 50%, 0);
	transform: translate3d(0, 50%, 0);
	background: -webkit-linear-gradient(
		top,
		rgba(72, 76, 97, 0) 0%,
		rgba(72, 76, 97, 0.8) 75%
	);
	background: linear-gradient(
		to bottom,
		rgba(72, 76, 97, 0) 0%,
		rgba(72, 76, 97, 0.8) 75%
	);
	content: "";
	height: 100%;
	left: 0;
	opacity: 0;
	position: absolute;
	top: 0;
	width: 100%;
}
.hover-effect figure.effect-sadie h2 {
	-webkit-transition: -webkit-transform 0.35s, color 0.35s;
	-moz-transition: -moz-transform 0.35s, color 0.35s;
	-o-transition: -o-transform 0.35s, color 0.35s;
	transition: transform 0.35s, color 0.35s;
	-webkit-transform: translate3d(0, -50%, 0);
	transform: translate3d(0, -50%, 0);
	color: #528cb3;
	left: 0;
	position: absolute;
	top: 50%;
	width: 100%;
}
.hover-effect figure.effect-sadie figcaption::before,
.hover-effect figure.effect-sadie p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
}
.hover-effect figure.effect-sadie p {
	-webkit-transform: translate3d(0, 10px, 0);
	transform: translate3d(0, 10px, 0);
	bottom: 0;
	left: 0;
	opacity: 0;
	padding: 2em;
	position: absolute;
	width: 100%;
}
.hover-effect figure.effect-sadie:hover h2 {
	-moz-transform: translate3d(0, -50%, 0) translate3d(0, -40px, 0);
	-ms-transform: translate3d(0, -50%, 0) translate3d(0, -40px, 0);
	-o-transform: translate3d(0, -50%, 0) translate3d(0, -40px, 0);
	-webkit-transform: translate3d(0, -50%, 0) translate3d(0, -40px, 0);
	transform: translate3d(0, -50%, 0) translate3d(0, -40px, 0);
	color: #fff;
}
.hover-effect figure.effect-sadie:hover figcaption::before,
.hover-effect figure.effect-sadie:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-sarah {
	background: #528cb3;
}
.hover-effect figure.effect-sarah img {
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(-10px, 0, 0);
	transform: translate3d(-10px, 0, 0);
	max-width: none;
	width: -webkit-calc(100% + 20px);
	width: calc(100% + 20px);
}
.hover-effect figure.effect-sarah:hover img {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 0.4;
}
.hover-effect figure.effect-sarah:hover h2::after {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-sarah:hover p {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}
.hover-effect figure.effect-sarah figcaption {
	text-align: left;
}
.hover-effect figure.effect-sarah h2 {
	overflow: hidden;
	padding: 0.5em 0;
	position: relative;
}
.hover-effect figure.effect-sarah h2::after {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(-100%, 0, 0);
	transform: translate3d(-100%, 0, 0);
	background: #fff;
	bottom: 0;
	content: "";
	height: 3px;
	left: 0;
	position: absolute;
	width: 100%;
}
.hover-effect figure.effect-sarah p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(100%, 0, 0);
	transform: translate3d(100%, 0, 0);
	opacity: 0;
	padding: 1em 0;
}

.hover-effect figure.effect-selena {
	background: #528cb3;
}
.hover-effect figure.effect-selena img {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform-origin: 50% 50%;
	-moz-transform-origin: 50% 50%;
	-ms-transform-origin: 50% 50%;
	transform-origin: 50% 50%;
	opacity: 0.95;
}
.hover-effect figure.effect-selena h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0, 20px, 0);
	transform: translate3d(0, 20px, 0);
}
.hover-effect figure.effect-selena p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-moz-transform: perspective(1000px) rotate3d(1, 0, 0, 90deg);
	-ms-transform: perspective(1000px) rotate3d(1, 0, 0, 90deg);
	-o-transform: perspective(1000px) rotate3d(1, 0, 0, 90deg);
	-webkit-transform: perspective(1000px) rotate3d(1, 0, 0, 90deg);
	transform: perspective(1000px) rotate3d(1, 0, 0, 90deg);
	-webkit-transform-origin: 50% 0%;
	-moz-transform-origin: 50% 0%;
	-ms-transform-origin: 50% 0%;
	transform-origin: 50% 0%;
	opacity: 0;
}
.hover-effect figure.effect-selena:hover img {
	-webkit-transform: scale3d(0.95, 0.95, 1);
	-ms-transform: scale3d(0.95, 0.95, 1);
	-o-transform: scale3d(0.95, 0.95, 1);
	transform: scale3d(0.95, 0.95, 1);
}
.hover-effect figure.effect-selena:hover h2 {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-selena:hover p {
	-moz-transform: perspective(1000px) rotate3d(1, 0, 0, 0);
	-ms-transform: perspective(1000px) rotate3d(1, 0, 0, 0);
	-o-transform: perspective(1000px) rotate3d(1, 0, 0, 0);
	-webkit-transform: perspective(1000px) rotate3d(1, 0, 0, 0);
	transform: perspective(1000px) rotate3d(1, 0, 0, 0);
	opacity: 1;
}

.hover-effect figure.effect-steve {
	background: #528cb3;
	overflow: visible;
	z-index: auto;
}
.hover-effect figure.effect-steve:before,
.hover-effect figure.effect-steve h2:before {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
	background: #528cb3;
	content: "";
	height: 100%;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
	z-index: -1;
}
.hover-effect figure.effect-steve:before {
	box-shadow: 0 3px 30px rgba(82, 140, 179, 0.8);
	opacity: 0;
}
.hover-effect figure.effect-steve figcaption {
	z-index: 1;
}
.hover-effect figure.effect-steve img {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-moz-transform: perspective(1000px) translate3d(0, 0, 0);
	-ms-transform: perspective(1000px) translate3d(0, 0, 0);
	-o-transform: perspective(1000px) translate3d(0, 0, 0);
	-webkit-transform: perspective(1000px) translate3d(0, 0, 0);
	transform: perspective(1000px) translate3d(0, 0, 0);
	opacity: 1;
}
.hover-effect figure.effect-steve h2,
.hover-effect figure.effect-steve p {
	background: #fff;
	color: #528cb3;
}
.hover-effect figure.effect-steve h2 {
	margin-top: 2em;
	padding: 0.25em;
	position: relative;
}
.hover-effect figure.effect-steve h2:before {
	box-shadow: 0 1px 10px rgba(82, 140, 179, 0.5);
}
.hover-effect figure.effect-steve p {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: scale3d(0.9, 0.9, 1);
	-ms-transform: scale3d(0.9, 0.9, 1);
	-o-transform: scale3d(0.9, 0.9, 1);
	transform: scale3d(0.9, 0.9, 1);
	font-weight: 800;
	margin-top: 1em;
	opacity: 0;
	padding: 0.5em;
}
.hover-effect figure.effect-steve:hover:before {
	opacity: 1;
}
.hover-effect figure.effect-steve:hover img {
	-moz-transform: perspective(1000px) translate(0, 0, 21px);
	-ms-transform: perspective(1000px) translate(0, 0, 21px);
	-o-transform: perspective(1000px) translate(0, 0, 21px);
	-webkit-transform: perspective(1000px) translate(0, 0, 21px);
	transform: perspective(1000px) translate(0, 0, 21px);
}
.hover-effect figure.effect-steve:hover h2:before {
	opacity: 0;
}
.hover-effect figure.effect-steve:hover p {
	-webkit-transform: scale3d(1, 1, 1);
	-ms-transform: scale3d(1, 1, 1);
	-o-transform: scale3d(1, 1, 1);
	transform: scale3d(1, 1, 1);
	opacity: 1;
}

.hover-effect figure.effect-terry {
	background: #528cb3;
}
.hover-effect figure.effect-terry figcaption {
	padding: 1em;
}
.hover-effect figure.effect-terry figcaption::before,
.hover-effect figure.effect-terry figcaption::after {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	border-color: #fff;
	border-style: solid;
	content: "";
	height: 200%;
	position: absolute;
	width: 200%;
}
.hover-effect figure.effect-terry figcaption::before {
	-webkit-transform: translate3d(70px, 60px, 0);
	transform: translate3d(70px, 60px, 0);
	border-width: 0 70px 60px 0;
	bottom: 0;
	right: 0;
}
.hover-effect figure.effect-terry figcaption::after {
	-webkit-transform: translate3d(-15px, -15px, 0);
	transform: translate3d(-15px, -15px, 0);
	border-width: 15px 0 0 15px;
	left: 0;
	top: 0;
}
.hover-effect figure.effect-terry img,
.hover-effect figure.effect-terry p a {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
}
.hover-effect figure.effect-terry img {
	opacity: 0.85;
}
.hover-effect figure.effect-terry h2 {
	-webkit-transition: -webkit-transform 0.35s, color 0.35s;
	-moz-transition: -moz-transform 0.35s, color 0.35s;
	-o-transition: -o-transform 0.35s, color 0.35s;
	transition: transform 0.35s, color 0.35s;
	-webkit-transform: translate3d(100%, 0, 0);
	transform: translate3d(100%, 0, 0);
	bottom: 0;
	left: 0;
	padding: 0.4em 10px;
	position: absolute;
	width: 50%;
}
@media screen and (max-width: 920px) {
	.hover-effect figure.effect-terry h2 {
		font-size: 120%;
		padding: 0.75em 10px;
	}
}
.hover-effect figure.effect-terry p {
	clear: both;
	float: right;
	font-size: 111%;
	text-align: left;
	text-transform: none;
}
.hover-effect figure.effect-terry p a {
	-webkit-transform: translate3d(90px, 0, 0);
	transform: translate3d(90px, 0, 0);
	display: block;
	margin-bottom: 1em;
	opacity: 0;
}
.hover-effect figure.effect-terry:hover figcaption::before,
.hover-effect figure.effect-terry:hover figcaption::after {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-terry:hover img {
	opacity: 0.6;
}
.hover-effect figure.effect-terry:hover h2 {
	color: #528cb3;
}
.hover-effect figure.effect-terry:hover h2,
.hover-effect figure.effect-terry:hover p a {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-terry:hover p a {
	opacity: 1;
}
.hover-effect figure.effect-terry:hover a:first-child {
	-webkit-transition-delay: 0.025s;
	transition-delay: 0.025s;
}
.hover-effect figure.effect-terry:hover a:nth-child(2) {
	-webkit-transition-delay: 0.05s;
	transition-delay: 0.05s;
}
.hover-effect figure.effect-terry:hover a:nth-child(3) {
	-webkit-transition-delay: 0.075s;
	transition-delay: 0.075s;
}
.hover-effect figure.effect-terry:hover a:nth-child(4) {
	-webkit-transition-delay: 0.1s;
	transition-delay: 0.1s;
}

.hover-effect figure.effect-winston {
	background: #528cb3;
	text-align: left;
}
.hover-effect figure.effect-winston img {
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
	-webkit-transition: opacity 0.45s;
	-moz-transition: opacity 0.45s;
	-o-transition: opacity 0.45s;
	transition: opacity 0.45s;
}
.hover-effect figure.effect-winston figcaption::before {
	-webkit-transition: opacity 0.45s, -webkit-transform 0.45s;
	-moz-transition: opacity 0.45s, -moz-transform 0.45s;
	-o-transition: opacity 0.45s, -o-transform 0.45s;
	transition: opacity 0.45s, transform 0.45s;
	-webkit-transform: rotate3d(0, 0, 1, 45deg);
	-ms-transform: rotate3d(0, 0, 1, 45deg);
	-o-transform: rotate3d(0, 0, 1, 45deg);
	transform: rotate3d(0, 0, 1, 45deg);
	-webkit-transform-origin: 0 100%;
	-moz-transform-origin: 0 100%;
	-ms-transform-origin: 0 100%;
	transform-origin: 0 100%;
	border-bottom: 300px solid #fff;
	border-left: 425px solid transparent;
	content: "";
	height: 100%;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
}
.hover-effect figure.effect-winston h2 {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0, 20px, 0);
	transform: translate3d(0, 20px, 0);
}
.hover-effect figure.effect-winston p {
	bottom: 0;
	padding: 0 1.5em 7% 0;
	position: absolute;
	right: 0;
}
.hover-effect figure.effect-winston p a i {
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	-moz-transition: opacity 0.35s, -moz-transform 0.35s;
	-o-transition: opacity 0.35s, -o-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	-webkit-transform: translate3d(0, 50px, 0);
	transform: translate3d(0, 50px, 0);
	opacity: 0;
}
.hover-effect figure.effect-winston a {
	color: #528cb3;
	font-size: 170%;
	margin: 0 10px;
}
.hover-effect figure.effect-winston a:hover,
.hover-effect figure.effect-winston a:focus {
	color: #111f28;
}
.hover-effect figure.effect-winston:hover img {
	opacity: 0.6;
}
.hover-effect figure.effect-winston:hover h2 {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-winston:hover figcaption::before {
	-webkit-transform: rotate3d(0, 0, 1, 20deg);
	-ms-transform: rotate3d(0, 0, 1, 20deg);
	-o-transform: rotate3d(0, 0, 1, 20deg);
	transform: rotate3d(0, 0, 1, 20deg);
	opacity: 0.7;
}
.hover-effect figure.effect-winston:hover p i {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	opacity: 1;
}
.hover-effect figure.effect-winston:hover p:nth-child(3) i {
	-webkit-transition-delay: 0.05s;
	transition-delay: 0.05s;
}
.hover-effect figure.effect-winston:hover p:nth-child(2) i {
	-webkit-transition-delay: 0.1s;
	transition-delay: 0.1s;
}
.hover-effect figure.effect-winston:hover p:first-child i {
	-webkit-transition-delay: 0.15s;
	transition-delay: 0.15s;
}

.hover-effect figure.effect-zoe figcaption {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0);
	background: #fff;
	bottom: 0;
	color: #528cb3;
	height: 3.75em;
	padding: 1em;
	top: auto;
}
.hover-effect figure.effect-zoe h2 {
	display: inline-block;
	float: left;
}
.hover-effect figure.effect-zoe p.icon-links a {
	color: #528cb3;
	float: right;
	font-size: 1.4em;
}
.hover-effect figure.effect-zoe p.icon-links a:hover,
.hover-effect figure.effect-zoe p.icon-links a:focus {
	color: #528cb3;
}
.hover-effect figure.effect-zoe p.icon-links a span::before {
	display: inline-block;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	padding: 8px 10px;
	speak: none;
}
.hover-effect figure.effect-zoe p.description {
	-webkit-transition: opacity 0.35s;
	-moz-transition: opacity 0.35s;
	-o-transition: opacity 0.35s;
	transition: opacity 0.35s;
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility: hidden;
	backface-visibility: hidden;
	/* Fix for Chrome 37.0.2062.120 (Mac) */
	bottom: 8em;
	color: #fff;
	font-size: 90%;
	opacity: 0;
	padding: 2em;
	position: absolute;
	text-transform: none;
}
.hover-effect figure.effect-zoe h2,
.hover-effect figure.effect-zoe p.icon-links a {
	-webkit-transition: -webkit-transform 0.35s;
	-moz-transition: -moz-transform 0.35s;
	-o-transition: -o-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0, 200%, 0);
	transform: translate3d(0, 200%, 0);
}
.hover-effect figure.effect-zoe:hover p.description {
	opacity: 1;
}
.hover-effect figure.effect-zoe:hover figcaption,
.hover-effect figure.effect-zoe:hover h2,
.hover-effect figure.effect-zoe:hover p.icon-links a {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.hover-effect figure.effect-zoe:hover h2 {
	-webkit-transition-delay: 0.05s;
	transition-delay: 0.05s;
}
.hover-effect figure.effect-zoe:hover p.icon-links a:nth-child(3) {
	-webkit-transition-delay: 0.1s;
	transition-delay: 0.1s;
}
.hover-effect figure.effect-zoe:hover p.icon-links a:nth-child(2) {
	-webkit-transition-delay: 0.15s;
	transition-delay: 0.15s;
}
.hover-effect figure.effect-zoe:hover p.icon-links a:first-child {
	-webkit-transition-delay: 0.2s;
	transition-delay: 0.2s;
}
