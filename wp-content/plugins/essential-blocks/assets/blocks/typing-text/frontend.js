jQuery(document).ready(function(t){t(".eb-typed-wrapper").each(function(){var e=t(this).find(".eb-typed-content").data("type-speed"),a=t(this).find(".eb-typed-content").data("start-delay"),d=t(this).find(".eb-typed-content").data("smart-backspace"),n=t(this).find(".eb-typed-content").data("back-speed"),i=t(this).find(".eb-typed-content").data("back-delay"),s=t(this).find(".eb-typed-content").data("fade"),c=t(this).find(".eb-typed-content").data("fade-delay"),p=t(this).find(".eb-typed-content").data("loop"),y=t(this).find(".eb-typed-content").data("cursor"),o=[];t(this).find(".eb-typed-text").each(function(){o.push(this.innerHTML)}),t(this).find(".eb-typed-view").each(function(){new Typed(this,{strings:o,typeSpeed:e,startDelay:a,smartBackspace:d,backSpeed:n,backDelay:i,fadeOut:s,fadeOutDelay:c,loop:p,showCursor:y})})})});