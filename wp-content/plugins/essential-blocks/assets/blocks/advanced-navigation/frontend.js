(()=>{function e(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=Array(n);t<n;t++)i[t]=e[t];return i}window.addEventListener("DOMContentLoaded",function(){var n,t=function(n,t){var i="undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(!i){if(Array.isArray(n)||(i=function(n,t){if(n){if("string"==typeof n)return e(n,t);var i={}.toString.call(n).slice(8,-1);return"Object"===i&&n.constructor&&(i=n.constructor.name),"Map"===i||"Set"===i?Array.from(n):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?e(n,t):void 0}}(n))||t&&n&&"number"==typeof n.length){i&&(n=i);var o=0,r=function(){};return{s:r,n:function(){return o>=n.length?{done:!0}:{done:!1,value:n[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,l=!0,c=!1;return{s:function(){i=i.call(n)},n:function(){var e=i.next();return l=e.done,e},e:function(e){c=!0,a=e},f:function(){try{l||null==i.return||i.return()}finally{if(c)throw a}}}}(document.querySelectorAll(".eb-advanced-navigation-wrapper"));try{var i=function(){var e=n.value,t=e.querySelectorAll(".wp-block-navigation-item__content");window.matchMedia("(max-width: 767px)"),t.forEach(function(n){null==n||n.addEventListener("click",function(t){if(""!==n.hash){t.preventDefault(),e.querySelector(".wp-block-navigation__responsive-container").classList.remove("is-menu-open","has-modal-open");var i=this.getAttribute("href");window.open(i,"_self")}})}),t.forEach(function(e){e.closest(".wp-block-navigation-item").classList.remove("active"),e.addEventListener("click",function(n){t.forEach(function(e){e.closest(".wp-block-navigation-item").classList.remove("current-menu-item")}),""!==e.hash&&e.closest(".wp-block-navigation-item").classList.add("current-menu-item")})}),document.querySelectorAll(".eb-advanced-navigation-wrapper").forEach(function(e){e.querySelectorAll(".wp-block-navigation.is-responsive > li.wp-block-navigation-submenu").forEach(function(e){e.querySelector("a"),e.innerHTML+='<span class="eb-menu-indicator"></span>'}),e.querySelectorAll(".wp-block-navigation.is-responsive > li ul li.wp-block-navigation-submenu").forEach(function(e){e.querySelector("a"),e.innerHTML+='<span class="eb-menu-indicator"></span>'}),window.addEventListener("load",function(){e.querySelectorAll(".eb-menu-indicator").forEach(function(e){null==e||e.addEventListener("click",function(e){e.preventDefault();var n=this.parentNode.querySelector("a").getBoundingClientRect().height-25;this.style.top=n/2+"px"})})}),e.addEventListener("click",function(e){if(e.target.classList.contains("eb-menu-indicator")){e.preventDefault(),e.target.classList.toggle("eb-menu-indicator-open");var n=e.target.previousElementSibling;"none"===n.style.display||""===n.style.display?n.style.display="block":n.style.display="none"}})});var i=e.querySelectorAll(".eb-menu-indicator"),o=e.querySelector(".wp-block-navigation__responsive-container-open"),r=e.querySelector(".wp-block-navigation__responsive-container-close");null==o||o.addEventListener("click",function(e){null==i||i.forEach(function(e){e.style.display="block"})}),null==r||r.addEventListener("click",function(e){null==i||i.forEach(function(e){e.style.display="none"})})};for(t.s();!(n=t.n()).done;)i()}catch(e){t.e(e)}finally{t.f()}})})();