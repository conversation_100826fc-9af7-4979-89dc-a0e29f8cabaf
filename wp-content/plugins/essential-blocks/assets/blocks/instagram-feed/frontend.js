(()=>{function n(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=Array(t);e<t;e++)r[e]=n[e];return r}window.addEventListener("DOMContentLoaded",function(t){var e=document.getElementsByClassName("eb-instagram__gallery");setTimeout(function(){var t,r=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return n(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var o=0,a=function(){};return{s:a,n:function(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}},e:function(n){throw n},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,l=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var n=r.next();return l=n.done,n},e:function(n){u=!0,i=n},f:function(){try{l||null==r.return||r.return()}finally{if(u)throw i}}}}(e);try{var o=function(){var n=t.value;imagesLoaded(n,function(){new Isotope(n,{itemSelector:".instagram__gallery__col",percentPosition:!0,masonry:{columnWidth:".instagram__gallery__col"}})})};for(r.s();!(t=r.n()).done;)o()}catch(n){r.e(n)}finally{r.f()}},1e3)})})();