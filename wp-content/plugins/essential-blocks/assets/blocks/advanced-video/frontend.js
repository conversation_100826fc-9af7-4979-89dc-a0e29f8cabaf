(()=>{"use strict";var t,e={1609:t=>{t.exports=window.React},4773:(t,e,a)=>{a(1609);var n=a(8468),r=a(3554);function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=Array(e);a<e;a++)n[a]=t[a];return n}var o=function(t){var e,a,n=t.wrapper,i=t._autoplay,o=t._muted,s=n.getAttribute("data-url"),l=(n.getAttribute("data-option"),"true"===n.getAttribute("data-controls")),c="true"===n.getAttribute("data-loop"),d=o||"true"===n.getAttribute("data-muted"),u=i||"true"===n.getAttribute("data-playing"),y="true"===n.getAttribute("data-overlay"),m=n.getAttribute("data-light"),p="true"===n.getAttribute("data-customPlayIcon"),b=n.getAttribute("data-playicon"),v=n.getAttribute("data-customPlayIconType"),f=n.getAttribute("data-customPlayIconLib"),g=n.getAttribute("data-download");e=!(!0!==y||!m)&&m,a=1==y&&1==p&&"image"==v?React.createElement("img",{src:b}):1==y&&1==p&&"icon"==v?React.createElement("i",{class:f}):null;var w=null!==n.closest(".lightbox"),h={file:{attributes:{controlsList:"false"===g?"nodownload":"",playsInline:!w,webkitPlaysinline:w?void 0:"true",playsinline:w?void 0:"true"}},youtube:{playerVars:{playsinline:w?0:1,modestbranding:1,origin:window.location.origin}},vimeo:{playerOptions:{playsinline:!w,dnt:!0}}};return React.createElement(React.Fragment,null,React.createElement(r.A,{className:"eb-react-player",width:"100%",height:"100%",url:s,controls:l,loop:c,muted:d,playing:u,light:e,playIcon:a,volume:.5,config:h,playsinline:!w}))};document.addEventListener("DOMContentLoaded",function(t){var e,a=function(t,e){var a="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!a){if(Array.isArray(t)||(a=function(t,e){if(t){if("string"==typeof t)return i(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?i(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){a&&(t=a);var n=0,r=function(){};return{s:r,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,l=!1;return{s:function(){a=a.call(t)},n:function(){var t=a.next();return s=t.done,t},e:function(t){l=!0,o=t},f:function(){try{s||null==a.return||a.return()}finally{if(l)throw o}}}}(document.getElementsByClassName("eb-advanced-video-wrapper"));try{var r,s,l,c,d,u,y,m=function(){var t=e.value,a=t.getElementsByClassName("eb-player-option")[0],i="true"===a.getAttribute("data-overlay"),m=(a.getAttribute("data-id"),a.getAttribute("data-option"));if(i&&a.addEventListener("click",function(t){(0,n.render)(React.createElement(o,{wrapper:a,_autoplay:!0,_muted:!1}),a)}),(0,n.render)(React.createElement(o,{wrapper:a}),a),"eb-sticky"===m){(r=document.querySelector(".eb-player-option.eb-sticky")).innerHeight,s=document.querySelector(".eb-react-player").offsetHeight,l=a.closest(".eb-sticky").closest(".wp-block-essential-blocks-advanced-video").offsetTop,c=a.querySelector(".eb-sticky-video-close"),(d=document.createElement("span")).innerHTML="&times;",d.setAttribute("class","eb-sticky-video-close");var p="stuck-out",b=0,v=a.getAttribute("data-stickyVisibility"),f=a.getAttribute("data-stickyVisibilityTAB"),g=a.getAttribute("data-stickyVisibilityMOB");window.matchMedia("(min-width: 1025px)").matches&&"hidden"!=v&&document.addEventListener("scroll",function(){var t=s+l+200,e=s+l+320,a=window.pageYOffset;a<=t?r.classList.remove(p):window.scrollY>t?window.scrollY>e?(r.classList.remove("stuck-out"),r.classList.add("stuck"),null==c&&r.prepend(d),d.style.display="inline",d.addEventListener("click",function(){r.classList.remove("eb-sticky")})):(a<b&&r.classList.contains("stuck")&&(r.classList.remove("stuck"),r.classList.add(p)),b=a):r.classList.remove("stuck-out")}),window.matchMedia("(min-width: 768px) and (max-width: 1024px)").matches&&"hidden"!=f&&document.addEventListener("scroll",function(){var t=s+l+200,e=s+l+320,a=window.pageYOffset;a<=t?r.classList.remove(p):window.scrollY>t?window.scrollY>e?(r.classList.remove("stuck-out"),r.classList.add("stuck"),null==c&&r.prepend(d),d.style.display="inline",d.addEventListener("click",function(){r.classList.remove("eb-sticky")})):(a<b&&r.classList.contains("stuck")&&(r.classList.remove("stuck"),r.classList.add(p)),b=a):r.classList.remove("stuck-out")}),window.matchMedia("(max-width: 767px)").matches&&"hidden"!=g&&document.addEventListener("scroll",function(){var t=s+l+200,e=s+l+320,a=window.pageYOffset;a<=t?r.classList.remove(p):window.scrollY>t?window.scrollY>e?(r.classList.remove("stuck-out"),r.classList.add("stuck"),null==c&&r.prepend(d),d.style.display="inline",d.addEventListener("click",function(){r.classList.remove("eb-sticky")})):(a<b&&r.classList.contains("stuck")&&(r.classList.remove("stuck"),r.classList.add(p)),b=a):r.classList.remove("stuck-out")})}if(t.classList.contains("lightbox")){var w=t.getAttribute("data-id"),h=document.querySelector('[data-id="'.concat(w,'"]')),k="#eb-modal-"+w,L="#myBtn-"+w;h.querySelector(k),u=h.querySelector(L),y=h.getElementsByClassName("eb-modal-close")[0],u.onclick=function(){var t="#eb-modal-"+this.id.substring(6),e=document.querySelector(t),r=e.getElementsByClassName("lightbox")[0].getAttribute("data-autoplay");e.style.display="block","true"===r&&(0,n.render)(React.createElement(o,{wrapper:a,_autoplay:!0,_muted:!1}),a)},y.onclick=function(){var t="#eb-modal-"+this.id.substring(6);document.querySelector(t).style.display="none",(0,n.render)(React.createElement(o,{wrapper:a,_autoplay:!1}),a)},window.onclick=function(t){t.target.classList.contains("eb-modal-player")&&(document.getElementById(t.target.id).style.display="none",(0,n.render)(React.createElement(o,{wrapper:a,_autoplay:!1}),a))}}};for(a.s();!(e=a.n()).done;)m()}catch(t){a.e(t)}finally{a.f()}})},8468:t=>{t.exports=window.wp.element}},a={};function n(t){var r=a[t];if(void 0!==r)return r.exports;var i=a[t]={id:t,loaded:!1,exports:{}};return e[t].call(i.exports,i,i.exports,n),i.loaded=!0,i.exports}n.m=e,n.amdO={},t=[],n.O=(e,a,r,i)=>{if(!a){var o=1/0;for(d=0;d<t.length;d++){for(var[a,r,i]=t[d],s=!0,l=0;l<a.length;l++)(!1&i||o>=i)&&Object.keys(n.O).every(t=>n.O[t](a[l]))?a.splice(l--,1):(s=!1,i<o&&(o=i));if(s){t.splice(d--,1);var c=r();void 0!==c&&(e=c)}}return e}i=i||0;for(var d=t.length;d>0&&t[d-1][2]>i;d--)t[d]=t[d-1];t[d]=[a,r,i]},n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var a in e)n.o(e,a)&&!n.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.nmd=t=>(t.paths=[],t.children||(t.children=[]),t),n.j=6695,(()=>{var t={6695:0};n.O.j=e=>0===t[e];var e=(e,a)=>{var r,i,[o,s,l]=a,c=0;if(o.some(e=>0!==t[e])){for(r in s)n.o(s,r)&&(n.m[r]=s[r]);if(l)var d=l(n)}for(e&&e(a);c<o.length;c++)i=o[c],n.o(t,i)&&t[i]&&t[i][0](),t[i]=0;return n.O(d)},a=globalThis.webpackChunkessential_blocks=globalThis.webpackChunkessential_blocks||[];a.forEach(e.bind(null,0)),a.push=e.bind(null,a.push.bind(a))})(),n.nc=void 0;var r=n.O(void 0,[7916],()=>n(4773));r=n.O(r)})();