(()=>{"use strict";var t={n:e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return t.d(r,{a:r}),r},d:(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const e=window.wp.domReady;t.n(e)()(function(){var t=document.querySelectorAll(".eb-counter-wrapper .eb-counter");if(t){var e=new IntersectionObserver(function(t,e){t.forEach(function(t){var n,a,o,i,u,c,d,s;t.isIntersecting&&(n=t.target,a=+n.getAttribute("data-target"),o=+n.getAttribute("data-duration"),i=+n.getAttribute("data-startValue"),u=n.getAttribute("data-isShowSeparator"),c=n.getAttribute("data-separator"),s=(a-(d=i<a?i:0))/o*53,function t(){d+=s,n.innerText=r(Math.floor(d),u,c),d<a?setTimeout(t,53):n.innerText=r(a,u,c)}(),e.unobserve(t.target))})},{threshold:.25});t.forEach(function(t){e.observe(t)})}function r(t,e,r){return"true"===e?t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,r):t.toString()}})})();