(()=>{function e(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}document.addEventListener("DOMContentLoaded",function(t){var r,n,o=!1;null!==(r=window)&&void 0!==r&&r.eb_frontend&&"function"===(null===(n=window)||void 0===n?void 0:n.eb_frontend.SetEqualHeightOfMultiColumnBlock)&&(o=window.eb_frontend.SetEqualHeightOfMultiColumnBlock);var a=document.querySelectorAll(".eb-toggle-wrapper");if(a){var l,c=function(t,r){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,r){if(t){if("string"==typeof t)return e(t,r);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?e(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){n&&(t=n);var o=0,a=function(){};return{s:a,n:function(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,c=!0,i=!1;return{s:function(){n=n.call(t)},n:function(){var e=n.next();return c=e.done,e},e:function(e){i=!0,l=e},f:function(){try{c||null==n.return||n.return()}finally{if(i)throw l}}}}(a);try{var i=function(){var e=l.value,t=e.querySelector(".eb-toggle-switch > input"),r=e.querySelector(".eb-toggle-primary-label"),n=e.querySelector(".eb-toggle-secondary-label"),a=e.querySelector(".eb-toggle-primary-label-text"),c=e.querySelector(".eb-toggle-secondary-label-text"),i=e.querySelector(".eb-toggle-slider"),s=e.querySelector(".eb-toggle-controller"),u=e.querySelector(".eb-toggle-content"),d=e.querySelector(".eb-toggle-seperator"),g=e.getAttribute("data-init-content"),y=e.getAttribute("data-size"),b=e.getAttribute("data-switch-style"),f=e.getAttribute("data-primary-color"),v=e.getAttribute("data-secondary-color"),m=e.getAttribute("data-active-color"),p=e.getAttribute("data-bg-color"),h=e.getAttribute("data-active-bg"),S="inherit",x="inherit",L=e.querySelector(".eb-text-switch-label").getAttribute("for"),w=e.querySelector("#".concat(L));function k(){"toggle"!==b&&(r.style.background=h||x,n.style.background=p||S),A()}function q(){"toggle"!==b&&(n.style.background=h||x,r.style.background=p||S),E()}function A(){t.checked=!1,e.classList.contains("eb-toggle-secondary")&&(e.classList.remove("eb-toggle-secondary"),e.classList.add("eb-toggle-primary"));var l=u.children[1];l.classList.remove("active"),l.classList.add("inactive");var i=u.children[0];i.classList.remove("inactive"),setTimeout(function(){var e=i.querySelector(".eb-mcpt-wrap");o&&e&&o(e)},10),i.classList.add("active"),n.style.color=v,r.style.color=m||f,"toggle"===b&&(c.style.color=v,a.style.color=m||f),"text"!==b&&(s.style.transform="translateX(0px)")}function E(){t.checked=!0,e.classList.contains("eb-toggle-primary")&&(e.classList.remove("eb-toggle-primary"),e.classList.add("eb-toggle-secondary"));var l=u.children[0],i=u.children[1];l.classList.remove("active"),l.classList.add("inactive"),i.classList.remove("inactive"),setTimeout(function(){var e=i.querySelector(".eb-mcpt-wrap");o&&e&&o(e)},10),i.classList.add("active"),r.style.color=f,n.style.color=m,"toggle"===b&&(a.style.color=f,c.style.color=m||v),"text"!==b&&(s.style.transform=function(){switch(y){case"s":return"translateX(22px)";case"m":return"translateX(26px)";case"l":return"translateX(36px)";case"xl":return"translateX(42px)"}}())}"primary"===g?A():(w.checked=!0,E()),w.addEventListener("change",function(){this.checked?E():A()}),"text"===b&&(r.style.background=p||S,n.style.background=p||S,d.style.background=p||S),r.addEventListener("click",k),n.addEventListener("click",q),"text"===b&&("primary"===g?k():q()),"rounded"==b&&(i.style.borderRadius="21px",s.style.borderRadius=function(){switch(y){case"s":return"10px";case"m":return"13px";case"l":return"18px";case"xl":return"21px"}}()),"primary"===g?A():E(),t.addEventListener("change",function(){this.checked?E():A()})};for(c.s();!(l=c.n()).done;)i()}catch(e){c.e(e)}finally{c.f()}}})})();