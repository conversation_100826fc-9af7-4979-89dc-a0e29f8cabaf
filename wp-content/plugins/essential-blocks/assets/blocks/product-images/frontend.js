(()=>{"use strict";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var o in r)e.o(r,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:r[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.wp.domReady;function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}e.n(t)()(function(){var e=document.querySelectorAll(".eb-product-images-wrapper");if(e){var t,a=function(e,t){var o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!o){if(Array.isArray(e)||(o=function(e,t){if(e){if("string"==typeof e)return r(e,t);var o={}.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?r(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){o&&(e=o);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,l=!0,u=!1;return{s:function(){o=o.call(e)},n:function(){var e=o.next();return l=e.done,e},e:function(e){u=!0,a=e},f:function(){try{l||null==o.return||o.return()}finally{if(u)throw a}}}}(e);try{var l=function(){var e=t.value,r=e.getAttribute("data-id"),a=e.querySelector(".eb-product-image_slider"),l=a.querySelectorAll(".eb-product-image_slider-body-item"),u=a.querySelector(".eb-product-image_slider-body"),c=a.querySelector(".eb-product-image_slider-footer"),s=JSON.parse(e.getAttribute("data-settings")),d=JSON.parse(e.getAttribute("data-nav-settings")),f="true"===e.getAttribute("data-enable-zoom");if(jQuery(u).slick({slidesToShow:1,slidesToScroll:1,arrows:!1,fade:!0,asNavFor:".".concat(r," .eb-product-image_slider-footer"),adaptiveHeight:null==s?void 0:s.adaptiveHeight}),jQuery(c).slick({slidesToShow:4,slidesToScroll:1,asNavFor:".".concat(r," .eb-product-image_slider-body"),dots:!1,arrows:!(null!=d&&d.disableNavArrow),focusOnSelect:!0,vertical:"left"==(null==d?void 0:d.galleryPosition)||"right"==(null==d?void 0:d.galleryPosition),centerMode:!0,centerPadding:"top"==(null==d?void 0:d.galleryPosition)||"bottom"==(null==d?void 0:d.galleryPosition)?"60px":"",prevArrow:'<button type="button" class="slick-prev"><i class="fa-solid fa-chevron-left"></i></button>',nextArrow:'<button type="button" class="slick-next"><i class="fa-solid fa-chevron-right"></i></button>'}),l.length>0){o(a,l,f);var g=n(l);u.insertAdjacentHTML("afterBegin",'<a href="#" class="eb-product-gallery__trigger" id="'+r+'-trigger">🔍</a>'),document.getElementById(r+"-trigger").addEventListener("click",function(){i(g)})}};for(a.s();!(t=a.n()).done;)l()}catch(e){a.e(e)}finally{a.f()}}});var o=function(e,t,r){var o=e.clientWidth;t.forEach(function(e,t){var n=e.querySelector("img");r&&n.getAttribute("data-large_image_width")>o&&jQuery(e).zoom()})},n=function(e){var t=[];return e.forEach(function(e){var r=e.querySelector("img");r&&t.push(r.src)}),t},i=function(e){var t=new FsLightbox;t.props.sources=e,t.open()}})();