(()=>{"use strict";var e,t={3399:(e,t,n)=>{var r=n(2284),a=n(9394);function o(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return i(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){l=!0,o=e},f:function(){try{s||null==n.return||n.return()}finally{if(l)throw o}}}}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function s(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;if(e&&!function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!e)return!1;var n=e.getBoundingClientRect();return n.top>=0-t&&n.left>=0&&n.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&n.right<=(window.innerWidth||document.documentElement.clientWidth)}(e,t)){var n=e.getBoundingClientRect().top+window.pageYOffset-t;window.scrollTo({top:n,behavior:"smooth"})}}function l(e,t,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];e.classList.add("show");var a=e.getAttribute("data-confirmation-type"),o=c(e.getAttribute("data-success")),i=c(e.getAttribute("data-error")),s=c(e.getAttribute("data-redirect-url"));n&&"redirect"===a&&(s&&s.length>0?window.location.replace(s):window.location.replace("/"));var l="success",u="error";t&&0!==t.length||e.classList.remove("show",l,u),n&&(e.classList.remove(u),e.classList.add(l)),n||(e.classList.remove(l),e.classList.add(u)),e.innerHTML=r?n?o:i:t}function c(e){return e&&"string"!==!(0,r.A)(e)&&0!==e.length?decodeURIComponent(e.replace(/\+/g," ")):e}window.addEventListener("DOMContentLoaded",function(){var e,t=o(document.querySelectorAll(".eb-form-submit-button"));try{var n=function(){var t=e.value;t.onclick=function(e){e.preventDefault();var n=t.getAttribute("data-id"),i=(t.getAttribute("data-form-id"),"eb-field-wrapper"),c="eb-validation-error",u="eb-form-validation",d="g-recaptcha-response",f=document.querySelector(".".concat(n));if(f){var v,m=f.getElementsByClassName("eb-form")[0],p=f.getElementsByClassName("form-nonce"),b=new FormData(m),y=new FormData;y.append("action","eb_form_submit"),y.append("form_id",n),y.append("nonce",null===(v=p[0])||void 0===v?void 0:v.value);var g={};(0,a.A)(m.elements).forEach(function(e){e.name&&(g[e.name]="")}),(0,a.A)(b.entries()).forEach(function(e){g[e[0]].length>0&&e[1].length>0&&Object.keys(g).includes(e[0])?"string"==typeof g[e[0]]?g[e[0]]=[g[e[0]],e[1]]:"object"===(0,r.A)(g[e[0]])&&(g[e[0]]=[].concat((0,a.A)(g[e[0]]),[e[1]])):g[e[0]]=e[1]}),delete g["form-nonce"];var h=!0;if(Object.keys(g).includes(d)&&0===g[d].length){var w=f.querySelector('[name="'.concat(d,'"]'));if(w){var L=w.closest(".".concat(i));L&&(L.classList.add(c),$errorHtml='<div class="'.concat(u,"\">reCAPTCHA isn't verified!</div>"))}h=!1}if(h)return t.classList.add("loading"),y.append("form_data",JSON.stringify(g)),fetch(EssentialBlocksLocalize.ajax_url,{method:"POST",body:y}).then(function(e){return e.text()}).then(function(e){var n=JSON.parse(e);t.classList.remove("loading"),function(e,t,n){var r,a=o(e.querySelectorAll(".".concat(t)));try{for(a.s();!(r=a.n()).done;)r.value.classList.remove(n)}catch(e){a.e(e)}finally{a.f()}}(f,i,c);var a=f.getElementsByClassName("eb_form_submit_response")[0];if(n.success){"string"==typeof n.data&&l(a,n.data,!0),m.reset(),t.classList.add("success"),setTimeout(function(){s(f,50)},100),setTimeout(function(){a.classList.remove("show","success"),a.innerHTML="",t.classList.remove("success")},5e3);var v=f.querySelector(".eb-multistep-form");if(v){var p=v.querySelectorAll(".wp-block-essential-blocks-pro-form-multistep-wrapper:not(.wp-block-essential-blocks-pro-form-multistep-wrapper .wp-block-essential-blocks-pro-form-multistep-wrapper)");if(p.length>0){p.forEach(function(e){e.style.display="none"}),p[0].style.display="block";var b=v.querySelector("#ebFormPrevBtn"),y=v.querySelector("#ebFormNextBtn"),g=v.querySelector(".eb-form-submit-button");v.querySelectorAll(".step-nav-item").forEach(function(e,t){0===t?e.classList.add("active"):e.classList.remove("active")}),b&&(b.style.display="none"),y&&(y.style.display="inline-flex"),g&&(g.style.display="none");var h=new CustomEvent("eb-multistep-form-reset",{detail:{formWrapper:f}});document.dispatchEvent(h),setTimeout(function(){s(f,50)},100)}}}!n.success&&n.data&&(t.classList.remove("success"),"string"==typeof n.data?(l(a,n.data,!1,!1),setTimeout(function(){s(f,50)},100)):"object"===(0,r.A)(n.data)&&(n.data.message&&"string"==typeof n.data.message&&(l(a,n.data.message,!1,!1),setTimeout(function(){s(f,50)},100)),n.data.validation&&"object"===(0,r.A)(n.data.validation)&&Object.keys(n.data.validation).map(function(e){var t;e===d&&(t=f,grecaptcha.reset(t));var r=f.querySelector('[name="'.concat(e,'"]')).closest(".".concat(i));r.classList.add(c);var a=r.querySelector(".".concat(u));a&&(a.innerHTML=n.data.validation[e])})))}).catch(function(e){return console.log(e)})}}};for(t.s();!(e=t.n()).done;)n()}catch(e){t.e(e)}finally{t.f()}var i,c=o(document.getElementsByClassName("eb-form-wrapper"));try{for(c.s();!(i=c.n()).done;){var u,d=o(i.value.getElementsByClassName("eb-field-input"));try{var f=function(){var e,t=u.value;t.value&&(null===(e=t.nextSibling)||void 0===e||e.classList.add("active")),t.addEventListener("focus",function(e){var n,r;null!==(n=t.nextSibling)&&void 0!==n&&n.classList.contains("active")||null===(r=t.nextSibling)||void 0===r||r.classList.add("active")}),t.addEventListener("blur",function(){var e;t.value||null===(e=t.nextSibling)||void 0===e||e.classList.remove("active")})};for(d.s();!(u=d.n()).done;)f()}catch(e){d.e(e)}finally{d.f()}}}catch(e){c.e(e)}finally{c.f()}})}},n={};function r(e){var a=n[e];if(void 0!==a)return a.exports;var o=n[e]={exports:{}};return t[e](o,o.exports,r),o.exports}r.m=t,e=[],r.O=(t,n,a,o)=>{if(!n){var i=1/0;for(u=0;u<e.length;u++){for(var[n,a,o]=e[u],s=!0,l=0;l<n.length;l++)(!1&o||i>=o)&&Object.keys(r.O).every(e=>r.O[e](n[l]))?n.splice(l--,1):(s=!1,o<i&&(i=o));if(s){e.splice(u--,1);var c=a();void 0!==c&&(t=c)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[n,a,o]},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.j=2385,(()=>{var e={2385:0};r.O.j=t=>0===e[t];var t=(t,n)=>{var a,o,[i,s,l]=n,c=0;if(i.some(t=>0!==e[t])){for(a in s)r.o(s,a)&&(r.m[a]=s[a]);if(l)var u=l(r)}for(t&&t(n);c<i.length;c++)o=i[c],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(u)},n=globalThis.webpackChunkessential_blocks=globalThis.webpackChunkessential_blocks||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var a=r.O(void 0,[3935],()=>r(3399));a=r.O(a)})();