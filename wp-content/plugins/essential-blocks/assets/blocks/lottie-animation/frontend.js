(()=>{"use strict";var e,t={1661:(e,t,o)=>{var n=o(4467),r=o(3453),a=o(9891),l=o.n(a),i=o(1259);function s(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),o.push.apply(o,n)}return o}function p(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?s(Object(o),!0).forEach(function(t){(0,n.A)(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):s(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}window.addEventListener("DOMContentLoaded",function(e){var t,o=function(e,t){var o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!o){if(Array.isArray(e)||(o=function(e,t){if(e){if("string"==typeof e)return d(e,t);var o={}.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?d(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){o&&(e=o);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,l=!0,i=!1;return{s:function(){o=o.call(e)},n:function(){var e=o.next();return l=e.done,e},e:function(e){i=!0,a=e},f:function(){try{l||null==o.return||o.return()}finally{if(i)throw a}}}}(document.querySelectorAll(".eb-lottie-animation-wrapper"));try{var n,a=function(){var e=t.value,o=e.querySelector(".eb-lottie-animation");if(o&&"canvas"===o.tagName.toLowerCase()){var a=document.createElement("div");if(Array.from(o.attributes).forEach(function(e){a.setAttribute(e.name,e.value)}),a.style.cssText=o.style.cssText,a.className=o.className,o.parentNode.insertBefore(a,o),o._eventListeners){for(var s=function(){var e=(0,r.A)(c[d],2),t=e[0];e[1].forEach(function(e){a.addEventListener(t,e)})},d=0,c=Object.entries(o._eventListeners);d<c.length;d++)s();a._eventListeners=o._eventListeners}o.parentNode.removeChild(o),o=a}var f=JSON.parse(e.getAttribute("data-settings")),u=f.lottieURl.toLowerCase().endsWith(".lottie");if(o.innerHTML="",u){var v=document.createElement("canvas");o.appendChild(v);var g=new i.j({speed:f.speed,mode:f.reverse?"reverse":"forward",autoplay:"none"===f.playOn||"viewport"===f.playOn,loop:(!f.loop||!(f.loopCount>0||f.delay>0))&&f.loop,canvas:v,src:f.lottieURl});if(g.addEventListener("load",function(e){var t=g.totalFrames-1,o=Math.floor(f.startSegment/100*t),n=Math.floor(f.endSegment/100*t);g.setSegment(o,n)}),"scroll"!==f.playOn&&(f.loop&&f.loopCount>0&&(n=0,g.addEventListener("complete",function(){(n+=1)<f.loopCount?setTimeout(function(){g.play()},1e3*f.delay):g.stop()})),f.loop&&f.delay>0&&0===f.loopCount&&g.addEventListener("complete",function(){setTimeout(function(){g.play()},1e3*f.delay)})),"none"===f.playOn&&g.setRenderConfig(p(p({},g.renderConfig),{},{freezeOnOffscreen:!1})),"hover"===f.playOn&&(g.setRenderConfig(p(p({},g.renderConfig),{},{freezeOnOffscreen:!1})),v.addEventListener("mouseenter",function(){g&&"function"==typeof g.play&&g.play()}),v.addEventListener("mouseleave",function(){g&&"function"==typeof g.stop&&g.stop()})),"click"===f.playOn&&(g.setRenderConfig(p(p({},g.renderConfig),{},{freezeOnOffscreen:!1})),v.addEventListener("click",function(){n=0,g.play()})),"scroll"===f.playOn){var y=f.scrollTopPoint/100,m=f.scrollBottomPoint/100;window.addEventListener("scroll",function(){var e,t=v.getBoundingClientRect(),o=window.innerHeight,n=o*y,r=o*(1-m),a=t.top,l=t.bottom;if(f.scrollTopPoint>0||f.scrollBottomPoint>0)if(l>n&&a<r){e=(r-a)/(r-n),e=Math.min(Math.max(e,0),1);var i=Math.floor(e*g.totalFrames);g.setFrame(i)}else g.setFrame(0);else{e=Math.min(Math.max((o-t.top)/(t.height+o),0),1);var s=Math.floor(e*g.totalFrames);g.setFrame(s)}})}"viewport"===f.playOn&&new IntersectionObserver(function(e){e.forEach(function(e){e.isIntersecting?(g.setRenderConfig(p(p({},g.renderConfig),{},{freezeOnOffscreen:!1})),n=0,g.unfreeze(),g.play()):g.freeze()})},{threshold:.2}).observe(e)}else{var h=l().loadAnimation({container:o,renderer:"svg",loop:f.loop&&0===f.loopCount&&0===f.delay&&!T,autoplay:!1,path:f.lottieURl,rendererSettings:{preserveAspectRatio:"xMidYMid slice"}});h&&"function"==typeof h.setSpeed&&h.setSpeed(f.speed||1),h.setDirection(f.reverse?-1:1);var S=0,O=1,b=0,T=!1;h.addEventListener("DOMLoaded",function(){if(b=h.totalFrames-1,S=Math.floor(f.startSegment/100*b),O=Math.floor(f.endSegment/100*b),(T=0!==S||O!==b)?h.setLoop(!1):f.loop&&0===f.loopCount&&0===f.delay&&h.setLoop(!0),f.reverse?h.goToAndStop(O,!0):h.goToAndStop(S,!0),"none"===f.playOn&&(T?f.reverse?h.playSegments([O,S],!0):h.playSegments([S,O],!0):h.play()),"scroll"!==f.playOn){if(f.loop&&f.loopCount>0){var t=0;h.addEventListener("complete",function(){(t+=1)<f.loopCount?setTimeout(function(){f.reverse?T?(h.goToAndStop(O,!0),h.playSegments([O,S],!0)):(h.goToAndStop(b,!0),h.play()):T?(h.goToAndStop(S,!0),h.playSegments([S,O],!0)):h.goToAndPlay(0,!0)},1e3*f.delay):h.stop()})}f.loop&&f.delay>0&&0===f.loopCount&&h.addEventListener("complete",function(){setTimeout(function(){f.reverse?T?(h.goToAndStop(O,!0),h.playSegments([O,S],!0)):(h.goToAndStop(b,!0),h.play()):T?(h.goToAndStop(S,!0),h.playSegments([S,O],!0)):(h.goToAndStop(0,!0),h.play())},1e3*f.delay)}),f.loop&&0===f.delay&&0===f.loopCount&&T&&h.addEventListener("complete",function(){f.reverse?(h.goToAndStop(O,!0),h.playSegments([O,S],!0)):(h.goToAndStop(S,!0),h.playSegments([S,O],!0))})}if("hover"===f.playOn)o.addEventListener("mouseenter",function(){T?f.reverse?(h.goToAndStop(O,!0),h.playSegments([O,S],!0)):(h.goToAndStop(S,!0),h.playSegments([S,O],!0)):(f.reverse?h.goToAndStop(b,!0):h.goToAndStop(0,!0),h.play())}),o.addEventListener("mouseleave",function(){h.pause(),f.reverse?h.goToAndStop(T?O:b,!0):h.goToAndStop(T?S:0,!0)});else if("click"===f.playOn){var n=!1;o.addEventListener("click",function(){n?(h.pause(),f.reverse?h.goToAndStop(T?O:b,!0):h.goToAndStop(T?S:0,!0),n=!1):(t=0,T?f.reverse?(h.goToAndStop(O,!0),h.playSegments([O,S],!0)):(h.goToAndStop(S,!0),h.playSegments([S,O],!0)):(f.reverse?h.goToAndStop(b,!0):h.goToAndStop(0,!0),h.play()),n=!0)})}else if("scroll"===f.playOn){var r=function(){var e,t=o.getBoundingClientRect(),n=window.innerHeight,r=n*(f.scrollTopPoint/100),a=n*(1-f.scrollBottomPoint/100),l=t.top,i=t.bottom,s=O-S;if(f.scrollTopPoint>0||f.scrollBottomPoint>0)if(i>r&&l<a){e=(a-l)/(a-r),e=Math.min(Math.max(e,0),1),f.reverse&&(e=1-e);var p=S+Math.floor(e*s);h.goToAndStop(p,!0)}else f.reverse?h.goToAndStop(l>=a?O:S,!0):h.goToAndStop(l>=a?S:O,!0);else{e=Math.min(Math.max((n-t.top)/(t.height+n),0),1),f.reverse&&(e=1-e);var d=S+Math.floor(e*s);h.goToAndStop(d,!0)}};window.addEventListener("scroll",r),r()}else"viewport"===f.playOn&&(t=0,new IntersectionObserver(function(e){e.forEach(function(e){e.isIntersecting?(t=0,T?f.reverse?(h.goToAndStop(O,!0),h.playSegments([O,S],!0)):(h.goToAndStop(S,!0),h.playSegments([S,O],!0)):(f.reverse?h.goToAndStop(b,!0):h.goToAndStop(0,!0),h.play())):(h.pause(),f.reverse?h.goToAndStop(T?O:b,!0):h.goToAndStop(T?S:0,!0))})},{threshold:.2}).observe(e))})}};for(o.s();!(t=o.n()).done;)a()}catch(e){o.e(e)}finally{o.f()}})}},o={};function n(e){var r=o[e];if(void 0!==r)return r.exports;var a=o[e]={id:e,loaded:!1,exports:{}};return t[e].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}n.m=t,n.amdO={},e=[],n.O=(t,o,r,a)=>{if(!o){var l=1/0;for(d=0;d<e.length;d++){for(var[o,r,a]=e[d],i=!0,s=0;s<o.length;s++)(!1&a||l>=a)&&Object.keys(n.O).every(e=>n.O[e](o[s]))?o.splice(s--,1):(i=!1,a<l&&(l=a));if(i){e.splice(d--,1);var p=r();void 0!==p&&(t=p)}}return t}a=a||0;for(var d=e.length;d>0&&e[d-1][2]>a;d--)e[d]=e[d-1];e[d]=[o,r,a]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),n.j=6487,(()=>{var e={6487:0};n.O.j=t=>0===e[t];var t=(t,o)=>{var r,a,[l,i,s]=o,p=0;if(l.some(t=>0!==e[t])){for(r in i)n.o(i,r)&&(n.m[r]=i[r]);if(s)var d=s(n)}for(t&&t(o);p<l.length;p++)a=l[p],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(d)},o=globalThis.webpackChunkessential_blocks=globalThis.webpackChunkessential_blocks||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})(),n.nc=void 0;var r=n.O(void 0,[3935,7916],()=>n(1661));r=n.O(r)})();