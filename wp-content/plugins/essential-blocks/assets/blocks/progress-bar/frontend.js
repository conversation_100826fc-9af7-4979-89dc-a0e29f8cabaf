(()=>{"use strict";var e={n:r=>{var t=r&&r.__esModule?()=>r.default:()=>r;return e.d(t,{a:t}),t},d:(r,t)=>{for(var a in t)e.o(t,a)&&!e.o(r,a)&&Object.defineProperty(r,a,{enumerable:!0,get:t[a]})},o:(e,r)=>Object.prototype.hasOwnProperty.call(e,r)};const r=window.wp.domReady;var t=function(e){var r=e.duration,t=e.draw,a=e.timing,i=performance.now();requestAnimationFrame(function e(o){var n=(o-i)/r;n>1&&(n=1);var l=a(n);t(l),n<1&&requestAnimationFrame(e)})};e.n(r)()(function(){var e=document.querySelectorAll(".eb-progressbar");if(e){var r=new IntersectionObserver(function(e,r){e.forEach(function(e){var a,i,o,n;e.isIntersecting&&(a=e.target,i=a.getAttribute("data-layout"),o=a.getAttribute("data-count"),n=a.getAttribute("data-duration"),t({duration:n,timing:function(e){return e},draw:function(e){var r=Math.floor(100*e);if(r<=o)if("line"===i||"line_rainbow"===i)a.querySelector(".eb-progressbar-line-fill").style.width=r+"%";else if("circle"===i||"circle_fill"===i){var t=3.6*r;a.querySelector(".eb-progressbar-circle-half-left").style.transform="rotate("+t+"deg)",t>180&&(a.querySelector(".eb-progressbar-circle-pie").style.clipPath="inset(0)",a.querySelector(".eb-progressbar-circle-half-right").style.visibility="visible")}else"half_circle"===i||"half_circle_fill"===i?(t=1.8*r,a.querySelector(".eb-progressbar-circle-half").style.transform="rotate("+t+"deg)"):"box"===i&&(a.querySelector(".eb-progressbar-box-fill").style.height=r+"%")}}),function(e){var r=e.getAttribute("data-duration"),a=e.getAttribute("data-absolute"),i=e.getAttribute("data-count"),o="absolute"===e.getAttribute("data-type")?a:i;t({duration:r,timing:function(e){return e},draw:function(r){var t=Math.floor(r*o);if(t<=o){var a=e.querySelector(".eb-progressbar-count");a&&(a.innerText=t)}}})}(e.target),r.unobserve(e.target))})},{threshold:.25});e.forEach(function(e){r.observe(e)})}})})();