(()=>{function e(e,r){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,r){if(e){if("string"==typeof e)return t(e,r);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}(e))||r&&e&&"number"==typeof e.length){n&&(e=n);var a=0,l=function(){};return{s:l,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:l}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){c=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(c)throw o}}}}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}window.addEventListener("DOMContentLoaded",function(t){var r,n=e(document.querySelectorAll(".eb-gallery-img-wrapper.enable-isotope"));try{var a,l,o=function(){var t,n=r.value,o=n.getAttribute("data-id"),i=n.getAttribute("data-default-filter"),c=n.getAttribute("data-searchfilter"),s=n.closest(".eb-parent-wrapper").querySelectorAll(".eb-img-gallery-loadmore")[0],u=null==s?void 0:s.getAttribute("data-loadmore"),d=null==s?void 0:s.getAttribute("data-infinite-scroll"),f=Number(null==s?void 0:s.getAttribute("data-images-per-page")),p=f,g=n.closest(".eb-parent-wrapper").querySelectorAll(".filter-wrapper-".concat(o)),v=n.closest(".eb-parent-wrapper").querySelectorAll(".eb-img-gallery-filter-item"),y=n.closest(".eb-parent-wrapper").querySelectorAll(".eb-filter-select")[0],m=n.closest(".eb-parent-wrapper").querySelectorAll(".eb-img-gallery-filter-wrapper")[0],b=null==y?void 0:y.querySelector("span"),h="true"===c,w=n.closest(".eb-parent-wrapper").querySelector(".eb-search-gallery-input"),L=n.closest(".eb-parent-wrapper").querySelector(".eb-search-gallery-close"),A=n.closest(".eb-parent-wrapper").querySelector("#eb-img-gallery-not-found");if(v)if(i){var S,E=e(v);try{for(E.s();!(S=E.n()).done;){var k=S.value,q=k.getAttribute("data-filter");q!==".eb-filter-img-".concat(i)&&q!==i||k.classList.add("is-checked")}}catch(e){E.e(e)}finally{E.f()}}else{var x;null===(x=v[0])||void 0===x||x.classList.add("is-checked")}for(a=0,l=g.length;a<l;a++)I(g[a]);function I(e){e.addEventListener("click",function(t){matchesSelector(t.target,"li")&&(e.querySelector(".is-checked").classList.remove("is-checked"),t.target.classList.add("is-checked"),h&&(b.textContent=t.target.textContent,m.classList.remove("open-filters")))})}h&&(null==y||y.addEventListener("click",function(){m.classList.toggle("open-filters")}),null==y||y.addEventListener("blur",function(){setTimeout(function(){document.activeElement.closest(".nav-controls")||document.activeElement===y||m.classList.remove("open-filters")},500)}));var C=null,R="*";function z(e,r){var a,l=n.querySelectorAll(".hidden"),o=n.classList.contains("grid")?"fitRows":"masonry";l.forEach(function(e){e.classList.remove("hidden")});var i=null===(a=e.filteredItems)||void 0===a?void 0:a.slice(r,e.filteredItems.length).map(function(e){return e.element});null==i||i.forEach(function(e){e.classList.add("hidden")}),t.arrange({layoutMode:o}),0==(null==i?void 0:i.length)?s.style.display="none":s.style.display="block"}if(imagesLoaded(n,function(){var e=n.classList.contains("grid")?"fitRows":"masonry",r=!!n.classList.contains("masonry-uneven");t=new Isotope(".".concat(o),{itemSelector:".eb-gallery-img-content",layoutMode:e,transitionDuration:"0.5s",percentPosition:"fitRows"!==e,masonry:"fitRows"!==e?{columnWidth:r?".grid-sizer":".eb-gallery-img-content"}:null,filter:function(e,t){var r=e||t;if(!r)return!1;var n=(null==r?void 0:r.textContent)||"",a=!C||n.match(C),l="*"===R||r.matches(R);return a&&l}});var a=function(){t.filteredItems.map(function(e){return e.element}).forEach(function(e,t){var r="A"===e.tagName?e:e.querySelector("a[data-fslightbox]");r&&r.hasAttribute("data-fslightbox")&&r.setAttribute("data-fslightbox","gallery-".concat(o,"-").concat(R.replace(/[^a-zA-Z0-9]/g,"")))}),"function"==typeof refreshFsLightbox&&refreshFsLightbox()};i?(t.arrange({filter:"*"===i?"*":".eb-filter-img-".concat(i)}),a()):(t.arrange(),a()),h&&(w.addEventListener("keyup",function(){L.style.display=w.value.length>0?"block":"none",t.arrange({filter:function(e,t){var r=e||t;if(!r)return!1;var n=(null==r?void 0:r.textContent)||"",a=!C||n.match(C),l="*"===R||r.matches(R);return a&&l}}),C=new RegExp(w.value,"gi"),t.arrange();var e=t.filteredItems.length;s.style.display=e>f?"block":"none",C=new RegExp(w.value,"gi"),t.arrange(),a()}),L.addEventListener("click",function(e){e.preventDefault(),L.style.display="none",w.value="",C=null,t.arrange(),a()})),"true"===u&&"false"===d&&z(t,f),"true"===u&&"true"===d&&(window.addEventListener("scroll",function(e){window.innerHeight+window.scrollY>=n.offsetHeight&&(z(t,p),p+=f)}),z(t,p),p+=f);var l=n.closest(".eb-parent-wrapper").querySelectorAll(".filter-wrapper-".concat(o," li"));l.length>0&&l.forEach(function(e){e.addEventListener("click",function(r){var n=e.closest(".eb-parent-wrapper").querySelector(".".concat(o));R=r.target.getAttribute("data-filter"),(t=Isotope.data(n))&&(t.arrange({filter:R}),a())})}),A&&t.on("arrangeComplete",function(e){0===e.length?(A.classList.add("show"),s&&(s.style.display="none")):(A.classList.remove("show"),s&&(s.style.display="block"))})}),"true"===u&&"false"===d){var M=n.closest(".eb-parent-wrapper").querySelector(".eb-img-gallery-filter-wrapper");M&&(M.dataset.clicked="true"),s.addEventListener("click",function(e){t=Isotope.data(".".concat(o)),"true"===(null==M?void 0:M.dataset.clicked)&&(p=f,M.dataset.clicked="false"),z(t,p+=f)}),null==M||M.addEventListener("click",function(){t=Isotope.data(".".concat(o)),this.dataset.clicked="true",z(t,f)})}document.addEventListener("lazyloaded",function(){var e;null===(e=t)||void 0===e||e.layout()}),window.addEventListener("resize",function(){var e;null===(e=t)||void 0===e||e.layout()})};for(n.s();!(r=n.n()).done;)o()}catch(e){n.e(e)}finally{n.f()}})})();