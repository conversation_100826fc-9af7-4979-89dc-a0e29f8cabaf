(()=>{"use strict";var e,t={242:(e,t,r)=>{var n=r(8168),a=(r(1609),r(8468)),o=r(6391),i=r.n(o);function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}window.addEventListener("DOMContentLoaded",function(e){var t,r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return l(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){u=!0,o=e},f:function(){try{i||null==r.return||r.return()}finally{if(u)throw o}}}}(document.getElementsByClassName("eb-image-comparison-wrapper"));try{for(r.s();!(t=r.n()).done;){var o=t.value,u=o.getAttribute("data-left-image"),d=o.getAttribute("data-right-image"),f=o.getAttribute("data-vertical-mode"),c=o.getAttribute("data-hover"),s=o.getAttribute("data-show-label"),b=o.getAttribute("data-left-label"),g=o.getAttribute("data-right-label"),h=o.getAttribute("data-slider-position"),v=o.getAttribute("data-line-width"),m=o.getAttribute("data-line-color"),p=o.getAttribute("data-handle");(0,a.render)(React.createElement(i(),(0,n.A)({leftImage:u,rightImage:d},"true"==f?{vertical:"vertical"}:{},"true"==c?{hover:"hover"}:{},"true"==s?{leftImageLabel:b}:{},"true"==s?{rightImageLabel:g}:{},"true"==p?{handle:React.createElement(React.Fragment,null)}:{},{sliderPositionPercentage:h?h/100:.5,sliderLineWidth:v||0,sliderLineColor:m||"#ffffff"})),o)}}catch(e){r.e(e)}finally{r.f()}})},1609:e=>{e.exports=window.React},8468:e=>{e.exports=window.wp.element}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var o=r[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,n),o.loaded=!0,o.exports}n.m=t,n.amdO={},e=[],n.O=(t,r,a,o)=>{if(!r){var i=1/0;for(f=0;f<e.length;f++){for(var[r,a,o]=e[f],l=!0,u=0;u<r.length;u++)(!1&o||i>=o)&&Object.keys(n.O).every(e=>n.O[e](r[u]))?r.splice(u--,1):(l=!1,o<i&&(i=o));if(l){e.splice(f--,1);var d=a();void 0!==d&&(t=d)}}return t}o=o||0;for(var f=e.length;f>0&&e[f-1][2]>o;f--)e[f]=e[f-1];e[f]=[r,a,o]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),n.j=5242,(()=>{var e={5242:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var a,o,[i,l,u]=r,d=0;if(i.some(t=>0!==e[t])){for(a in l)n.o(l,a)&&(n.m[a]=l[a]);if(u)var f=u(n)}for(t&&t(r);d<i.length;d++)o=i[d],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return n.O(f)},r=globalThis.webpackChunkessential_blocks=globalThis.webpackChunkessential_blocks||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0;var a=n.O(void 0,[3935,7916],()=>n(242));a=n.O(a)})();