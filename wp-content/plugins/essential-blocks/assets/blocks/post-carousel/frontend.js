(()=>{"use strict";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.wp.apiFetch;var r=e.n(t);function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var o=window.eb_frontend.sanitizeIconValue,a=!!EssentialBlocksLocalize&&EssentialBlocksLocalize.rest_rootURL;r().use(r().createRootURLMiddleware(a)),window.addEventListener("DOMContentLoaded",function(e){var t,r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var o=0,a=function(){};return{s:a,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){l=!0,i=e},f:function(){try{s||null==r.return||r.return()}finally{if(l)throw i}}}}(document.getElementsByClassName("eb-post-carousel-wrapper"));try{var a=function(){var e=t.value.dataset,r=null==e?void 0:e.id,n=JSON.parse(e.slidersettings),a=JSON.parse(e.attributes),i=n.arrows,s=n.autoplay,l=n.dots,c=n.infinite,u=n.pauseOnHover,d=n.slideToShowRange,f=n.MOBslideToShowRange,p=n.TABslideToShowRange,v=n.autoplaySpeed,w=n.speed,y=(n.isRTLEnable,"rtl"===document.documentElement.dir);jQuery(".init-"+r).slick({arrows:i,autoplay:s,dots:l,infinite:c,pauseOnHover:u,slidesToShow:d,autoplaySpeed:v,speed:w,rtl:y,prevArrow:"<div class='slick-arrow slick-prev'><i class='".concat(o(a.leftArrowIcon),"'></i></div>"),nextArrow:"<div class='slick-arrow slick-next'><i class='".concat(o(a.rightArrowIcon),"'></i></div>"),responsive:[{breakpoint:1024,settings:{slidesToShow:p}},{breakpoint:767,settings:{slidesToShow:f}}]})};for(r.s();!(t=r.n()).done;)a()}catch(e){r.e(e)}finally{r.f()}})})();