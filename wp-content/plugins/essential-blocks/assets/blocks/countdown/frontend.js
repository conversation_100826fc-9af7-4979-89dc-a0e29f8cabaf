(()=>{"use strict";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.wp.domReady;function r(){var e=document.querySelectorAll(".eb-cd-wrapper > .eb-cd-inner");if(e)for(var t=function(){var t=e[r],n={textContent:"3e"},a=t.querySelector(".cd-box-day > .eb-cd-digit")||n,o=t.querySelector(".cd-box-hour > .eb-cd-digit")||n,c=t.querySelector(".cd-box-minute > .eb-cd-digit")||n,i=t.querySelector(".cd-box-second > .eb-cd-digit")||n,l=t.getAttribute("blockId"),d=t.getAttribute("data-is-evergreen-time"),u=t.getAttribute("data-evergreen-time-hours"),g=t.getAttribute("data-evergreen-time-minutes"),v=t.getAttribute("data-evergreen-recurring"),s=t.getAttribute("data-evergreen-restart-time"),b=t.getAttribute("data-evergreen-deadline-time"),f=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=Date.now(),n=Math.round((e-r)/1e3),l=n%60,d=Math.floor(n/60)%60,u=Math.floor(n/3600)%24,g=Math.floor(n/86400);if(n<0)return clearInterval(t),a.textContent="00",o.textContent="00",c.textContent="00",void(i.textContent="00");a.textContent=g<10?"0".concat(g):"".concat(g),o.textContent=u<10?"0".concat(u):"".concat(u),c.textContent=d<10?"0".concat(d):"".concat(d),i.textContent=l<10?"0".concat(l):"".concat(l)};if("true"===d){var m=l+"_evergreen_interval",I=l+"_evergreen_time",p=localStorage.getItem(m),x=localStorage.getItem(I),w=3600*parseInt(u||0),y=60*parseInt(g||0),C=parseInt(w+y);if(null!==x&&null!==p&&p==C||(x=Date.now()+1e3*parseInt(C),localStorage.setItem(m,C),localStorage.setItem(I,x)),"false"!==v){var S=36e5*parseFloat(s);parseInt(x)+S<Date.now()&&(x=Date.now()+1e3*parseInt(C),localStorage.setItem(I,x)),b<x&&(x=b)}f(x||0);var h=setInterval(function(){f(x||0,h)},1e3)}if(null==d||"false"==d){var A=parseInt(t.getAttribute("data-deadline-time"));f(A||0);var _=setInterval(function(){f(A||0,_)},1e3)}},r=0;r<e.length;r++)t()}e.n(t)()(function(){r()}),window.ebRunCountDown=r})();