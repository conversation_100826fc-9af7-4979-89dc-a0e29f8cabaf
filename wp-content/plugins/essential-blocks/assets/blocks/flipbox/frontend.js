window.addEventListener("DOMContentLoaded",function(){var e=document.querySelectorAll(".eb-flipbox-container");if(0===e.length)return!1;e.forEach(function(e,t){var i=e.getAttribute("data-flip-mode"),n=e.getAttribute("data-flip-type"),a=e.getAttribute("data-flip-mouseleave"),s=e.querySelector(".eb-flipper");s&&"click"===i&&(s.addEventListener("click",function(e){s.classList.contains(n)?s.classList.remove(n):s.classList.add(n)}),"true"===a&&s.addEventListener("mouseleave",function(e){s.classList.contains(n)&&s.classList.remove(n)}))})});