/*! For license information please see dash.all.min.js.LICENSE.txt */
!function e(t,r,n){function a(o,s){if(!r[o]){if(!t[o]){var u="function"==typeof require&&require;if(!s&&u)return u(o,!0);if(i)return i(o,!0);var l=new Error("Cannot find module '"+o+"'");throw l.code="MODULE_NOT_FOUND",l}var d=r[o]={exports:{}};t[o][0].call(d.exports,function(e){return a(t[o][1][e]||e)},d,d.exports,e,t,r,n)}return r[o].exports}for(var i="function"==typeof require&&require,o=0;o<n.length;o++)a(n[o]);return a}({1:[function(e,t,r){"use strict";var n=function(e){for(var t=[],r=0;r<e.length;++r){var n=e.charCodeAt(r);128>n?t.push(n):2048>n?(t.push(192|n>>6),t.push(128|63&n)):65536>n?(t.push(224|n>>12),t.push(128|63&n>>6),t.push(128|63&n)):(t.push(240|n>>18),t.push(128|63&n>>12),t.push(128|63&n>>6),t.push(128|63&n))}return t},i=function(e){for(var t=[],r=0;r<e.length;){var n=e[r++];128>n||(224>n?(n=(31&n)<<6,n|=63&e[r++]):240>n?(n=(15&n)<<12,n|=(63&e[r++])<<6,n|=63&e[r++]):(n=(7&n)<<18,n|=(63&e[r++])<<12,n|=(63&e[r++])<<6,n|=63&e[r++])),t.push(String.fromCharCode(n))}return t.join("")},o={};!function(e){var t=function(t){for(var r=0,n=[],a=0|t.length/3;0<a--;){var i=(t[r]<<16)+(t[r+1]<<8)+t[r+2];r+=3,n.push(e.charAt(63&i>>18)),n.push(e.charAt(63&i>>12)),n.push(e.charAt(63&i>>6)),n.push(e.charAt(63&i))}return 2==t.length-r?(i=(t[r]<<16)+(t[r+1]<<8),n.push(e.charAt(63&i>>18)),n.push(e.charAt(63&i>>12)),n.push(e.charAt(63&i>>6)),n.push("=")):1==t.length-r&&(i=t[r]<<16,n.push(e.charAt(63&i>>18)),n.push(e.charAt(63&i>>12)),n.push("==")),n.join("")},r=function(){for(var t=[],r=0;r<64;++r)t[e.charCodeAt(r)]=r;return t["=".charCodeAt(0)]=0,t}(),u=function(e){for(var t=0,n=[],a=0|e.length/4;0<a--;){var i=(r[e.charCodeAt(t)]<<18)+(r[e.charCodeAt(t+1)]<<12)+(r[e.charCodeAt(t+2)]<<6)+r[e.charCodeAt(t+3)];n.push(255&i>>16),n.push(255&i>>8),n.push(255&i),t+=4}return n&&("="==e.charAt(t-2)?(n.pop(),n.pop()):"="==e.charAt(t-1)&&n.pop()),n},l=function(e){for(var t=[],r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t},d=function(e){for(var t=0;t<s.length;++t)a[t]=String.fromCharCode(a[t]);return a.join("")};o.decodeArray=function(e){var t=u(e);return new Uint8Array(t)},o.encodeASCII=function(e){var r=l(e);return t(r)},o.decodeASCII=function(e){var t=u(e);return d(t)},o.encode=function(e){var r=n(e);return t(r)},o.decode=function(e){var t=u(e);return i(t)}}("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"),void 0!==r&&(r.decode=o.decode,r.decodeArray=o.decodeArray)},{}],2:[function(e,t,r){"use strict";!function(e){var t={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,128:174,129:176,130:189,131:191,132:8482,133:162,134:163,135:9834,136:224,137:32,138:232,139:226,140:234,141:238,142:244,143:251,144:193,145:201,146:211,147:218,148:220,149:252,150:8216,151:161,152:42,153:8217,154:9473,155:169,156:8480,157:8226,158:8220,159:8221,160:192,161:194,162:199,163:200,164:202,165:203,166:235,167:206,168:207,169:239,170:212,171:217,172:249,173:219,174:171,175:187,176:195,177:227,178:205,179:204,180:236,181:210,182:242,183:213,184:245,185:123,186:125,187:92,188:94,189:95,190:124,191:8764,192:196,193:228,194:214,195:246,196:223,197:165,198:164,199:9475,200:197,201:229,202:216,203:248,204:9487,205:9491,206:9495,207:9499},r=function(e){var r=e;return t.hasOwnProperty(e)&&(r=t[e]),String.fromCharCode(r)},n=15,a=32,i={17:1,18:3,21:5,22:7,23:9,16:11,19:12,20:14},o={17:2,18:4,21:6,22:8,23:10,19:13,20:15},s={25:1,26:3,29:5,30:7,31:9,24:11,27:12,28:14},u={25:2,26:4,29:6,30:8,31:10,27:13,28:15},l=["white","green","blue","cyan","red","yellow","magenta","black","transparent"],d={verboseFilter:{DATA:3,DEBUG:3,INFO:2,WARNING:2,TEXT:1,ERROR:0},time:null,verboseLevel:0,setTime:function(e){this.time=e},log:function(e,t){var r=this.verboseFilter[e];this.verboseLevel>=r&&console.log(this.time+" ["+e+"] "+t)}},f=function(e){for(var t=[],r=0;r<e.length;r++)t.push(e[r].toString(16));return t},c=function(e,t,r,n,a){this.foreground=e||"white",this.underline=t||!1,this.italics=r||!1,this.background=n||"black",this.flash=a||!1};c.prototype={reset:function(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1},setStyles:function(e){for(var t=["foreground","underline","italics","background","flash"],r=0;r<t.length;r++){var n=t[r];e.hasOwnProperty(n)&&(this[n]=e[n])}},isDefault:function(){return"white"===this.foreground&&!this.underline&&!this.italics&&"black"===this.background&&!this.flash},equals:function(e){return this.foreground===e.foreground&&this.underline===e.underline&&this.italics===e.italics&&this.background===e.background&&this.flash===e.flash},copy:function(e){this.foreground=e.foreground,this.underline=e.underline,this.italics=e.italics,this.background=e.background,this.flash=e.flash},toString:function(){return"color="+this.foreground+", underline="+this.underline+", italics="+this.italics+", background="+this.background+", flash="+this.flash}};var g=function(e,t,r,n,a,i){this.uchar=e||" ",this.penState=new c(t,r,n,a,i)};g.prototype={reset:function(){this.uchar=" ",this.penState.reset()},setChar:function(e,t){this.uchar=e,this.penState.copy(t)},setPenState:function(e){this.penState.copy(e)},equals:function(e){return this.uchar===e.uchar&&this.penState.equals(e.penState)},copy:function(e){this.uchar=e.uchar,this.penState.copy(e.penState)},isEmpty:function(){return" "===this.uchar&&this.penState.isDefault()}};var h=function(){this.chars=[];for(var e=0;a>e;e++)this.chars.push(new g);this.pos=0,this.currPenState=new c};h.prototype={equals:function(e){for(var t=!0,r=0;a>r;r++)if(!this.chars[r].equals(e.chars[r])){t=!1;break}return t},copy:function(e){for(var t=0;a>t;t++)this.chars[t].copy(e.chars[t])},isEmpty:function(){for(var e=!0,t=0;a>t;t++)if(!this.chars[t].isEmpty()){e=!1;break}return e},setCursor:function(e){this.pos!==e&&(this.pos=e),this.pos<0?(d.log("ERROR","Negative cursor position "+this.pos),this.pos=0):this.pos>a&&(d.log("ERROR","Too large cursor position "+this.pos),this.pos=a)},moveCursor:function(e){var t=this.pos+e;if(e>1)for(var r=this.pos+1;t+1>r;r++)this.chars[r].setPenState(this.currPenState);this.setCursor(t)},backSpace:function(){this.moveCursor(-1),this.chars[this.pos].setChar(" ",this.currPenState)},insertChar:function(e){e>=144&&this.backSpace();var t=r(e);return this.pos>=a?void d.log("ERROR","Cannot insert "+e.toString(16)+" ("+t+") at position "+this.pos+". Skipping it!"):(this.chars[this.pos].setChar(t,this.currPenState),void this.moveCursor(1))},clearFromPos:function(e){var t;for(t=e;a>t;t++)this.chars[t].reset()},clear:function(){this.clearFromPos(0),this.pos=0,this.currPenState.reset()},clearToEndOfRow:function(){this.clearFromPos(this.pos)},getTextString:function(){for(var e=[],t=!0,r=0;a>r;r++){var n=this.chars[r].uchar;" "!==n&&(t=!1),e.push(n)}return t?"":e.join("")},setPenStyles:function(e){this.currPenState.setStyles(e),this.chars[this.pos].setPenState(this.currPenState)}};var p=function(){this.rows=[];for(var e=0;n>e;e++)this.rows.push(new h);this.currRow=14,this.nrRollUpRows=null,this.reset()};p.prototype={reset:function(){for(var e=0;n>e;e++)this.rows[e].clear();this.currRow=14},equals:function(e){for(var t=!0,r=0;n>r;r++)if(!this.rows[r].equals(e.rows[r])){t=!1;break}return t},copy:function(e){for(var t=0;n>t;t++)this.rows[t].copy(e.rows[t])},isEmpty:function(){for(var e=!0,t=0;n>t;t++)if(!this.rows[t].isEmpty()){e=!1;break}return e},backSpace:function(){this.rows[this.currRow].backSpace()},clearToEndOfRow:function(){this.rows[this.currRow].clearToEndOfRow()},insertChar:function(e){this.rows[this.currRow].insertChar(e)},setPen:function(e){this.rows[this.currRow].setPenStyles(e)},moveCursor:function(e){this.rows[this.currRow].moveCursor(e)},setCursor:function(e){d.log("INFO","setCursor: "+e),this.rows[this.currRow].setCursor(e)},setPAC:function(e){d.log("INFO","pacData = "+JSON.stringify(e));var t=e.row-1;this.nrRollUpRows&&t<this.nrRollUpRows-1&&(t=this.nrRollUpRows-1),this.currRow=t;var r=this.rows[this.currRow];if(null!==e.indent){var n=e.indent,a=Math.max(n-1,0);r.setCursor(e.indent),e.color=r.chars[a].penState.foreground}var i={foreground:e.color,underline:e.underline,italics:e.italics,background:"black",flash:!1};this.setPen(i)},setBkgData:function(e){d.log("INFO","bkgData = "+JSON.stringify(e)),this.backSpace(),this.setPen(e),this.insertChar(32)},setRollUpRows:function(e){this.nrRollUpRows=e},rollUp:function(){if(null!==this.nrRollUpRows){d.log("TEXT",this.getDisplayText());var e=this.currRow+1-this.nrRollUpRows,t=this.rows.splice(e,1)[0];t.clear(),this.rows.splice(this.currRow,0,t),d.log("INFO","Rolling up")}else d.log("DEBUG","roll_up but nrRollUpRows not set yet")},getDisplayText:function(e){e=e||!1;for(var t=[],r="",a=-1,i=0;n>i;i++){var o=this.rows[i].getTextString();o&&(a=i+1,e?t.push("Row "+a+': "'+o+'"'):t.push(o.trim()))}return t.length>0&&(r=e?"["+t.join(" | ")+"]":t.join("\n")),r},getTextAndFormat:function(){return this.rows}};var m=function(e,t){this.chNr=e,this.outputFilter=t,this.mode=null,this.verbose=0,this.displayedMemory=new p,this.nonDisplayedMemory=new p,this.lastOutputScreen=new p,this.currRollUpRow=this.displayedMemory.rows[14],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null};m.prototype={modes:["MODE_ROLL-UP","MODE_POP-ON","MODE_PAINT-ON","MODE_TEXT"],reset:function(){this.mode=null,this.displayedMemory.reset(),this.nonDisplayedMemory.reset(),this.lastOutputScreen.reset(),this.currRollUpRow=this.displayedMemory.rows[14],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null,this.lastCueEndTime=null},getHandler:function(){return this.outputFilter},setHandler:function(e){this.outputFilter=e},setPAC:function(e){this.writeScreen.setPAC(e)},setBkgData:function(e){this.writeScreen.setBkgData(e)},setMode:function(e){e!==this.mode&&(this.mode=e,d.log("INFO","MODE="+e),"MODE_POP-ON"==this.mode?this.writeScreen=this.nonDisplayedMemory:(this.writeScreen=this.displayedMemory,this.writeScreen.reset()),"MODE_ROLL-UP"!==this.mode&&(this.displayedMemory.nrRollUpRows=null,this.nonDisplayedMemory.nrRollUpRows=null),this.mode=e)},insertChars:function(e){for(var t=0;t<e.length;t++)this.writeScreen.insertChar(e[t]);var r=this.writeScreen===this.displayedMemory?"DISP":"NON_DISP";d.log("INFO",r+": "+this.writeScreen.getDisplayText(!0)),("MODE_PAINT-ON"===this.mode||"MODE_ROLL-UP"===this.mode)&&(d.log("TEXT","DISPLAYED: "+this.displayedMemory.getDisplayText(!0)),this.outputDataUpdate())},cc_RCL:function(){d.log("INFO","RCL - Resume Caption Loading"),this.setMode("MODE_POP-ON")},cc_BS:function(){d.log("INFO","BS - BackSpace"),"MODE_TEXT"!==this.mode&&(this.writeScreen.backSpace(),this.writeScreen===this.displayedMemory&&this.outputDataUpdate())},cc_AOF:function(){},cc_AON:function(){},cc_DER:function(){d.log("INFO","DER- Delete to End of Row"),this.writeScreen.clearToEndOfRow(),this.outputDataUpdate()},cc_RU:function(e){d.log("INFO","RU("+e+") - Roll Up"),this.writeScreen=this.displayedMemory,this.setMode("MODE_ROLL-UP"),this.writeScreen.setRollUpRows(e)},cc_FON:function(){d.log("INFO","FON - Flash On"),this.writeScreen.setPen({flash:!0})},cc_RDC:function(){d.log("INFO","RDC - Resume Direct Captioning"),this.setMode("MODE_PAINT-ON")},cc_TR:function(){d.log("INFO","TR"),this.setMode("MODE_TEXT")},cc_RTD:function(){d.log("INFO","RTD"),this.setMode("MODE_TEXT")},cc_EDM:function(){d.log("INFO","EDM - Erase Displayed Memory"),this.displayedMemory.reset(),this.outputDataUpdate()},cc_CR:function(){d.log("CR - Carriage Return"),this.writeScreen.rollUp(),this.outputDataUpdate()},cc_ENM:function(){d.log("INFO","ENM - Erase Non-displayed Memory"),this.nonDisplayedMemory.reset()},cc_EOC:function(){if(d.log("INFO","EOC - End Of Caption"),"MODE_POP-ON"===this.mode){var e=this.displayedMemory;this.displayedMemory=this.nonDisplayedMemory,this.nonDisplayedMemory=e,this.writeScreen=this.nonDisplayedMemory,d.log("TEXT","DISP: "+this.displayedMemory.getDisplayText())}this.outputDataUpdate()},cc_TO:function(e){d.log("INFO","TO("+e+") - Tab Offset"),this.writeScreen.moveCursor(e)},cc_MIDROW:function(e){var t={flash:!1};if(t.underline=e%2==1,t.italics=e>=46,t.italics)t.foreground="white";else{var r=Math.floor(e/2)-16;t.foreground=["white","green","blue","cyan","red","yellow","magenta"][r]}d.log("INFO","MIDROW: "+JSON.stringify(t)),this.writeScreen.setPen(t)},outputDataUpdate:function(){var e=d.time;null!==e&&this.outputFilter&&(this.outputFilter.updateData&&this.outputFilter.updateData(e,this.displayedMemory),null!==this.cueStartTime||this.displayedMemory.isEmpty()?this.displayedMemory.equals(this.lastOutputScreen)||(this.outputFilter.newCue&&this.outputFilter.newCue(this.cueStartTime,e,this.lastOutputScreen),this.cueStartTime=this.displayedMemory.isEmpty()?null:e):this.cueStartTime=e,this.lastOutputScreen.copy(this.displayedMemory))},cueSplitAtTime:function(e){this.outputFilter&&(this.displayedMemory.isEmpty()||(this.outputFilter.newCue&&this.outputFilter.newCue(this.cueStartTime,e,this.displayedMemory),this.cueStartTime=e))}};var v=function(e,t,r){this.field=e||1,this.outputs=[t,r],this.channels=[new m(1,t),new m(2,r)],this.currChNr=-1,this.lastCmdA=null,this.lastCmdB=null,this.bufferedData=[],this.startTime=null,this.lastTime=null,this.dataCounters={padding:0,char:0,cmd:0,other:0}};v.prototype={getHandler:function(e){return this.channels[e].getHandler()},setHandler:function(e,t){this.channels[e].setHandler(t)},addData:function(e,t){var r,n,a,i=!1;this.lastTime=e,d.setTime(e);for(var o=0;o<t.length;o+=2)n=127&t[o],a=127&t[o+1],0!==n||0!==a?(d.log("DATA","["+f([t[o],t[o+1]])+"] -> ("+f([n,a])+")"),(r=this.parseCmd(n,a))||(r=this.parseMidrow(n,a)),r||(r=this.parsePAC(n,a)),r||(r=this.parseBackgroundAttributes(n,a)),!r&&(i=this.parseChars(n,a))&&(this.currChNr&&this.currChNr>=0?this.channels[this.currChNr-1].insertChars(i):d.log("WARNING","No channel found yet. TEXT-MODE?")),r?this.dataCounters.cmd+=2:i?this.dataCounters.char+=2:(this.dataCounters.other+=2,d.log("WARNING","Couldn't parse cleaned data "+f([n,a])+" orig: "+f([t[o],t[o+1]])))):this.dataCounters.padding+=2},parseCmd:function(e,t){var r;if(!((20===e||28===e)&&t>=32&&47>=t||(23===e||31===e)&&t>=33&&35>=t))return!1;if(e===this.lastCmdA&&t===this.lastCmdB)return this.lastCmdA=null,this.lastCmdB=null,d.log("DEBUG","Repeated command ("+f([e,t])+") is dropped"),!0;r=20===e||23===e?1:2;var n=this.channels[r-1];return 20===e||28===e?32===t?n.cc_RCL():33===t?n.cc_BS():34===t?n.cc_AOF():35===t?n.cc_AON():36===t?n.cc_DER():37===t?n.cc_RU(2):38===t?n.cc_RU(3):39===t?n.cc_RU(4):40===t?n.cc_FON():41===t?n.cc_RDC():42===t?n.cc_TR():43===t?n.cc_RTD():44===t?n.cc_EDM():45===t?n.cc_CR():46===t?n.cc_ENM():47===t&&n.cc_EOC():n.cc_TO(t-32),this.lastCmdA=e,this.lastCmdB=t,this.currChNr=r,!0},parseMidrow:function(e,t){var r=null;return(17===e||25===e)&&t>=32&&47>=t&&((r=17===e?1:2)!==this.currChNr?(d.log("ERROR","Mismatch channel in midrow parsing"),!1):(this.channels[r-1].cc_MIDROW(t),d.log("DEBUG","MIDROW ("+f([e,t])+")"),!0))},parsePAC:function(e,t){var r,n;if(!((e>=17&&23>=e||e>=25&&31>=e)&&t>=64&&127>=t||(16===e||24===e)&&t>=64&&95>=t))return!1;if(e===this.lastCmdA&&t===this.lastCmdB)return this.lastCmdA=null,this.lastCmdB=null,!0;r=23>=e?1:2,n=t>=64&&95>=t?1===r?i[e]:s[e]:1===r?o[e]:u[e];var a=this.interpretPAC(n,t);return this.channels[r-1].setPAC(a),this.lastCmdA=e,this.lastCmdB=t,this.currChNr=r,!0},interpretPAC:function(e,t){var r,n={color:null,italics:!1,indent:null,underline:!1,row:e};return r=t>95?t-96:t-64,n.underline=!(1&~r),13>=r?n.color=["white","green","blue","cyan","red","yellow","magenta","white"][Math.floor(r/2)]:15>=r?(n.italics=!0,n.color="white"):n.indent=4*Math.floor((r-16)/2),n},parseChars:function(e,t){var n,a=null,i=null,o=null;(e>=25?(a=2,o=e-8):(a=1,o=e),o>=17&&19>=o)?(n=17===o?t+80:18===o?t+112:t+144,d.log("INFO","Special char '"+r(n)+"' in channel "+a),i=[n]):e>=32&&127>=e&&(i=0===t?[e]:[e,t]);if(i){var s=f(i);d.log("DEBUG","Char codes =  "+s.join(",")),this.lastCmdA=null,this.lastCmdB=null}return i},parseBackgroundAttributes:function(e,t){var r,n,a;return!!((16===e||24===e)&&t>=32&&47>=t||(23===e||31===e)&&t>=45&&47>=t)&&(r={},16===e||24===e?(n=Math.floor((t-32)/2),r.background=l[n],t%2==1&&(r.background=r.background+"_semi")):45===t?r.background="transparent":(r.foreground="black",47===t&&(r.underline=!0)),a=24>e?1:2,this.channels[a-1].setBkgData(r),this.lastCmdA=null,this.lastCmdB=null,!0)},reset:function(){for(var e=0;e<this.channels.length;e++)this.channels[e]&&this.channels[e].reset();this.lastCmdA=null,this.lastCmdB=null},cueSplitAtTime:function(e){for(var t=0;t<this.channels.length;t++)this.channels[t]&&this.channels[t].cueSplitAtTime(e)}},e.logger=d,e.PenState=c,e.CaptionScreen=p,e.Cea608Parser=v,e.findCea608Nalus=function(e,t,r){for(var n=0,a=t,i=[],o=function(e,t,r,n){if(4!==e||8>t)return null;var a=r.getUint8(n),i=r.getUint16(n+1),o=r.getUint32(n+3),s=r.getUint8(n+7);return 181==a&&49==i&&1195456820==o&&3==s};t+r>a;){if(n=e.getUint32(a),6==(31&e.getUint8(a+4)))for(var s=a+5,u=-1;a+4+n-1>s;){u=0;for(var l=255;255===l;)u+=l=e.getUint8(s),s++;var d=0;for(l=255;255===l;)d+=l=e.getUint8(s),s++;o(u,d,e,s)&&i.push([s,d]),s+=d}a+=n+4}return i},e.extractCea608DataFromRange=function(e,t){var r=t[0],n=[[],[]];r+=8;var a=31&e.getUint8(r);r+=2;for(var i=0;a>i;i++){var o=e.getUint8(r),s=4&o,u=3&o;r++;var l=e.getUint8(r);r++;var d=e.getUint8(r);r++,s&&(127&l)+(127&d)!=0&&(0===u?(n[0].push(l),n[0].push(d)):1===u&&(n[1].push(l),n[1].push(d)))}return n}}(void 0===r?(void 0).cea608parser={}:r)},{}],3:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){var t,r,n;for(t=[],n=0,r=e.length;r>n;n+=1)e[n].isRoot?t.push("root"):t.push(e[n].name);var a=function(e,t){var r;if(null!==e&&null!==t)for(r in e)e.hasOwnProperty(r)&&(t.hasOwnProperty(r)||(t[r]=e[r]))},i=function(e,t,r){var n,i,o,s,u;if(null!==e&&0!==e.length)for(n=0,i=e.length;i>n;n+=1)o=e[n],t.hasOwnProperty(o.name)&&(r.hasOwnProperty(o.name)?o.merge&&(s=t[o.name],u=r[o.name],"object"==typeof s&&"object"==typeof u?a(s,u):null!=o.mergeFunction?r[o.name]=o.mergeFunction(s,u):r[o.name]=s+u):r[o.name]=t[o.name])},o=function e(t,r){var n,a,o,s,u,l,d,f=t;if(null!==f.children&&0!==f.children.length)for(n=0,a=f.children.length;a>n;n+=1)if(l=f.children[n],r.hasOwnProperty(l.name))if(l.isArray)for(o=0,s=(u=r[l.name+"_asArray"]).length;s>o;o+=1)d=u[o],i(f.properties,r,d),e(l,d);else d=r[l.name],i(f.properties,r,d),e(l,d)};return{run:function r(n){var a,i,s,u,l,d,f;if(null===n)return n;if("object"!=typeof n)return n;for(a=0,i=t.length;i>a;a+=1)"root"===t[a]&&(l=e[a],o(l,d=n));for(u in n)if(n.hasOwnProperty(u)&&"__children"!=u){if(-1!==(s=t.indexOf(u)))if((l=e[s]).isArray)for(a=0,i=(f=n[u+"_asArray"]).length;i>a;a+=1)d=f[a],o(l,d);else d=n[u],o(l,d);r(n[u])}return n}}},t.exports=r.default},{}],4:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){function n(e){var t=e.localName;return null==t&&(t=e.baseName),(null==t||""==t)&&(t=e.nodeName),t}function a(e){return"string"==typeof e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;"):e}function i(a){if(a.nodeType==p.DOCUMENT_NODE){var o,s,u=a.firstChild;for(o=0,s=a.childNodes.length;s>o;o+=1)if(a.childNodes[o].nodeType!==p.COMMENT_NODE){u=a.childNodes[o];break}return r?l=i(u):(l={})[g=n(u)]=i(u),l}if(a.nodeType==p.ELEMENT_NODE){var l;(l=new Object).__cnt=0;for(var d=[],f=a.childNodes,c=0;c<f.length;c++){var g=n(u=f.item(c));if(l.__cnt++,null==l[g]){var m=i(u);("#text"!=g||/[^\s]/.test(m))&&((_={})[g]=m,d.push(_)),l[g]=m,l[g+"_asArray"]=new Array(1),l[g+"_asArray"][0]=l[g]}else{if(null!=l[g]&&!(l[g]instanceof Array)){var v=l[g];l[g]=new Array,l[g][0]=v,l[g+"_asArray"]=l[g]}for(var y=0;null!=l[g][y];)y++;var _;m=i(u),("#text"!=g||/[^\s]/.test(m))&&((_={})[g]=m,d.push(_)),l[g][y]=m}}l.__children=d;for(var E=0;E<a.attributes.length;E++){var T=a.attributes.item(E);l.__cnt++;for(var M=T.value,S=0,R=e.length;R>S;S++){var I=e[S];I.test.call(this,T)&&(M=I.converter.call(this,T.value))}l[t+T.name]=M}var C=function(e){return e.prefix}(a);return null!=C&&""!=C&&(l.__cnt++,l.__prefix=C),1==l.__cnt&&null!=l["#text"]&&(l=l["#text"]),null!=l["#text"]&&(l.__text=l["#text"],h&&(l.__text=function(e){return e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&#x2F;/g,"/")}(l.__text)),delete l["#text"],delete l["#text_asArray"]),null!=l["#cdata-section"]&&(l.__cdata=l["#cdata-section"],delete l["#cdata-section"],delete l["#cdata-section_asArray"]),(null!=l.__text||null!=l.__cdata)&&(l.toString=function(){return(null!=this.__text?this.__text:"")+(null!=this.__cdata?this.__cdata:"")}),l}return a.nodeType==p.TEXT_NODE||a.nodeType==p.CDATA_SECTION_NODE?a.nodeValue:a.nodeType==p.COMMENT_NODE?null:void 0}function o(e,t,r,n){var a="<"+(null!=e&&null!=e.__prefix?e.__prefix+":":"")+t;if(null!=r)for(var i=0;i<r.length;i++){var o=r[i],s=e[o];a+=" "+o.substr(1)+"='"+s+"'"}return a+(n?"/>":">")}function s(e,t){return"</"+(null!=e.__prefix?e.__prefix+":":"")+t+">"}function u(e,t){return!!(function(e,t){return-1!==e.indexOf(t,e.length-8)}(t.toString(),"_asArray")||0==t.toString().indexOf("_")||e[t]instanceof Function)}function l(e){var t=0;if(e instanceof Object)for(var r in e)u(e,r)||t++;return t}function d(e){var t=[];if(e instanceof Object)for(var r in e)-1==r.toString().indexOf("__")&&0==r.toString().indexOf("_")&&t.push(r);return t}function f(e){var t="";return e instanceof Object?t+=function(e){var t="";return null!=e.__cdata&&(t+="<![CDATA["+e.__cdata+"]]>"),null!=e.__text&&(t+=h?a(e.__text):e.__text),t}(e):null!=e&&(t+=h?a(e):e),t}function c(e,t,r){var n="";if(0==e.length)n+=o(e,t,r,!0);else for(var a=0;a<e.length;a++)n+=o(e[a],t,d(e[a]),!1),n+=g(e[a]),n+=s(e[a],t);return n}function g(e){var t="";if(l(e)>0)for(var r in e)if(!u(e,r)){var n=e[r],a=d(n);null==n||null==n?t+=o(n,r,a,!0):n instanceof Object?n instanceof Array?t+=c(n,r,a):l(n)>0||null!=n.__text||null!=n.__cdata?(t+=o(n,r,a,!1),t+=g(n),t+=s(n,r)):t+=o(n,r,a,!0):(t+=o(n,r,a,!1),t+=f(n),t+=s(n,r))}return t+f(e)}null==t&&(t="_"),null==r&&(r=!1);var h=!1,p={ELEMENT_NODE:1,TEXT_NODE:3,CDATA_SECTION_NODE:4,COMMENT_NODE:8,DOCUMENT_NODE:9};this.parseXmlString=function(e){var t,r,n;if(window.DOMParser){r=new window.DOMParser;try{n=r.parseFromString("<","text/xml").getElementsByTagName("parsererror")[0].namespaceURI}catch(e){}try{t=r.parseFromString(e,"text/xml"),n&&t.getElementsByTagNameNS(n,"parsererror").length&&(t=void 0)}catch(e){}}else 0==e.indexOf("<?")&&(e=e.substr(e.indexOf("?>")+2)),(t=new ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e);return t},this.xml2json=function(e){return i(e)},this.xml_str2json=function(e){var t=this.parseXmlString(e);return t?this.xml2json(t):void 0},this.json2xml_str=function(e){return g(e)},this.json2xml=function(e){var t=this.json2xml_str(e);return this.parseXmlString(t)},this.getVersion=function(){return"1.0.11"},this.escapeMode=function(e){h=e}},t.exports=r.default},{}],5:[function(e,t,r){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(r,"__esModule",{value:!0});var a=n(e("./src/streaming/MediaPlayer.js")),i=n(e("./src/streaming/protection/Protection.js")),o=n(e("./src/streaming/metrics/MetricsReporting.js")),s=n(e("./src/streaming/MediaPlayerFactory.js")),u=window||t,l=u.dashjs;l||(l=u.dashjs={}),l.MediaPlayer=a.default,l.Protection=i.default,l.MetricsReporting=o.default,l.MediaPlayerFactory=s.default,r.default=l,r.MediaPlayer=a.default,r.Protection=i.default,r.MetricsReporting=o.default,r.MediaPlayerFactory=s.default}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./src/streaming/MediaPlayer.js":39,"./src/streaming/MediaPlayerFactory.js":41,"./src/streaming/metrics/MetricsReporting.js":64,"./src/streaming/protection/Protection.js":95}],6:[function(e,t,r){var n={parseBuffer:function(e){return new a(e).parse()},Utils:{}};n.Utils.dataViewToString=function(e,t){var r=t||"utf-8";if("undefined"!=typeof TextDecoder)return new TextDecoder(r).decode(e);var n=[],a=0;if("utf-8"===r)for(;a<e.byteLength;){var i=e.getUint8(a++);128>i||(224>i?(i=(31&i)<<6,i|=63&e.getUint8(a++)):240>i?(i=(15&i)<<12,i|=(63&e.getUint8(a++))<<6,i|=63&e.getUint8(a++)):(i=(7&i)<<18,i|=(63&e.getUint8(a++))<<12,i|=(63&e.getUint8(a++))<<6,i|=63&e.getUint8(a++))),n.push(String.fromCharCode(i))}else for(;a<e.byteLength;)n.push(String.fromCharCode(e.getUint8(a++)));return n.join("")},void 0!==r&&(r.parseBuffer=n.parseBuffer,r.Utils=n.Utils),n.Cursor=function(e){this.offset=void 0===e?0:e};var a=function(e){this._raw=new DataView(e),this._cursor=new n.Cursor,this.boxes=[]};a.prototype.fetch=function(e){var t=this.fetchAll(e,!0);return t.length?t[0]:null},a.prototype.fetchAll=function(e,t){var r=[];return a._sweep.call(this,e,r,t),r},a.prototype.parse=function(){for(this._cursor.offset=0,this.boxes=[];this._cursor.offset<this._raw.byteLength;){var e=i.parse(this);if(void 0===e.type)break;this.boxes.push(e)}return this},a._sweep=function(e,t,r){for(var n in this.type&&this.type==e&&t.push(this),this.boxes){if(t.length&&r)return;a._sweep.call(this.boxes[n],e,t,r)}};var i=function(){this._cursor=new n.Cursor};i.parse=function(e){var t=new i;return t._offset=e._cursor.offset,t._root=e._root?e._root:e,t._raw=e._raw,t._parent=e,t._parseBox(),e._cursor.offset=t._raw.byteOffset+t._raw.byteLength,t},i.prototype._readInt=function(e){var t=null;switch(e){case 8:t=this._raw.getInt8(this._cursor.offset-this._raw.byteOffset);break;case 16:t=this._raw.getInt16(this._cursor.offset-this._raw.byteOffset);break;case 32:t=this._raw.getInt32(this._cursor.offset-this._raw.byteOffset);break;case 64:var r=this._raw.getInt32(this._cursor.offset-this._raw.byteOffset),n=this._raw.getInt32(this._cursor.offset-this._raw.byteOffset+4);t=r*Math.pow(2,32)+n}return this._cursor.offset+=e>>3,t},i.prototype._readUint=function(e){var t=null;switch(e){case 8:t=this._raw.getUint8(this._cursor.offset-this._raw.byteOffset);break;case 16:t=this._raw.getUint16(this._cursor.offset-this._raw.byteOffset);break;case 24:t=((r=this._raw.getUint16(this._cursor.offset-this._raw.byteOffset))<<8)+(n=this._raw.getUint8(this._cursor.offset-this._raw.byteOffset+2));break;case 32:t=this._raw.getUint32(this._cursor.offset-this._raw.byteOffset);break;case 64:var r=this._raw.getUint32(this._cursor.offset-this._raw.byteOffset),n=this._raw.getUint32(this._cursor.offset-this._raw.byteOffset+4);t=r*Math.pow(2,32)+n}return this._cursor.offset+=e>>3,t},i.prototype._readString=function(e){for(var t="",r=0;e>r;r++){var n=this._readUint(8);t+=String.fromCharCode(n)}return t},i.prototype._readTerminatedString=function(){for(var e="";;){var t=this._readUint(8);if(0==t)break;e+=String.fromCharCode(t)}return e},i.prototype._readTemplate=function(e){return this._readUint(e/2)+this._readUint(e/2)/Math.pow(2,e/2)},i.prototype._parseBox=function(){if(this._cursor.offset=this._offset,this._offset+8>this._raw.buffer.byteLength)this._root._incomplete=!0;else{switch(this.size=this._readUint(32),this.type=this._readString(4),1==this.size&&(this.largesize=this._readUint(64)),"uuid"==this.type&&(this.usertype=this._readString(16)),this.size){case 0:this._raw=new DataView(this._raw.buffer,this._offset,this._raw.byteLength-this._cursor.offset);break;case 1:this._offset+this.size>this._raw.buffer.byteLength?(this._incomplete=!0,this._root._incomplete=!0):this._raw=new DataView(this._raw.buffer,this._offset,this.largesize);break;default:this._offset+this.size>this._raw.buffer.byteLength?(this._incomplete=!0,this._root._incomplete=!0):this._raw=new DataView(this._raw.buffer,this._offset,this.size)}!this._incomplete&&this._boxParsers[this.type]&&this._boxParsers[this.type].call(this)}},i.prototype._parseFullBox=function(){this.version=this._readUint(8),this.flags=this._readUint(24)},i.prototype._boxParsers={},["moov","trak","tref","mdia","minf","stbl","edts","dinf","mvex","moof","traf","mfra","udta","meco","strk","vttc"].forEach(function(e){i.prototype._boxParsers[e]=function(){for(this.boxes=[];this._cursor.offset-this._raw.byteOffset<this._raw.byteLength;)this.boxes.push(i.parse(this))}}),i.prototype._boxParsers.elst=function(){this._parseFullBox(),this.entry_count=this._readUint(32),this.entries=[];for(var e=1;e<=this.entry_count;e++){var t={};1==this.version?(t.segment_duration=this._readUint(64),t.media_time=this._readInt(64)):(t.segment_duration=this._readUint(32),t.media_time=this._readInt(32)),t.media_rate_integer=this._readInt(16),t.media_rate_fraction=this._readInt(16),this.entries.push(t)}},i.prototype._boxParsers.emsg=function(){this._parseFullBox(),this.scheme_id_uri=this._readTerminatedString(),this.value=this._readTerminatedString(),this.timescale=this._readUint(32),this.presentation_time_delta=this._readUint(32),this.event_duration=this._readUint(32),this.id=this._readUint(32),this.message_data=new DataView(this._raw.buffer,this._cursor.offset,this._raw.byteLength-(this._cursor.offset-this._offset))},i.prototype._boxParsers.free=i.prototype._boxParsers.skip=function(){this.data=new DataView(this._raw.buffer,this._cursor.offset,this._raw.byteLength-(this._cursor.offset-this._offset))},i.prototype._boxParsers.ftyp=i.prototype._boxParsers.styp=function(){for(this.major_brand=this._readString(4),this.minor_versions=this._readUint(32),this.compatible_brands=[];this._cursor.offset-this._raw.byteOffset<this._raw.byteLength;)this.compatible_brands.push(this._readString(4))},i.prototype._boxParsers.hdlr=function(){this._parseFullBox(),this.pre_defined=this._readUint(32),this.handler_type=this._readString(4),this.reserved=[this._readUint(32),this._readUint(32),this._readUint(32)],this.name=this._readTerminatedString()},i.prototype._boxParsers.mdat=function(){this.data=new DataView(this._raw.buffer,this._cursor.offset,this._raw.byteLength-(this._cursor.offset-this._offset))},i.prototype._boxParsers.mdhd=function(){this._parseFullBox(),1==this.version?(this.creation_time=this._readUint(64),this.modification_time=this._readUint(64),this.timescale=this._readUint(32),this.duration=this._readUint(64)):(this.creation_time=this._readUint(32),this.modification_time=this._readUint(32),this.timescale=this._readUint(32),this.duration=this._readUint(32));var e=this._readUint(16);this.pad=e>>15,this.language=String.fromCharCode(96+(e>>10&31),96+(e>>5&31),96+(31&e)),this.pre_defined=this._readUint(16)},i.prototype._boxParsers.mfhd=function(){this._parseFullBox(),this.sequence_number=this._readUint(32)},i.prototype._boxParsers.mvhd=function(){this._parseFullBox(),1==this.version?(this.creation_time=this._readUint(64),this.modification_time=this._readUint(64),this.timescale=this._readUint(32),this.duration=this._readUint(64)):(this.creation_time=this._readUint(32),this.modification_time=this._readUint(32),this.timescale=this._readUint(32),this.duration=this._readUint(32)),this.rate=this._readTemplate(32),this.volume=this._readTemplate(16),this.reserved1=this._readUint(16),this.reserved2=[this._readUint(32),this._readUint(32)],this.matrix=[];for(var e=0;9>e;e++)this.matrix.push(this._readTemplate(32));for(this.pre_defined=[],e=0;6>e;e++)this.pre_defined.push(this._readUint(32));this.next_track_ID=this._readUint(32)},i.prototype._boxParsers.payl=function(){var e=new DataView(this._raw.buffer,this._cursor.offset,this._raw.byteLength-(this._cursor.offset-this._offset));this.cue_text=n.Utils.dataViewToString(e)},i.prototype._boxParsers.sidx=function(){this._parseFullBox(),this.reference_ID=this._readUint(32),this.timescale=this._readUint(32),0==this.version?(this.earliest_presentation_time=this._readUint(32),this.first_offset=this._readUint(32)):(this.earliest_presentation_time=this._readUint(64),this.first_offset=this._readUint(64)),this.reserved=this._readUint(16),this.reference_count=this._readUint(16),this.references=[];for(var e=0;e<this.reference_count;e++){var t={},r=this._readUint(32);t.reference_type=r>>31&1,t.referenced_size=2147483647&r,t.subsegment_duration=this._readUint(32);var n=this._readUint(32);t.starts_with_SAP=n>>31&1,t.SAP_type=n>>28&7,t.SAP_delta_time=268435455&n,this.references.push(t)}},i.prototype._boxParsers.ssix=function(){this._parseFullBox(),this.subsegment_count=this._readUint(32),this.subsegments=[];for(var e=0;e<this.subsegment_count;e++){var t={};t.ranges_count=this._readUint(32),t.ranges=[];for(var r=0;r<t.ranges_count;r++){var n={};n.level=this._readUint(8),n.range_size=this._readUint(24),t.ranges.push(n)}this.subsegments.push(t)}},i.prototype._boxParsers.tfdt=function(){this._parseFullBox(),1==this.version?this.baseMediaDecodeTime=this._readUint(64):this.baseMediaDecodeTime=this._readUint(32)},i.prototype._boxParsers.tfhd=function(){this._parseFullBox(),this.track_ID=this._readUint(32),1&this.flags&&(this.base_data_offset=this._readUint(64)),2&this.flags&&(this.sample_description_offset=this._readUint(32)),8&this.flags&&(this.default_sample_duration=this._readUint(32)),16&this.flags&&(this.default_sample_size=this._readUint(32)),32&this.flags&&(this.default_sample_flags=this._readUint(32))},i.prototype._boxParsers.tkhd=function(){this._parseFullBox(),1==this.version?(this.creation_time=this._readUint(64),this.modification_time=this._readUint(64),this.track_ID=this._readUint(32),this.reserved1=this._readUint(32),this.duration=this._readUint(64)):(this.creation_time=this._readUint(32),this.modification_time=this._readUint(32),this.track_ID=this._readUint(32),this.reserved1=this._readUint(32),this.duration=this._readUint(32)),this.reserved2=[this._readUint(32),this._readUint(32)],this.layer=this._readUint(16),this.alternate_group=this._readUint(16),this.volume=this._readTemplate(16),this.reserved3=this._readUint(16),this.matrix=[];for(var e=0;9>e;e++)this.matrix.push(this._readTemplate(32));this.width=this._readUint(32),this.height=this._readUint(32)},i.prototype._boxParsers.trun=function(){this._parseFullBox(),this.sample_count=this._readUint(32),1&this.flags&&(this.data_offset=this._readInt(32)),4&this.flags&&(this.first_sample_flags=this._readUint(32)),this.samples=[];for(var e=0;e<this.sample_count;e++){var t={};256&this.flags&&(t.sample_duration=this._readUint(32)),512&this.flags&&(t.sample_size=this._readUint(32)),1024&this.flags&&(t.sample_flags=this._readUint(32)),2048&this.flags&&(0==this.version?t.sample_composition_time_offset=this._readUint(32):t.sample_composition_time_offset=this._readInt(32)),this.samples.push(t)}},i.prototype._boxParsers.vlab=function(){var e=new DataView(this._raw.buffer,this._cursor.offset,this._raw.byteLength-(this._cursor.offset-this._offset));this.source_label=n.Utils.dataViewToString(e)},i.prototype._boxParsers.vttC=function(){var e=new DataView(this._raw.buffer,this._cursor.offset,this._raw.byteLength-(this._cursor.offset-this._offset));this.config=n.Utils.dataViewToString(e)},i.prototype._boxParsers.vtte=function(){}},{}],7:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e,t=this.context,r=(0,i.default)(t).getInstance(),n=void 0,a=void 0,s=void 0;return e={log:function(){var e="",t=null;a&&(t=(new Date).getTime(),e+="["+(t-s)+"]"),e.length>0&&(e+=" "),Array.apply(null,arguments).forEach(function(t){e+=t+" "}),n&&console.log(e),r.trigger(o.default.LOG,{message:e})},setLogTimestampVisible:function(e){a=e},setLogToBrowserConsole:function(e){n=e},getLogToBrowserConsole:function(){return n}},n=!0,a=!0,s=(new Date).getTime(),e}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./EventBus.js")),o=n(e("./events/Events.js")),s=n(e("./FactoryMaker.js"));a.__dashjs_factory_name="Debug",r.default=s.default.getSingletonFactory(a),t.exports=r.default},{"./EventBus.js":8,"./FactoryMaker.js":9,"./events/Events.js":11}],8:[function(e,t,r){"use strict";function n(){function e(e,r,n){var a=t[e];if(!a||0===a.length)return-1;for(var i=0;i<a.length;i++)if(a[i].callback===r&&(!n||n===a[i].scope))return i;return-1}var t={};return{on:function(r,n,a){if(!r)throw new Error("event type cannot be null or undefined");if(!n||"function"!=typeof n)throw new Error("listener must be a function: "+n);if(!(e(r,n,a)>=0)){var i={callback:n,scope:a};t[r]=t[r]||[],t[r].push(i)}},off:function(r,n,a){if(r&&n&&t[r]){var i=e(r,n,a);0>i||t[r].splice(i,1)}},trigger:function(e,r){if(e&&t[e]){if((r=r||{}).hasOwnProperty("type"))throw new Error("'type' is a reserved word for event dispatching");r.type=e,t[e].forEach(function(e){e.callback.call(e.scope,r)})}},reset:function(){t={}}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("./FactoryMaker.js"));n.__dashjs_factory_name="EventBus",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"./FactoryMaker.js":9}],9:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r in i){var n=i[r];if(n.context===e&&n.name===t)return n.instance}return null}function t(e,t,a,i){var o=r(a)[e];if(o){var s=o.instance;if(!o.override)return s.apply({context:a,factory:n},i);for(var u in s=s.apply({context:a,factory:n,parent:t},i))t.hasOwnProperty(u)&&(t[u]=s[u])}return t}function r(e){var t=void 0;return a.forEach(function(r){r===e&&(t=r)}),t||(t=a.push(e)),t}var n=void 0,a=[],i=[];return n={extend:function(e,t,n,a){var i=r(a);!i[e]&&t&&(i[e]={instance:t,override:n})},getSingletonInstance:e,setSingletonInstance:function(e,t,r){for(var n in i){var a=i[n];if(a.context===e&&a.name===t)return void(i[n].instance=r)}i.push({name:t,context:e,instance:r})},getSingletonFactory:function(r){return function(n){var a=void 0;return void 0===n&&(n={}),{getInstance:function(){return a||(a=e(n,r.__dashjs_factory_name)),a||(a=t(r.__dashjs_factory_name,r.apply({context:n},arguments),n,arguments),i.push({name:r.__dashjs_factory_name,context:n,instance:a})),a}}}},getClassFactory:function(e){return function(r){return void 0===r&&(r={}),{create:function(){return t(e.__dashjs_factory_name,e.apply({context:r},arguments),r,arguments)}}}}}}();r.default=n,t.exports=r.default},{}],10:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){function t(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,t),function(e,t,r){for(var n=!0;n;){var a=e,i=t,o=r;n=!1,null===a&&(a=Function.prototype);var s=Object.getOwnPropertyDescriptor(a,i);if(void 0!==s){if("value"in s)return s.value;var u=s.get;if(void 0===u)return;return u.call(o)}var l=Object.getPrototypeOf(a);if(null===l)return;e=l,t=i,r=o,n=!0,s=l=void 0}}(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),this.AST_IN_FUTURE="astinfuture",this.BUFFERING_COMPLETED="bufferingCompleted",this.BUFFER_CLEARED="bufferCleared",this.BUFFER_LEVEL_UPDATED="bufferLevelUpdated",this.BYTES_APPENDED="bytesAppended",this.CHECK_FOR_EXISTENCE_COMPLETED="checkForExistenceCompleted",this.CHUNK_APPENDED="chunkAppended",this.CURRENT_TRACK_CHANGED="currenttrackchanged",this.DATA_UPDATE_COMPLETED="dataUpdateCompleted",this.DATA_UPDATE_STARTED="dataUpdateStarted",this.FRAGMENT_LOADING_COMPLETED="fragmentLoadingCompleted",this.FRAGMENT_LOADING_STARTED="fragmentLoadingStarted",this.INITIALIZATION_LOADED="initializationLoaded",this.INIT_FRAGMENT_LOADED="initFragmentLoaded",this.INIT_REQUESTED="initRequested",this.INTERNAL_MANIFEST_LOADED="internalManifestLoaded",this.LIVE_EDGE_SEARCH_COMPLETED="liveEdgeSearchCompleted",this.LOADING_COMPLETED="loadingCompleted",this.LOADING_PROGRESS="loadingProgress",this.MANIFEST_UPDATED="manifestUpdated",this.MEDIA_FRAGMENT_LOADED="mediaFragmentLoaded",this.QUALITY_CHANGED="qualityChanged",this.QUOTA_EXCEEDED="quotaExceeded",this.REPRESENTATION_UPDATED="representationUpdated",this.SEGMENTS_LOADED="segmentsLoaded",this.SERVICE_LOCATION_BLACKLIST_CHANGED="serviceLocationBlacklistChanged",this.SOURCEBUFFER_APPEND_COMPLETED="sourceBufferAppendCompleted",this.SOURCEBUFFER_REMOVE_COMPLETED="sourceBufferRemoveCompleted",this.STREAMS_COMPOSED="streamsComposed",this.STREAM_BUFFERING_COMPLETED="streamBufferingCompleted",this.STREAM_COMPLETED="streamCompleted",this.STREAM_INITIALIZED="streaminitialized",this.STREAM_TEARDOWN_COMPLETE="streamTeardownComplete",this.TIMED_TEXT_REQUESTED="timedTextRequested",this.TIME_SYNCHRONIZATION_COMPLETED="timeSynchronizationComplete",this.URL_RESOLUTION_FAILED="urlResolutionFailed",this.WALLCLOCK_TIME_UPDATED="wallclockTimeUpdated",this.XLINK_ALL_ELEMENTS_LOADED="xlinkAllElementsLoaded",this.XLINK_ELEMENT_LOADED="xlinkElementLoaded",this.XLINK_READY="xlinkReady"}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(function(e){return e&&e.__esModule?e:{default:e}}(e("./EventsBase.js")).default);r.default=n,t.exports=r.default},{"./EventsBase.js":12}],11:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){function t(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,t),function(e,t,r){for(var n=!0;n;){var a=e,i=t,o=r;n=!1,null===a&&(a=Function.prototype);var s=Object.getOwnPropertyDescriptor(a,i);if(void 0!==s){if("value"in s)return s.value;var u=s.get;if(void 0===u)return;return u.call(o)}var l=Object.getPrototypeOf(a);if(null===l)return;e=l,t=i,r=o,n=!0,s=l=void 0}}(Object.getPrototypeOf(t.prototype),"constructor",this).apply(this,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(function(e){return e&&e.__esModule?e:{default:e}}(e("./CoreEvents.js")).default),a=new n;r.default=a,t.exports=r.default},{"./CoreEvents.js":10}],12:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),a=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return n(e,[{key:"extend",value:function(e,t){if(e){var r=!!t&&t.override,n=!!t&&t.publicOnly;for(var a in e)!e.hasOwnProperty(a)||this[a]&&!r||n&&-1===e[a].indexOf("public_")||(this[a]=e[a])}}}]),e}();r.default=a,t.exports=r.default},{}],13:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e,t){return t.getRepresentationForQuality(e.quality)}function t(e){return v[e.streamInfo.id][e.index]}function r(e){for(var t=m.length,r=0;t>r;r++){var n=m[r];if(e.id===n.id)return n}return null}function n(e,t){var r=new i.default,n=t.adaptation.period.mpd.manifest.Period_asArray[t.adaptation.period.index].AdaptationSet_asArray[t.adaptation.index],o=p.getRepresentationFor(t.index,n);return r.id=t.id,r.quality=t.index,r.bandwidth=p.getBandwidth(o),r.DVRWindow=t.segmentAvailabilityRange,r.fragmentDuration=t.segmentDuration||(t.segments&&t.segments.length>0?t.segments[0].duration:NaN),r.MSETimeOffset=t.MSETimeOffset,r.useCalculatedLiveEdgeTime=t.useCalculatedLiveEdgeTime,r.mediaInfo=a(e,t.adaptation),r}function a(e,t){var r,n=new o.default,a=t.period.mpd.manifest.Period_asArray[t.period.index].AdaptationSet_asArray[t.index];return n.id=t.id,n.index=t.index,n.type=t.type,n.streamInfo=g(e,t.period),n.representationCount=p.getRepresentationCount(a),n.lang=p.getLanguageForAdaptation(a),r=p.getViewpointForAdaptation(a),n.viewpoint=r?r.value:void 0,n.accessibility=p.getAccessibilityForAdaptation(a).map(function(e){var t=e.value,r=t;return e.schemeIdUri&&e.schemeIdUri.search("cea-608")>=0&&void 0!==f.default&&(r=t?"cea-608:"+t:"cea-608",n.embeddedCaptions=!0),r}),n.audioChannelConfiguration=p.getAudioChannelConfigurationForAdaptation(a).map(function(e){return e.value}),n.roles=p.getRolesForAdaptation(a).map(function(e){return e.value}),n.codec=p.getCodec(a),n.mimeType=p.getMimeType(a),n.contentProtection=p.getContentProtectionData(a),n.bitrateList=p.getBitrateListForAdaptation(a),n.contentProtection&&n.contentProtection.forEach(function(e){e.KID=p.getKID(e)}),n.isText=p.getIsTextTrack(n.mimeType),n}function d(e,t,r){e.id=t,e.index=100+parseInt(t.substring(2,3)),e.type="embeddedText",e.codec="cea-608-in-SEI",e.isText=!0,e.isEmbedded=!0,e.lang=t+" "+r,e.roles=["caption"]}function g(e,t){var r=new s.default;return r.id=t.id,r.index=t.index,r.start=t.start,r.duration=t.duration,r.manifestInfo=h(e,t.mpd),r.isLast=1===e.Period_asArray.length||Math.abs(r.start+r.duration-r.manifestInfo.duration)<1,r.isFirst=1===e.Period_asArray.length||p.getRegularPeriods(e,p.getMpd(e))[0].id===t.id,r}function h(e,t){var r=new u.default;return r.DVRWindowSize=t.timeShiftBufferDepth,r.loadedTime=t.manifest.loadedTime,r.availableFrom=t.availabilityStartTime,r.minBufferTime=t.manifest.minBufferTime,r.maxFragmentDuration=t.maxSegmentDuration,r.duration=p.getDuration(e),r.isDynamic=p.getIsDynamic(e),r}var p=void 0,m=void 0,v=void 0;return{initialize:function(){m=[],v={}},convertDataToTrack:n,convertDataToMedia:a,convertDataToStream:g,getDataForTrack:e,getDataForMedia:t,getDataForStream:r,getStreamsInfo:function(e){var t,r,n,a=[];if(!e)return null;for(t=p.getMpd(e),m=p.getRegularPeriods(e,t),t.checkTime=p.getCheckTime(e,m[0]),v={},r=m.length,n=0;r>n;n++)a.push(g(e,m[n]));return a},getManifestInfo:function(e){return h(e,p.getMpd(e))},getMediaInfoForType:function(e,t,n){var i,o=r(t),s=o.id,u=p.getAdaptationForType(e,t.index,n);return u?(i=p.getIndexForAdaptation(u,e,t.index),v[s]=v[s]||p.getAdaptationsForPeriod(e,o),a(e,v[s][i])):null},getAllMediaInfoForType:function(e,t,n){var i,o,s,u,l,f,c=r(t),g=c.id,h=p.getAdaptationsForType(e,t.index,"embeddedText"!==n?n:"video"),m=[];if(!h)return m;for(v[g]=v[g]||p.getAdaptationsForPeriod(e,c),u=0,f=h.length;f>u;u++){if(i=h[u],s=p.getIndexForAdaptation(i,e,t.index),o=a(e,v[g][s]),"embeddedText"===n){var y=o.accessibility.length;for(l=0;y>l;l++)if(o){var _=o.accessibility[l];if(0===_.indexOf("cea-608:")){var E=_.substring(8).split(";");if("CC"===E[0].substring(0,2))for(l=0;l<E.length;l++)o||(o=a.call(this,e,v[g][s])),d(o,E[l].substring(0,3),E[l].substring(4)),m.push(o),o=null;else for(l=0;l<E.length;l++)o||(o=a.call(this,e,v[g][s])),d(o,"CC"+(l+1),E[l]),m.push(o),o=null}else 0===_.indexOf("cea-608")&&(d(o,"CC1","eng"),m.push(o),o=null)}}o&&"embeddedText"!==n&&m.push(o)}return m},getCurrentRepresentationInfo:function(e,t){var r=t.getCurrentRepresentation();return r?n(e,r):null},getRepresentationInfoForQuality:function(e,t,r){var a=t.getRepresentationForQuality(r);return a?n(e,a):null},updateData:function(e,n){var a,i,o=r(n.getStreamInfo()),s=n.getMediaInfo(),u=t(s),l=n.getType();i=(a=s.id)?p.getAdaptationForId(a,e,o.index):p.getAdaptationForIndex(s.index,e,o.index),n.getRepresentationController().updateData(i,u,l)},getInitRequest:function(e,t){var r=e.getRepresentationController().getRepresentationForQuality(t);return e.getIndexHandler().getInitRequest(r)},getNextFragmentRequest:function(t,r){var n=e(r,t.getRepresentationController());return t.getIndexHandler().getNextSegmentRequest(n)},getFragmentRequestForTime:function(t,r,n,a){var i=e(r,t.getRepresentationController());return t.getIndexHandler().getSegmentRequestForTime(i,n,a)},generateFragmentRequestForTime:function(t,r,n){var a=e(r,t.getRepresentationController());return t.getIndexHandler().generateSegmentRequestForTime(a,n)},getIndexHandlerTime:function(e){return e.getIndexHandler().getCurrentTime()},setIndexHandlerTime:function(e,t){return e.getIndexHandler().setCurrentTime(t)},getEventsFor:function(n,a,u){var l=[];return a instanceof s.default?l=p.getEventsForPeriod(n,r(a)):a instanceof o.default?l=p.getEventStreamForAdaptationSet(n,t(a)):a instanceof i.default&&(l=p.getEventStreamForRepresentation(n,e(a,u.getRepresentationController()))),l},getEvent:function(e,t,r){var n=new l.default,a=e.scheme_id_uri,i=e.value,o=e.timescale,s=e.presentation_time_delta,u=e.event_duration,d=e.id,f=e.message_data,c=r*o+s;return t[a]?(n.eventStream=t[a],n.eventStream.value=i,n.eventStream.timescale=o,n.duration=u,n.id=d,n.presentationTime=c,n.messageData=f,n.presentationTimeDelta=s,n):null},setConfig:function(e){e&&e.dashManifestModel&&(p=e.dashManifestModel)},reset:function(){m=[],v={}},metricsList:c}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../streaming/vo/TrackInfo.js")),o=n(e("../streaming/vo/MediaInfo.js")),s=n(e("../streaming/vo/StreamInfo.js")),u=n(e("../streaming/vo/ManifestInfo.js")),l=n(e("./vo/Event.js")),d=n(e("../core/FactoryMaker.js")),f=n(e("../../externals/cea608-parser.js")),c={TCP_CONNECTION:"TcpList",HTTP_REQUEST:"HttpList",TRACK_SWITCH:"RepSwitchList",BUFFER_LEVEL:"BufferLevel",BUFFER_STATE:"BufferState",DVR_INFO:"DVRInfo",DROPPED_FRAMES:"DroppedFrames",SCHEDULING_INFO:"SchedulingInfo",REQUESTS_QUEUE:"RequestsQueue",MANIFEST_UPDATE:"ManifestUpdate",MANIFEST_UPDATE_STREAM_INFO:"ManifestUpdatePeriodInfo",MANIFEST_UPDATE_TRACK_INFO:"ManifestUpdateRepresentationInfo",PLAY_LIST:"PlayList",DVB_ERRORS:"DVBErrors"};a.__dashjs_factory_name="DashAdapter",r.default=d.default.getSingletonFactory(a),t.exports=r.default},{"../../externals/cea608-parser.js":2,"../core/FactoryMaker.js":9,"../streaming/vo/ManifestInfo.js":153,"../streaming/vo/MediaInfo.js":154,"../streaming/vo/StreamInfo.js":156,"../streaming/vo/TrackInfo.js":159,"./vo/Event.js":29}],14:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e,t,r){var n,a,i=D.resolve(r.path);return i&&t!==i.url&&R.isRelative(t)?(n=i.url,a=i.serviceLocation,t&&(n+=t)):n=t,!R.isRelative(n)&&(e.url=n,e.serviceLocation=a,!0)}function r(e){var t,r,n,a=e.adaptation.period,i=e.segmentInfoType,o=!1;return 0>O?o=!1:w||O<e.availableSegmentsNumber?(r=(0,g.getSegmentByIndex)(O,e))&&(n=r.presentationStartTime-a.start,t=e.adaptation.period.duration,M(e.segmentInfoType+": "+n+" / "+t),o=("SegmentTimeline"!==i||!w)&&n>=t):o=!0,o}function n(e){return B.getSegments(e,P,O,a)}function a(e,t){if(e.segments=t,t&&t.length>0&&(L=isNaN(L)?t[0].presentationStartTime:Math.min(t[0].presentationStartTime,L)),w&&isNaN(C.getExpectedLiveEdge())){var r=t[t.length-1].presentationStartTime,n=j.getMetricsFor("stream");C.setExpectedLiveEdge(r),j.updateManifestUpdateInfo(A.getCurrentManifestUpdate(n),{presentationStartTime:r})}}function d(e){if(!e)throw new o.default("no representation");return e.segments=null,n(e),e}function m(e,t,r){var n,a,i,o,s,u=t.segments,l=u?u.length:null,d=-1;if(u&&l>0)for(s=0;l>s;s++)if(i=(a=u[s]).presentationStartTime,o=a.duration,e+(n=null==r?o/2:r)>=i&&i+o>e-n){d=a.availabilityIdx;break}return d}function v(e){if(null==e)return null;var r=new i.default,n=e.representation,a=n.adaptation.period.mpd.manifest.Period_asArray[n.adaptation.period.index].AdaptationSet_asArray[n.adaptation.index].Representation_asArray[n.index].bandwidth,o=e.media;return o=(0,g.replaceTokenForTemplate)(o,"Number",e.replacementNumber),o=(0,g.replaceTokenForTemplate)(o,"Time",e.replacementTime),o=function(e,t){if(null===t||-1===e.indexOf("$RepresentationID$"))return e;var r=t.toString();return e.split("$RepresentationID$").join(r)}(o=(0,g.replaceTokenForTemplate)(o,"Bandwidth",a),n.id),o=function(e){return e.split("$$").join("$")}(o),r.mediaType=N,r.type=s.default.MEDIA_SEGMENT_TYPE,r.range=e.mediaRange,r.startTime=e.presentationStartTime,r.duration=e.duration,r.timescale=n.timescale,r.availabilityStartTime=e.availabilityStartTime,r.availabilityEndTime=e.availabilityEndTime,r.wallStartTime=e.wallStartTime,r.quality=n.index,r.index=e.availabilityIdx,r.mediaInfo=F.getMediaInfo(),r.adaptationIndex=n.adaptation.index,t(r,o,n)?r:void 0}function y(e,t,a){var o,s=O,u=!!a&&a.keepIdx,l=a?a.timeThreshold:null,d=!(!a||!a.ignoreIsFinished);return e?(P!==t&&(P=t,M("Getting the request for "+N+" time : "+t)),O=m(t,e,l),n(e),0>O&&(O=m(t,e,l)),O>0&&M("Index for "+N+" time "+t+" is "+O),!d&&r(e)?((o=new i.default).action=i.default.ACTION_COMPLETE,o.index=O,o.mediaType=N,o.mediaInfo=F.getMediaInfo(),M("Signal complete.",o)):o=v((0,g.getSegmentByIndex)(O,e)),u&&s>=0&&(O="SegmentTimeline"===e.segmentInfoType&&w?O:s),o):null}function _(e){var t=e.representation;t.segments&&S.trigger(u.default.REPRESENTATION_UPDATED,{sender:this,representation:t})}function E(e){if(!e.error&&N===e.mediaType){var t,r,n,i,o=e.segments,s=e.representation,l=[],d=0;for(t=0,r=o.length;r>t;t++)n=o[t],i=(0,g.getTimeBasedSegment)(C,w,s,n.startTime,n.duration,n.timescale,n.media,n.mediaRange,d),l.push(i),i=null,d++;s.segmentAvailabilityRange={start:l[0].presentationStartTime,end:l[r-1].presentationStartTime},s.availableSegmentsNumber=r,a(s,l),s.initialization&&S.trigger(u.default.REPRESENTATION_UPDATED,{sender:this,representation:s})}}var T=this.context,M=(0,f.default)(T).getInstance().log,S=(0,l.default)(T).getInstance(),R=(0,c.default)(T).getInstance(),I=e.segmentBaseLoader,C=e.timelineConverter,A=e.dashMetrics,j=e.metricsModel,D=e.baseURLController,b=void 0,O=void 0,P=void 0,w=void 0,N=void 0,x=void 0,L=void 0,F=void 0,B=void 0;return b={initialize:function(t){N=(F=t).getType(),w=F.isDynamic(),B=(0,h.default)(T).create(e,w)},getStreamProcessor:function(){return F},getInitRequest:function(e){return e?function(e,r){var n,a,o=new i.default;return n=e.adaptation.period,o.mediaType=r,o.type=s.default.INIT_SEGMENT_TYPE,o.range=e.range,a=n.start,o.availabilityStartTime=C.calcAvailabilityStartTimeFromPresentationTime(a,e.adaptation.period.mpd,w),o.availabilityEndTime=C.calcAvailabilityEndTimeFromPresentationTime(a+n.duration,n.mpd,w),o.quality=e.index,o.mediaInfo=F.getMediaInfo(),t(o,e.initialization,e)?o:void 0}(e,N):null},getSegmentRequestForTime:y,getNextSegmentRequest:function(e){var t,a;return e&&-1!==O?(P=null,O++,M("Getting the next request at index: "+O),r(e)?((t=new i.default).action=i.default.ACTION_COMPLETE,t.index=O,t.mediaType=N,t.mediaInfo=F.getMediaInfo(),M("Signal complete.")):(n(e),t=v(a=(0,g.getSegmentByIndex)(O,e)),!a&&w&&O--),t):null},generateSegmentRequestForTime:function(e,t){var r=(e.segmentAvailabilityRange.end-e.segmentAvailabilityRange.start)/2;return e.segments=null,e.segmentAvailabilityRange={start:t-r,end:t+r},y(e,t,{keepIdx:!1,ignoreIsFinished:!0})},updateRepresentation:function(e,t){var r,n=e.initialization,a="BaseURL"!==e.segmentInfoType&&"SegmentBase"!==e.segmentInfoType;return e.segmentDuration||e.segments||d(e),e.segmentAvailabilityRange=null,e.segmentAvailabilityRange=C.calcSegmentAvailabilityRange(e,w),e.segmentAvailabilityRange.end<e.segmentAvailabilityRange.start&&!e.useCalculatedLiveEdgeTime?(r=new o.default(p,"no segments are available yet",{availabilityDelay:e.segmentAvailabilityRange.start-e.segmentAvailabilityRange.end}),void S.trigger(u.default.REPRESENTATION_UPDATED,{sender:this,representation:e,error:r})):(t||(O=-1),e.segmentDuration&&d(e),n||I.loadInitialization(e),a||I.loadSegments(e,N,e.indexRange),void(n&&a&&S.trigger(u.default.REPRESENTATION_UPDATED,{sender:this,representation:e})))},setCurrentTime:function(e){x=e},getCurrentTime:function(){return x},getCurrentIndex:function(){return O},getEarliestTime:function(){return L},reset:function(){B=null,x=0,L=NaN,P=NaN,O=-1,w=null,N=null,F=null,S.off(u.default.INITIALIZATION_LOADED,_,b),S.off(u.default.SEGMENTS_LOADED,E,b)}},O=-1,x=0,L=NaN,S.on(u.default.INITIALIZATION_LOADED,_,b),S.on(u.default.SEGMENTS_LOADED,E,b),b}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../streaming/vo/FragmentRequest.js")),o=n(e("../streaming/vo/Error.js")),s=n(e("../streaming/vo/metrics/HTTPRequest.js")),u=n(e("../core/events/Events.js")),l=n(e("../core/EventBus.js")),d=n(e("../core/FactoryMaker.js")),f=n(e("../core/Debug.js")),c=n(e("../streaming/utils/URLUtils.js")),g=e("./utils/SegmentsUtils.js"),h=n(e("./utils/SegmentsGetter.js")),p=1;a.__dashjs_factory_name="DashHandler";var m=d.default.getClassFactory(a);m.SEGMENTS_UNAVAILABLE_ERROR_CODE=p,r.default=m,t.exports=r.default},{"../core/Debug.js":7,"../core/EventBus.js":8,"../core/FactoryMaker.js":9,"../core/events/Events.js":11,"../streaming/utils/URLUtils.js":145,"../streaming/vo/Error.js":149,"../streaming/vo/FragmentRequest.js":150,"../streaming/vo/metrics/HTTPRequest.js":166,"./utils/SegmentsGetter.js":22,"./utils/SegmentsUtils.js":23}],15:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e){if(null===e)return null;var t,r=e.HttpList,n=null;if(null===r||r.length<=0)return null;for(t=r.length-1;t>=0;){if(r[t].responsecode){n=r[t];break}t--}return n}function t(e){return null===e?[]:e.HttpList?e.HttpList:[]}function r(e){var t={};if(!e)return t;for(var r=e.split("\r\n"),n=0,a=r.length;a>n;n++){var i=r[n],o=i.indexOf(": ");o>0&&(t[i.substring(0,o)]=i.substring(o+2))}return t}function n(e,t){return(0,u.default)(a).getInstance().getIsTypeOf(e,t)}var a=this.context,l=(0,s.default)(a).getInstance();return{getBandwidthForRepresentation:function(e,t){var r;return r=function(e,t){var r,n,a,i,o;for(r=e.AdaptationSet_asArray,i=0;i<r.length;i+=1)for(a=r[i].Representation_asArray,o=0;o<a.length;o+=1)if(t===(n=a[o]).id)return n;return null}(l.getValue().Period_asArray[t],e),null===r?null:r.bandwidth},getIndexForRepresentation:function(e,t){return function(e,t){var r,n,a,i;for(r=e.AdaptationSet_asArray,a=0;a<r.length;a+=1)for(n=r[a].Representation_asArray,i=0;i<n.length;i+=1)if(t===n[i].id)return i;return-1}(l.getValue().Period_asArray[t],e)},getMaxIndexForBufferType:function(e,t){return function(e,t){var r,a,i,o;if(!e||!t)return-1;for(a=e.AdaptationSet_asArray,o=0;o<a.length;o+=1)if(i=(r=a[o]).Representation_asArray,n(r,t))return i.length;return-1}(l.getValue().Period_asArray[t],e)},getMaxAllowedIndexForBufferType:function(e,t){var r=0,n=(0,o.default)(a).getInstance();return n&&(r=n.getTopQualityIndexFor(e,t)),r},getCurrentRepresentationSwitch:function(e){if(null===e)return null;var t=e.RepSwitchList;return null===t||t.length<=0?null:t[t.length-1]},getLatestBufferLevelVO:function(e){if(null===e)return null;var t=e.BufferLevel;return null===t||t.length<=0?null:t[t.length-1]},getCurrentBufferLevel:function(e){if(null===e)return 0;var t=e.BufferLevel;return null===t||t.length<=0?0:t[t.length-1].level/1e3},getCurrentHttpRequest:e,getHttpRequests:t,getCurrentDroppedFrames:function(e){if(null===e)return null;var t=e.DroppedFrames;return null===t||t.length<=0?null:t[t.length-1]},getCurrentSchedulingInfo:function(e){if(null===e)return null;var t=e.SchedulingInfo;return null===t||t.length<=0?null:t[t.length-1]},getCurrentDVRInfo:function(e){if(null===e)return null;var t=e.DVRInfo;return null===t||t.length<=0?null:t[t.length-1]},getCurrentManifestUpdate:function(e){if(null===e)return null;var t=e.ManifestUpdate;return null===t||t.length<=0?null:t[t.length-1]},getLatestFragmentRequestHeaderValueByID:function(t,n){if(null===t)return null;var a,i=e(t);return null===i||null===i._responseHeaders||void 0===(a=r(i._responseHeaders))[n]?null:a[n]},getLatestMPDRequestHeaderValueByID:function(e,n){var a,o,s,u={};if(null===e)return null;for(s=(a=t(e)).length-1;s>=0;s--)if((o=a[s]).type===i.default.MPD_TYPE){u=r(o._responseHeaders);break}return void 0===u[n]?null:u[n]},getRequestsQueue:function(e){return e.RequestsQueue}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../streaming/vo/metrics/HTTPRequest.js")),o=n(e("../streaming/controllers/AbrController.js")),s=n(e("../streaming/models/ManifestModel.js")),u=n(e("./models/DashManifestModel.js")),l=n(e("../core/FactoryMaker.js"));a.__dashjs_factory_name="DashMetrics",r.default=l.default.getSingletonFactory(a),t.exports=r.default},{"../core/FactoryMaker.js":9,"../streaming/controllers/AbrController.js":49,"../streaming/models/ManifestModel.js":89,"../streaming/vo/metrics/HTTPRequest.js":166,"./models/DashManifestModel.js":19}],16:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e,t=this.context,r=(0,s.default)(t).getInstance().log,n=void 0,a=void 0,o=void 0,d=void 0;return e={parse:function(e,n){var a,o=new l.default(d,"",!0),s=new u.default(function(){var e=[];return e.push(function(){var e,t,r,n;return n=[{name:"profiles",merge:!1},{name:"width",merge:!1},{name:"height",merge:!1},{name:"sar",merge:!1},{name:"frameRate",merge:!1},{name:"audioSamplingRate",merge:!1},{name:"mimeType",merge:!1},{name:"segmentProfiles",merge:!1},{name:"codecs",merge:!1},{name:"maximumSAPPeriod",merge:!1},{name:"startsWithSap",merge:!1},{name:"maxPlayoutRate",merge:!1},{name:"codingDependency",merge:!1},{name:"scanType",merge:!1},{name:"FramePacking",merge:!0},{name:"AudioChannelConfiguration",merge:!0},{name:"ContentProtection",merge:!0}],(e={}).name="AdaptationSet",e.isRoot=!1,e.isArray=!0,e.parent=null,e.children=[],e.properties=n,(t={}).name="Representation",t.isRoot=!1,t.isArray=!0,t.parent=e,t.children=[],t.properties=n,e.children.push(t),(r={}).name="SubRepresentation",r.isRoot=!1,r.isArray=!0,r.parent=t,r.children=[],r.properties=n,t.children.push(r),e}()),e.push(function(){var e,t,r,n;return n=[{name:"SegmentBase",merge:!0},{name:"SegmentTemplate",merge:!0},{name:"SegmentList",merge:!0}],(e={}).name="Period",e.isRoot=!1,e.isArray=!0,e.parent=null,e.children=[],e.properties=n,(t={}).name="AdaptationSet",t.isRoot=!1,t.isArray=!0,t.parent=e,t.children=[],t.properties=n,e.children.push(t),(r={}).name="Representation",r.isRoot=!1,r.isArray=!0,r.parent=t,r.children=[],r.properties=n,t.children.push(r),e}()),e}()),f=new Date,c=null,g=null;try{if(!(a=o.xml_str2json(e)))throw"parser error";c=new Date,a.hasOwnProperty("Location")&&(a.Location=a.Location_asArray[0]),s.run(a),g=new Date,n.setMatchers(d),n.setIron(s),r("Parsing complete: ( xml2json: "+(c.getTime()-f.getTime())+"ms, objectiron: "+(g.getTime()-c.getTime())+"ms, total: "+(g.getTime()-f.getTime())/1e3+"s)")}catch(r){return(0,i.default)(t).getInstance().manifestError("parsing the manifest failed","parse",e),null}return a}},n=/^([-])?P(([\d.]*)Y)?(([\d.]*)M)?(([\d.]*)D)?T?(([\d.]*)H)?(([\d.]*)M)?(([\d.]*)S)?/,a=/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2})(?::([0-9]*)(\.[0-9]*)?)?(?:([+-])([0-9]{2})([0-9]{2}))?/,o=/^[-+]?[0-9]+[.]?[0-9]*([eE][-+]?[0-9]+)?$/,d=[{type:"duration",test:function(e){for(var t=["minBufferTime","mediaPresentationDuration","minimumUpdatePeriod","timeShiftBufferDepth","maxSegmentDuration","maxSubsegmentDuration","suggestedPresentationDelay","start","starttime","duration"],r=t.length,a=0;r>a;a++)if(e.nodeName===t[a])return n.test(e.value);return!1},converter:function(e){var t=n.exec(e),r=31536e3*parseFloat(t[2]||0)+2592e3*parseFloat(t[4]||0)+86400*parseFloat(t[6]||0)+3600*parseFloat(t[8]||0)+60*parseFloat(t[10]||0)+parseFloat(t[12]||0);return void 0!==t[1]&&(r=-r),r}},{type:"datetime",test:function(e){return a.test(e.value)},converter:function(e){var t,r=a.exec(e);if(t=Date.UTC(parseInt(r[1],10),parseInt(r[2],10)-1,parseInt(r[3],10),parseInt(r[4],10),parseInt(r[5],10),r[6]&&parseInt(r[6],10)||0,r[7]&&1e3*parseFloat(r[7])||0),r[9]&&r[10]){var n=60*parseInt(r[9],10)+parseInt(r[10],10);t+=("+"===r[8]?-1:1)*n*60*1e3}return new Date(t)}},{type:"numeric",test:function(e){return o.test(e.value)},converter:function(e){return parseFloat(e)}}],e}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../streaming/utils/ErrorHandler.js")),o=n(e("../core/FactoryMaker.js")),s=n(e("../core/Debug.js")),u=n(e("../../externals/objectiron.js")),l=n(e("../../externals/xml2json.js"));a.__dashjs_factory_name="DashParser",r.default=o.default.getClassFactory(a),t.exports=r.default},{"../../externals/objectiron.js":3,"../../externals/xml2json.js":4,"../core/Debug.js":7,"../core/FactoryMaker.js":9,"../streaming/utils/ErrorHandler.js":139}],17:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e,t){for(var r,n,a,i,s=e.references,u=s.length,l=e.timescale,d=e.earliest_presentation_time,f=t.range.start+e.first_offset+e.size,c=[],g=0;u>g;g++)a=s[g].subsegment_duration,i=s[g].referenced_size,(r=new o.default).duration=a,r.media=t.url,r.startTime=d,r.timescale=l,n=f+i-1,r.mediaRange=f+"-"+n,c.push(r),d+=a,f+=i;return c}function t(e,t){t.url&&(e.open("GET",m.modifyRequestURL(t.url)),e.responseType="arraybuffer",e.setRequestHeader("Range","bytes="+t.range.start+"-"+t.range.end),(e=m.modifyRequestHeader(e)).send(null))}function r(e,t,r){e?c.trigger(l.default.SEGMENTS_LOADED,{segments:e,representation:t,mediaType:r}):c.trigger(l.default.SEGMENTS_LOADED,{segments:null,representation:t,mediaType:r,error:new s.default(null,"error loading segments",null)})}var n=this.context,a=(0,g.default)(n).getInstance().log,c=(0,d.default)(n).getInstance(),h=void 0,p=void 0,m=void 0,v=void 0;return{setConfig:function(e){e.baseURLController&&(v=e.baseURLController)},initialize:function(){h=(0,u.default)(n).getInstance(),p=(0,f.default)(n).getInstance(),m=(0,i.default)(n).getInstance()},loadInitialization:function e(r,n){var i=!0,o=null,s=null,u=new XMLHttpRequest,d=v.resolve(r.path),f=n||{url:d?d.url:void 0,range:{start:0,end:1500},searching:!1,bytesLoaded:0,bytesToLoad:1500,request:u};a("Start searching for initialization."),u.onload=function(){u.status<200||u.status>299||(i=!1,f.bytesLoaded=f.range.end,s=p.parse(u.response),o=function(e){var t,r,n=e.getBox("ftyp"),i=e.getBox("moov"),o=null;return a("Searching for initialization."),i&&i.isComplete&&(t=n?n.offset:i.offset,r=i.offset+i.size-1,a("Found the initialization.  Range: "+(o=t+"-"+r))),o}(s),o?(r.range=o,r.initialization=f.url,c.trigger(l.default.INITIALIZATION_LOADED,{representation:r})):(f.range.end=f.bytesLoaded+f.bytesToLoad,e(r,f)))},u.onloadend=u.onerror=function(){i&&(i=!1,h.downloadError("initialization",f.url,u),c.trigger(l.default.INITIALIZATION_LOADED,{representation:r}))},t(u,f),a("Perform init search: "+f.url)},loadSegments:function n(i,o,s,u,l){if(s&&(void 0===s.start||void 0===s.end)){var d=s?s.toString().split("-"):null;s=d?{start:parseFloat(d[0]),end:parseFloat(d[1])}:null}l=l||r;var f=!0,c=null,g=null,m=!!s,y=new XMLHttpRequest,_=v.resolve(i.path),E={url:_?_.url:void 0,range:m?s:{start:0,end:1500},searching:!m,bytesLoaded:u?u.bytesLoaded:0,bytesToLoad:1500,request:y};y.onload=function(){if(!(y.status<200||y.status>299)){var t=E.bytesToLoad,r=y.response.byteLength;if(f=!1,E.bytesLoaded=E.range.end-E.range.start,c=p.parse(y.response),(g=c.getBox("sidx"))&&g.isComplete){var s,u,d=g.references;if(null!=d&&d.length>0&&(s=1===d[0].reference_type),s){a("Initiate multiple SIDX load."),E.range.end=E.range.start+g.size;var h,m,v,_,T=[],M=0,S=(g.offset||E.range.start)+g.size,R=function(e){e?(T=T.concat(e),++M>=m&&l(T,i,o)):l(null,i,o)};for(h=0,m=d.length;m>h;h++)v=S,_=S+d[h].referenced_size-1,S+=d[h].referenced_size,n(i,null,{start:v,end:_},E,R)}else a("Parsing segments from SIDX."),u=e(g,E),l(u,i,o)}else{if(g)E.range.start=g.offset||E.range.start,E.range.end=E.range.start+(g.size||t);else{if(r<E.bytesLoaded)return void l(null,i,o);var I=c.getLastBox();I&&I.size?(E.range.start=I.offset+I.size,E.range.end=E.range.start+t):E.range.end+=t}n(i,o,E.range,E,l)}}},y.onloadend=y.onerror=function(){f&&(f=!1,h.downloadError("SIDX",E.url,y),l(null,i,o))},t(y,E),a("Perform SIDX load: "+E.url)},reset:function(){h=null,p=null,m=null,a=null}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../streaming/utils/RequestModifier.js")),o=n(e("./vo/Segment.js")),s=n(e("../streaming/vo/Error.js")),u=n(e("../streaming/utils/ErrorHandler.js")),l=n(e("../core/events/Events.js")),d=n(e("../core/EventBus.js")),f=n(e("../streaming/utils/BoxParser.js")),c=n(e("../core/FactoryMaker.js")),g=n(e("../core/Debug.js"));a.__dashjs_factory_name="SegmentBaseLoader",r.default=c.default.getSingletonFactory(a),t.exports=r.default},{"../core/Debug.js":7,"../core/EventBus.js":8,"../core/FactoryMaker.js":9,"../core/events/Events.js":11,"../streaming/utils/BoxParser.js":135,"../streaming/utils/ErrorHandler.js":139,"../streaming/utils/RequestModifier.js":143,"../streaming/vo/Error.js":149,"./vo/Segment.js":34}],18:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){return b}function t(){return P}function r(){var e=new Date,r=t(),n=1e3*F.getTime();k.addRepresentationSwitch(r.adaptation.type,e,n,r.id)}function n(){var e=K.calcSegmentAvailabilityRange(P,w.isDynamic());k.addDVRInfo(w.getType(),F.getTime(),w.getStreamInfo().manifestInfo,e)}function a(e){return O[e]}function y(e){for(var t,r=0,n=O.length;n>r;r++)(t=O[r]).segmentAvailabilityRange=K.calcSegmentAvailabilityRange(t,e)}function _(a){if(a.sender.getStreamProcessor()===w&&e()){var i,o,s=a.representation,u=k.getMetricsFor("stream"),l=k.getMetricsFor(t().adaptation.type),d=q.getCurrentManifestUpdate(u),f=!1,c=0;if("dynamic"==s.adaptation.period.mpd.manifest.type){var g=s.segmentAvailabilityRange.end-s.segmentAvailabilityRange.start;c=1e3*(F.computeLiveDelay(P.segmentDuration,w.getStreamInfo().manifestInfo.DVRWindowSize)-g)}if(c>0)return n(),function(t){var r=t;b=!1,C.trigger(v.default.AST_IN_FUTURE,{delay:r}),setTimeout(function(){if(!e()){b=!0,C.trigger(v.default.DATA_UPDATE_STARTED,{sender:A}),O.forEach(function(e){e.segmentAvailabilityRange=null});for(var t=0;t<O.length;t++)x.updateRepresentation(O[t],!0)}},r)}(c),o=new p.default(R,"Segments update failed",null),void C.trigger(v.default.DATA_UPDATE_COMPLETED,{sender:this,data:j,currentRepresentation:P,error:o});if(d){for(var h=0;h<d.trackInfo.length;h++)if((i=d.trackInfo[h]).index===s.index&&i.mediaType===w.getType()){f=!0;break}f||k.addManifestUpdateRepresentationInfo(d,s.id,s.index,s.adaptation.period.index,w.getType(),s.presentationTimeOffset,s.startNumber,s.segmentInfoType)}(function(){for(var e=0,t=O.length;t>e;e++){var r=O[e].segmentInfoType;if(null===O[e].segmentAvailabilityRange||null===O[e].initialization||("SegmentBase"===r||"BaseURL"===r)&&!O[e].segments)return!1}return!0})()&&(b=!1,N.setPlaybackQuality(w.getType(),w.getStreamInfo(),function(e){return O.indexOf(e)}(P)),k.updateManifestUpdateInfo(d,{latency:P.segmentAvailabilityRange.end-F.getTime()}),q.getCurrentRepresentationSwitch(l)||r(),C.trigger(v.default.DATA_UPDATE_COMPLETED,{sender:this,data:j,currentRepresentation:P}))}}function E(e){e.isDynamic&&y(e.isDynamic)}function T(e){if(!e.error){y(!0),x.updateRepresentation(P,!1);var t=B.getValue(),r=P.adaptation.period,n=L.getActiveStreamInfo();n.isLast&&(r.mpd.checkTime=H.getCheckTime(t,r),r.duration=H.getEndTimeForLastPeriod(B.getValue(),r)-r.start,n.duration=r.duration)}}function M(e){e.sender.getStreamProcessor()===w&&n()}function S(e){e.mediaType===w.getType()&&w.getStreamInfo().id===e.streamInfo.id&&e.oldQuality!==e.newQuality&&(P=a(e.newQuality),U.setSavedBitrateSettings(e.mediaType,P.bandwidth),r())}var R=1,I=this.context,C=(0,m.default)(I).getInstance(),A=void 0,j=void 0,D=void 0,b=void 0,O=void 0,P=void 0,w=void 0,N=void 0,x=void 0,L=void 0,F=void 0,B=void 0,k=void 0,U=void 0,K=void 0,H=void 0,q=void 0;return A={initialize:function(e){x=(w=e).getIndexHandler()},setConfig:function(e){e.abrController&&(N=e.abrController)},getData:function(){return j},getDataIndex:function(){return D},isUpdating:e,updateData:function(e,t,r){var n,i=null,o=w.getStreamInfo(),s=N.getTopQualityIndexFor(r,o.id);if(b=!0,C.trigger(v.default.DATA_UPDATE_STARTED,{sender:this}),O=function(e){var t=B.getValue();return D=H.getIndexForAdaptation(j,t,e.period.index),H.getRepresentationsForAdaptation(t,e)}(t),null===j&&"fragmentedText"!==r?(i=N.getAverageThroughput(r)||N.getInitialBitrateFor(r,o),n=N.getQualityForBitrate(w.getMediaInfo(),i)):n=N.getQualityFor(r,o),n>s&&(n=s),P=a(n),j=e,"video"!==r&&"audio"!==r&&"fragmentedText"!==r)return b=!1,void C.trigger(v.default.DATA_UPDATE_COMPLETED,{sender:this,data:j,currentRepresentation:P});for(var u=0;u<O.length;u++)x.updateRepresentation(O[u],!0)},getStreamProcessor:function(){return w},getCurrentRepresentation:t,getRepresentationForQuality:a,reset:function(){C.off(v.default.QUALITY_CHANGED,S,A),C.off(v.default.REPRESENTATION_UPDATED,_,A),C.off(v.default.WALLCLOCK_TIME_UPDATED,E,A),C.off(v.default.BUFFER_LEVEL_UPDATED,M,A),C.off(v.default.LIVE_EDGE_SEARCH_COMPLETED,T,A),j=null,D=-1,b=!0,O=[],N=null,L=null,F=null,B=null,k=null,U=null,K=null,H=null,q=null}},j=null,D=-1,b=!0,O=[],N=(0,u.default)(I).getInstance(),L=(0,d.default)(I).getInstance(),F=(0,l.default)(I).getInstance(),B=(0,f.default)(I).getInstance(),k=(0,c.default)(I).getInstance(),U=(0,h.default)(I).getInstance(),K=(0,s.default)(I).getInstance(),H=(0,i.default)(I).getInstance(),q=(0,o.default)(I).getInstance(),(0,g.default)(I).getInstance(),C.on(v.default.QUALITY_CHANGED,S,A),C.on(v.default.REPRESENTATION_UPDATED,_,A),C.on(v.default.WALLCLOCK_TIME_UPDATED,E,A),C.on(v.default.BUFFER_LEVEL_UPDATED,M,A),C.on(v.default.LIVE_EDGE_SEARCH_COMPLETED,T,A),A}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../models/DashManifestModel.js")),o=n(e("../DashMetrics.js")),s=n(e("../utils/TimelineConverter.js")),u=n(e("../../streaming/controllers/AbrController.js")),l=n(e("../../streaming/controllers/PlaybackController.js")),d=n(e("../../streaming/controllers/StreamController.js")),f=n(e("../../streaming/models/ManifestModel.js")),c=n(e("../../streaming/models/MetricsModel.js")),g=n(e("../../streaming/models/MediaPlayerModel.js")),h=n(e("../../streaming/utils/DOMStorage.js")),p=n(e("../../streaming/vo/Error.js")),m=n(e("../../core/EventBus.js")),v=n(e("../../core/events/Events.js")),y=n(e("../../core/FactoryMaker.js"));a.__dashjs_factory_name="RepresentationController",r.default=y.default.getClassFactory(a),t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../../streaming/controllers/AbrController.js":49,"../../streaming/controllers/PlaybackController.js":57,"../../streaming/controllers/StreamController.js":60,"../../streaming/models/ManifestModel.js":89,"../../streaming/models/MediaPlayerModel.js":90,"../../streaming/models/MetricsModel.js":91,"../../streaming/utils/DOMStorage.js":138,"../../streaming/vo/Error.js":149,"../DashMetrics.js":15,"../models/DashManifestModel.js":19,"../utils/TimelineConverter.js":25}],19:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e,t){var r,n,a,i=!1,o=!1,s=e.ContentComponent_asArray,u="text"!==t?new RegExp(t):new RegExp("(vtt|ttml)");if(e.Representation_asArray.length>0&&e.Representation_asArray[0].hasOwnProperty("codecs")){var l=e.Representation_asArray[0].codecs;if("stpp"===l||"wvtt"===l)return"fragmentedText"===t}if(s){if(s.length>1)return"muxed"==t;s[0]&&s[0].contentType===t&&(i=!0,o=!0)}if(e.hasOwnProperty("mimeType")&&(i=u.test(e.mimeType),o=!0),!o)for(r=0,n=e.Representation_asArray.length;!o&&n>r;)(a=e.Representation_asArray[r]).hasOwnProperty("mimeType")&&(i=u.test(a.mimeType),o=!0),r++;return i}function t(t){return e(t,"audio")}function r(t){return e(t,"video")}function n(t){return e(t,"fragmentedText")}function a(t){return e(t,"muxed")}function p(e){return"text/vtt"===e||"application/ttml+xml"===e}function m(e){return e.hasOwnProperty("Role_asArray")?e.Role_asArray:[]}function v(e){return m(e).filter(function(e){return"main"===e.value})[0]}function y(e){return void 0!==e.Representation_asArray&&null!==e.Representation_asArray&&e.Representation_asArray.sort(function(e,t){return e.bandwidth-t.bandwidth}),e}function _(t,r,n){var a,i,o=t.Period_asArray[r].AdaptationSet_asArray,s=[];for(a=0,i=o.length;i>a;a++)e(o[a],n)&&s.push(y(o[a]));return s}function E(e){var t=!1;return e.hasOwnProperty("type")&&(t="dynamic"===e.type),t}function T(e,t){var r=!1;return e.profiles&&e.profiles.length>0&&(r=-1!==e.profiles.indexOf(t)),r}function M(e){if(!e)throw new Error("Period cannot be null or undefined");var t=s.default.DEFAULT_ID;return e.hasOwnProperty("id")&&"__proto__"!==e.id&&(t=e.id),t}function S(e,t){return j.calcPresentationTimeFromWallTime(e.loadedTime,t)}function R(e,t){var r=NaN;return e.hasOwnProperty("minimumUpdatePeriod")&&(r=S(e,t)+e.minimumUpdatePeriod),r}function I(e,t){var r,n=R(e,t);if(e.mediaPresentationDuration)r=e.mediaPresentationDuration;else{if(isNaN(n))throw new Error("Must have @mediaPresentationDuration or @minimumUpdatePeriod on MPD or an explicit @duration on the last period.");r=n}return r}function C(e,t){var r=[];if(!e)return r;for(var n=0;n<e.length;n++){var a=new g.default;if(a.timescale=1,a.representation=t,!e[n].hasOwnProperty("schemeIdUri"))throw"Invalid EventStream. SchemeIdUri has to be set";a.schemeIdUri=e[n].schemeIdUri,e[n].hasOwnProperty("timescale")&&(a.timescale=e[n].timescale),e[n].hasOwnProperty("value")&&(a.value=e[n].value),r.push(a)}return r}var A=this.context,j=(0,d.default)(A).getInstance(),D=(0,h.default)(A).getInstance();return{getIsTypeOf:e,getIsAudio:t,getIsVideo:r,getIsText:function(t){return e(t,"text")},getIsMuxed:a,getIsTextTrack:p,getIsFragmentedText:n,getIsMain:v,getLanguageForAdaptation:function(e){var t="";return e.hasOwnProperty("lang")&&(t=e.lang.replace(/[^A-Za-z0-9-]/g,"")),t},getViewpointForAdaptation:function(e){return e.hasOwnProperty("Viewpoint")?e.Viewpoint:null},getRolesForAdaptation:m,getAccessibilityForAdaptation:function(e){return e.hasOwnProperty("Accessibility_asArray")?e.Accessibility_asArray:[]},getAudioChannelConfigurationForAdaptation:function(e){return e.hasOwnProperty("AudioChannelConfiguration_asArray")?e.AudioChannelConfiguration_asArray:[]},processAdaptation:y,getAdaptationForIndex:function(e,t,r){return t.Period_asArray[r].AdaptationSet_asArray[e]},getIndexForAdaptation:function(e,t,r){var n,a,i=t.Period_asArray[r].AdaptationSet_asArray;for(n=0,a=i.length;a>n;n++)if(i[n]===e)return n;return-1},getAdaptationForId:function(e,t,r){var n,a,i=t.Period_asArray[r].AdaptationSet_asArray;for(n=0,a=i.length;a>n;n++)if(i[n].hasOwnProperty("id")&&i[n].id===e)return i[n];return null},getAdaptationsForType:_,getAdaptationForType:function(e,t,r){var n,a,i;if(!(i=_(e,t,r))||0===i.length)return null;for(n=0,a=i.length;a>n;n++)if(v(i[n]))return i[n];return i[0]},getCodec:function(e){var t=e.Representation_asArray[0];return t.mimeType+';codecs="'+t.codecs+'"'},getMimeType:function(e){return e.Representation_asArray[0].mimeType},getKID:function(e){return e&&e.hasOwnProperty("cenc:default_KID")?e["cenc:default_KID"]:null},getContentProtectionData:function(e){return e&&e.hasOwnProperty("ContentProtection_asArray")&&0!==e.ContentProtection_asArray.length?e.ContentProtection_asArray:null},getIsDynamic:E,getIsDVR:function(e){var t,r=E(e);return t=!isNaN(e.timeShiftBufferDepth),r&&t},getIsOnDemand:function(e){return T(e,"urn:mpeg:dash:profile:isoff-on-demand:2011")},getIsDVB:function(e){return T(e,"urn:dvb:dash:profile:dvb-dash:2014")},getDuration:function(e){return e.hasOwnProperty("mediaPresentationDuration")?e.mediaPresentationDuration:Number.MAX_VALUE},getBandwidth:function(e){return e.bandwidth},getRefreshDelay:function(e){var t=NaN;return e.hasOwnProperty("minimumUpdatePeriod")&&(t=Math.max(parseFloat(e.minimumUpdatePeriod),2)),t},getRepresentationCount:function(e){return e.Representation_asArray.length},getBitrateListForAdaptation:function(e){if(!e||!e.Representation_asArray||!e.Representation_asArray.length)return null;for(var t=y(e).Representation_asArray,r=t.length,n=[],a=0;r>a;a++)n.push({bandwidth:t[a].bandwidth,width:t[a].width||0,height:t[a].height||0});return n},getRepresentationFor:function(e,t){return t.Representation_asArray[e]},getRepresentationsForAdaptation:function(e,t){for(var r,n,a,o,s,u=y(e.Period_asArray[t.period.index].AdaptationSet_asArray[t.index]),l=[],d=0;d<u.Representation_asArray.length;d++)o=u.Representation_asArray[d],(r=new i.default).index=d,r.adaptation=t,o.hasOwnProperty("id")&&(r.id=o.id),o.hasOwnProperty("bandwidth")&&(r.bandwidth=o.bandwidth),o.hasOwnProperty("maxPlayoutRate")&&(r.maxPlayoutRate=o.maxPlayoutRate),o.hasOwnProperty("SegmentBase")?(a=o.SegmentBase,r.segmentInfoType="SegmentBase"):o.hasOwnProperty("SegmentList")?(a=o.SegmentList,r.segmentInfoType="SegmentList",r.useCalculatedLiveEdgeTime=!0):o.hasOwnProperty("SegmentTemplate")?((a=o.SegmentTemplate).hasOwnProperty("SegmentTimeline")?(r.segmentInfoType="SegmentTimeline",(!(s=a.SegmentTimeline.S_asArray[a.SegmentTimeline.S_asArray.length-1]).hasOwnProperty("r")||s.r>=0)&&(r.useCalculatedLiveEdgeTime=!0)):r.segmentInfoType="SegmentTemplate",a.hasOwnProperty("initialization")&&(r.initialization=a.initialization.split("$Bandwidth$").join(o.bandwidth).split("$RepresentationID$").join(o.id))):(a=o.BaseURL,r.segmentInfoType="BaseURL"),a.hasOwnProperty("Initialization")?(n=a.Initialization).hasOwnProperty("sourceURL")?r.initialization=n.sourceURL:n.hasOwnProperty("range")&&(r.range=n.range):o.hasOwnProperty("mimeType")&&p(o.mimeType)&&(r.range=0),a.hasOwnProperty("timescale")&&(r.timescale=a.timescale),a.hasOwnProperty("duration")&&(r.segmentDuration=a.duration/r.timescale),a.hasOwnProperty("startNumber")&&(r.startNumber=a.startNumber),a.hasOwnProperty("indexRange")&&(r.indexRange=a.indexRange),a.hasOwnProperty("presentationTimeOffset")&&(r.presentationTimeOffset=a.presentationTimeOffset/r.timescale),r.MSETimeOffset=j.calcMSETimeOffset(r),r.path=[t.period.index,t.index,d],l.push(r);return l},getAdaptationsForPeriod:function(e,i){for(var s,u,l=e.Period_asArray[i.index],d=[],f=0;f<l.AdaptationSet_asArray.length;f++)u=l.AdaptationSet_asArray[f],s=new o.default,u.hasOwnProperty("id")&&(s.id=u.id),s.index=f,s.period=i,a(u)?s.type="muxed":t(u)?s.type="audio":r(u)?s.type="video":n(u)?s.type="fragmentedText":s.type="text",d.push(s);return d},getRegularPeriods:function(e,t){var r,n,a=E(e),i=[],o=null,u=null,l=null,d=null;for(n=0,r=e.Period_asArray.length;r>n;n++)(u=e.Period_asArray[n]).hasOwnProperty("start")?(d=new s.default).start=u.start:null!==o&&u.hasOwnProperty("duration")&&null!==l?((d=new s.default).start=l.start+l.duration,d.duration=u.duration):0!==n||a||((d=new s.default).start=0),null!==l&&isNaN(l.duration)&&(l.duration=d.start-l.start),null!==d&&(d.id=M(u)),null!==d&&u.hasOwnProperty("duration")&&(d.duration=u.duration),null!==d&&(d.index=n,d.mpd=t,i.push(d),o=u,l=d),u=null,d=null;return 0===i.length||null!==l&&isNaN(l.duration)&&(l.duration=I(e,l)-l.start),i},getPeriodId:M,getMpd:function(e){var t=new u.default;return t.manifest=e,e.hasOwnProperty("availabilityStartTime")?t.availabilityStartTime=new Date(e.availabilityStartTime.getTime()):t.availabilityStartTime=new Date(e.loadedTime.getTime()),e.hasOwnProperty("availabilityEndTime")&&(t.availabilityEndTime=new Date(e.availabilityEndTime.getTime())),e.hasOwnProperty("suggestedPresentationDelay")&&(t.suggestedPresentationDelay=e.suggestedPresentationDelay),e.hasOwnProperty("timeShiftBufferDepth")&&(t.timeShiftBufferDepth=e.timeShiftBufferDepth),e.hasOwnProperty("maxSegmentDuration")&&(t.maxSegmentDuration=e.maxSegmentDuration),t},getFetchTime:S,getCheckTime:R,getEndTimeForLastPeriod:I,getEventsForPeriod:function(e,t){var r=e.Period_asArray[t.index].EventStream_asArray,n=[];if(r)for(var a=0;a<r.length;a++){var i=new g.default;if(i.period=t,i.timescale=1,!r[a].hasOwnProperty("schemeIdUri"))throw"Invalid EventStream. SchemeIdUri has to be set";i.schemeIdUri=r[a].schemeIdUri,r[a].hasOwnProperty("timescale")&&(i.timescale=r[a].timescale),r[a].hasOwnProperty("value")&&(i.value=r[a].value);for(var o=0;o<r[a].Event_asArray.length;o++){var s=new f.default;s.presentationTime=0,s.eventStream=i,r[a].Event_asArray[o].hasOwnProperty("presentationTime")&&(s.presentationTime=r[a].Event_asArray[o].presentationTime),r[a].Event_asArray[o].hasOwnProperty("duration")&&(s.duration=r[a].Event_asArray[o].duration),r[a].Event_asArray[o].hasOwnProperty("id")&&(s.id=r[a].Event_asArray[o].id),n.push(s)}}return n},getEventStreams:C,getEventStreamForAdaptationSet:function(e,t){return C(e.Period_asArray[t.period.index].AdaptationSet_asArray[t.index].InbandEventStream_asArray,null)},getEventStreamForRepresentation:function(e,t){return C(e.Period_asArray[t.adaptation.period.index].AdaptationSet_asArray[t.adaptation.index].Representation_asArray[t.index].InbandEventStream_asArray,t)},getUTCTimingSources:function(e){var t=E(e),r=e.hasOwnProperty("availabilityStartTime"),n=e.UTCTiming_asArray,a=[];return(t||r)&&n&&n.forEach(function(e){var t=new l.default;e.hasOwnProperty("schemeIdUri")&&(t.schemeIdUri=e.schemeIdUri,e.hasOwnProperty("value")&&(t.value=e.value.toString(),a.push(t)))}),a},getBaseURLsFromElement:function(e){var t=[],r=e.BaseURL_asArray||[e.baseUri]||[],n=!1;return r.some(function(r){if(r){var a=new c.default,i=r.__text||r;return D.isRelative(i)&&(n=!0,e.baseUri&&(i=e.baseUri+i)),a.url=i,r.hasOwnProperty("serviceLocation")&&r.serviceLocation.length?a.serviceLocation=r.serviceLocation:a.serviceLocation=i,r.hasOwnProperty("dvb:priority")&&(a.dvb_priority=r["dvb:priority"]),r.hasOwnProperty("dvb:weight")&&(a.dvb_weight=r["dvb:weight"]),t.push(a),n}}),t},getRepresentationSortFunction:function(){return function(e,t){return e.bandwidth-t.bandwidth}}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../vo/Representation.js")),o=n(e("../vo/AdaptationSet.js")),s=n(e("../vo/Period.js")),u=n(e("../vo/Mpd.js")),l=n(e("../vo/UTCTiming.js")),d=n(e("../utils/TimelineConverter.js")),f=n(e("../vo/Event.js")),c=n(e("../vo/BaseURL.js")),g=n(e("../vo/EventStream.js")),h=n(e("../../streaming/utils/URLUtils.js")),p=n(e("../../core/FactoryMaker.js"));a.__dashjs_factory_name="DashManifestModel",r.default=p.default.getSingletonFactory(a),t.exports=r.default},{"../../core/FactoryMaker.js":9,"../../streaming/utils/URLUtils.js":145,"../utils/TimelineConverter.js":25,"../vo/AdaptationSet.js":27,"../vo/BaseURL.js":28,"../vo/Event.js":29,"../vo/EventStream.js":30,"../vo/Mpd.js":31,"../vo/Period.js":32,"../vo/Representation.js":33,"../vo/UTCTiming.js":35}],20:[function(e,t,r){"use strict";function n(){var e=void 0;return{getSamplesInfo:function(t){var r,n,a,i,o,s,u,l,d,f,c=e.parse(t),g=c.getBox("tfhd"),h=c.getBox("tfdt"),p=c.getBox("trun"),m=c.getBox("moof");for(f=c.getBox("mfhd").sequence_number,a=p.sample_count,o=h.baseMediaDecodeTime,d=(g.base_data_offset||0)+(p.data_offset||0),s=[],l=0;a>l;l++)r=void 0!==(u=p.samples[l]).sample_duration?u.sample_duration:g.default_sample_duration,i=void 0!==u.sample_size?u.sample_size:g.default_sample_size,n=void 0!==u.sample_composition_time_offset?u.sample_composition_time_offset:0,s.push({dts:o,cts:o+n,duration:r,offset:m.offset+d,size:i}),d+=i,o+=r;return{sampleList:s,sequenceNumber:f,totalDuration:o-h.baseMediaDecodeTime}},getMediaTimescaleFromMoov:function(t){var r=e.parse(t).getBox("mdhd");return r?r.timescale:NaN},setConfig:function(t){t&&t.boxParser&&(e=t.boxParser)}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js"));n.__dashjs_factory_name="FragmentedTextBoxParser",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../core/FactoryMaker.js":9}],21:[function(e,t,r){"use strict";function n(e,t){var r=e.timelineConverter;return{getSegments:function(e,n,a,o){var s,u,l,d,f,c,g,h=e.adaptation.period.mpd.manifest.Period_asArray[e.adaptation.period.index].AdaptationSet_asArray[e.adaptation.index].Representation_asArray[e.index].SegmentList,p=e.adaptation.period.mpd.manifest.Period_asArray[e.adaptation.period.index].AdaptationSet_asArray[e.adaptation.index].Representation_asArray[e.index].BaseURL,m=h.SegmentURL_asArray.length,v=[];for(g=e.startNumber,d=(0,i.decideSegmentListRangeForTemplate)(r,t,e,n,a,o),f=Math.max(d.start,0),c=Math.min(d.end,h.SegmentURL_asArray.length-1),s=f;c>=s;s++)l=h.SegmentURL_asArray[s],(u=(0,i.getIndexBasedSegment)(r,t,e,s)).replacementTime=(g+s-1)*e.segmentDuration,u.media=l.media?l.media:p,u.mediaRange=l.mediaRange,u.index=l.index,u.indexRange=l.indexRange,v.push(u),u=null;return e.availableSegmentsNumber=m,v}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js")),i=e("./SegmentsUtils.js");n.__dashjs_factory_name="ListSegmentsGetter";var o=a.default.getClassFactory(n);r.default=o,t.exports=r.default},{"../../core/FactoryMaker.js":9,"./SegmentsUtils.js":23}],22:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e,t){var r,n=this.context,a=void 0,i=void 0,l=void 0;return r={getSegments:function(e,t,r,n,o){var s,u=e.segmentInfoType;return"SegmentBase"!==u&&"BaseURL"!==u&&function(e,t){var r,n,a=e.segments,i=!1;return a&&0!==a.length?(n=a[0].availabilityIdx,r=a[a.length-1].availabilityIdx,i=n>t||t>r):i=!0,i}(e,r)?("SegmentTimeline"===u?s=a.getSegments(e,t,r,o):"SegmentTemplate"===u?s=i.getSegments(e,t,r,o):"SegmentList"===u&&(s=l.getSegments(e,t,r,o)),n&&n(e,s)):s=e.segments,s}},a=(0,o.default)(n).create(e,t),i=(0,s.default)(n).create(e,t),l=(0,u.default)(n).create(e,t),r}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/FactoryMaker.js")),o=n(e("./TimelineSegmentsGetter.js")),s=n(e("./TemplateSegmentsGetter.js")),u=n(e("./ListSegmentsGetter.js"));a.__dashjs_factory_name="SegmentsGetter";var l=i.default.getClassFactory(a);r.default=l,t.exports=r.default},{"../../core/FactoryMaker.js":9,"./ListSegmentsGetter.js":21,"./TemplateSegmentsGetter.js":24,"./TimelineSegmentsGetter.js":26}],23:[function(e,t,r){"use strict";function n(e,t){for(;e.length<t;)e="0"+e;return e}function a(e,t){return e.representation.startNumber+t}function i(e,t,r){for(var a,i,o,s,u,l,d=t.length;;){if(0>(a=e.indexOf("$"+t)))return e;if(0>(i=e.indexOf("$",a+d)))return e;if((o=e.indexOf("%0",a+d))>a&&i>o)switch(s=e.charAt(i-1),u=parseInt(e.substring(o+2,i-1),10),s){case"d":case"i":case"u":l=n(r.toString(),u);break;case"x":l=n(r.toString(16),u);break;case"X":l=n(r.toString(16),u).toUpperCase();break;case"o":l=n(r.toString(8),u);break;default:return e}else l=r;e=e.substring(0,a)+l+e.substring(i+1)}}function o(e,t){if(!t||!t.segments)return null;var r,n,a=t.segments.length;if(a>e&&(r=t.segments[e])&&r.availabilityIdx===e)return r;for(n=0;a>n;n++)if((r=t.segments[n])&&r.availabilityIdx===e)return r;return null}Object.defineProperty(r,"__esModule",{value:!0}),r.replaceTokenForTemplate=i,r.getIndexBasedSegment=function(e,t,r,n){var i,o,u,l;return o=r.segmentDuration,isNaN(o)&&(o=r.adaptation.period.duration),l=(u=r.adaptation.period.start+n*o)+o,(i=new s.default).representation=r,i.duration=o,i.presentationStartTime=u,i.mediaStartTime=e.calcMediaTimeFromPresentationTime(i.presentationStartTime,r),i.availabilityStartTime=e.calcAvailabilityStartTimeFromPresentationTime(i.presentationStartTime,r.adaptation.period.mpd,t),i.availabilityEndTime=e.calcAvailabilityEndTimeFromPresentationTime(l,r.adaptation.period.mpd,t),i.wallStartTime=e.calcWallTimeForSegment(i,t),i.replacementNumber=a(i,n),i.availabilityIdx=n,i},r.getTimeBasedSegment=function(e,t,r,n,o,u,l,d,f){var c,g,h,p=n/u,m=Math.min(o/u,r.adaptation.period.mpd.maxSegmentDuration);return g=(c=e.calcPresentationTimeFromMediaTime(p,r))+m,(h=new s.default).representation=r,h.duration=m,h.mediaStartTime=p,h.presentationStartTime=c,h.availabilityStartTime=r.adaptation.period.mpd.manifest.loadedTime,h.availabilityEndTime=e.calcAvailabilityEndTimeFromPresentationTime(g,r.adaptation.period.mpd,t),h.wallStartTime=e.calcWallTimeForSegment(h,t),h.replacementTime=n,h.replacementNumber=a(h,f),l=i(l,"Number",h.replacementNumber),l=i(l,"Time",h.replacementTime),h.media=l,h.mediaRange=d,h.availabilityIdx=f,h},r.getSegmentByIndex=o,r.decideSegmentListRangeForTimeline=function(e,t,r,n,a){var i=a||10,o=Number.POSITIVE_INFINITY;return t&&!e.isTimeSyncCompleted()?{start:0,end:o}:!t&&r||0>n?null:{start:Math.max(n-2,0),end:Math.min(n+i,o)}},r.decideSegmentListRangeForTemplate=function(e,t,r,n,a,i){var s,u=r.segmentDuration,l=r.adaptation.period.mpd.manifest.minBufferTime,d=r.segmentAvailabilityRange,f={start:e.calcPeriodRelativeTimeFromMpdRelativeTime(r,d.start),end:e.calcPeriodRelativeTimeFromMpdRelativeTime(r,d.end)},c=r.segments,g=2*u,h=i||Math.max(2*l,10*u),p=NaN,m=null;return f.start=Math.max(f.start,0),t&&!e.isTimeSyncCompleted()?{start:s=Math.floor(f.start/u),end:Math.floor(f.end/u)}:(p=c&&c.length>0?(m=o(a,r))?e.calcPeriodRelativeTimeFromMpdRelativeTime(r,m.presentationStartTime):a>0?a*u:e.calcPeriodRelativeTimeFromMpdRelativeTime(r,n):a>0?a*u:t?f.end:f.start,{start:s=Math.floor(Math.max(p-g,f.start)/u),end:Math.floor(Math.min(s+h/u,f.end/u))})};var s=function(e){return e&&e.__esModule?e:{default:e}}(e("./../vo/Segment.js"))},{"./../vo/Segment.js":34}],24:[function(e,t,r){"use strict";function n(e,t){var r=e.timelineConverter;return{getSegments:function(e,n,a,o){var s,u,l,d,f,c=e.adaptation.period.mpd.manifest.Period_asArray[e.adaptation.period.index].AdaptationSet_asArray[e.adaptation.index].Representation_asArray[e.index].SegmentTemplate,g=e.segmentDuration,h=e.segmentAvailabilityRange,p=[],m=null,v=null;for(f=e.startNumber,l=(s=isNaN(g)&&!t?{start:f,end:f}:(0,i.decideSegmentListRangeForTemplate)(r,t,e,n,a,o)).start,d=s.end,u=l;d>=u;u++)(v=(0,i.getIndexBasedSegment)(r,t,e,u)).replacementTime=(f+u-1)*e.segmentDuration,m=c.media,m=(0,i.replaceTokenForTemplate)(m,"Number",v.replacementNumber),m=(0,i.replaceTokenForTemplate)(m,"Time",v.replacementTime),v.media=m,p.push(v),v=null;return isNaN(g)?e.availableSegmentsNumber=1:e.availableSegmentsNumber=Math.ceil((h.end-h.start)/g),p}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js")),i=e("./SegmentsUtils.js");n.__dashjs_factory_name="TemplateSegmentsGetter";var o=a.default.getClassFactory(n);r.default=o,t.exports=r.default},{"../../core/FactoryMaker.js":9,"./SegmentsUtils.js":23}],25:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e,t,r,n){return n?r&&t.timeShiftBufferDepth!=Number.POSITIVE_INFINITY?new Date(t.availabilityStartTime.getTime()+1e3*(e+t.timeShiftBufferDepth)):t.availabilityEndTime:r?new Date(t.availabilityStartTime.getTime()+1e3*(e-u)):t.availabilityStartTime}function t(e,t){return(e.getTime()-t.mpd.availabilityStartTime.getTime()+1e3*u)/1e3}function r(e){l||e.error||(u+=e.liveEdge-(d+e.searchTime),l=!0)}function n(e){l||e.error||(u=e.offset/1e3,l=!0)}var a=this.context,s=(0,i.default)(a).getInstance(),u=void 0,l=void 0,d=void 0;return{initialize:function(){u=0,l=!1,d=NaN,s.on(o.default.LIVE_EDGE_SEARCH_COMPLETED,r,this),s.on(o.default.TIME_SYNCHRONIZATION_COMPLETED,n,this)},isTimeSyncCompleted:function(){return l},setTimeSyncCompleted:function(e){l=e},getClientTimeOffset:function(){return u},getExpectedLiveEdge:function(){return d},setExpectedLiveEdge:function(e){d=e},calcAvailabilityStartTimeFromPresentationTime:function(t,r,n){return e.call(this,t,r,n)},calcAvailabilityEndTimeFromPresentationTime:function(t,r,n){return e.call(this,t,r,n,!0)},calcPresentationTimeFromWallTime:t,calcPresentationTimeFromMediaTime:function(e,t){return e+(t.adaptation.period.start-t.presentationTimeOffset)},calcPeriodRelativeTimeFromMpdRelativeTime:function(e,t){return t-e.adaptation.period.start},calcMpdRelativeTimeFromPeriodRelativeTime:function(e,t){return t+e.adaptation.period.start},calcMediaTimeFromPresentationTime:function(e,t){return e-t.adaptation.period.start+t.presentationTimeOffset},calcSegmentAvailabilityRange:function(e,r){var n,a,i=e.adaptation.period.start,o=i+e.adaptation.period.duration,s={start:i,end:o},u=e.segmentDuration||(e.segments&&e.segments.length?e.segments[e.segments.length-1].duration:0);if(!r)return s;if(!l&&e.segmentAvailabilityRange)return e.segmentAvailabilityRange;n=e.adaptation.period.mpd.checkTime,a=t(new Date,e.adaptation.period),i=Math.max(a-e.adaptation.period.mpd.timeShiftBufferDepth,e.adaptation.period.start);var d=isNaN(n)?a:Math.min(n,a),f=e.adaptation.period.start+e.adaptation.period.duration;return{start:i,end:o=(d>=f&&f>d-u?f:d)-u}},calcWallTimeForSegment:function(e,t){var r,n,a;return t&&(r=e.representation.adaptation.period.mpd.suggestedPresentationDelay,n=e.presentationStartTime+r,a=new Date(e.availabilityStartTime.getTime()+1e3*n)),a},calcMSETimeOffset:function(e){var t=e.presentationTimeOffset;return e.adaptation.period.start-t},reset:function(){s.off(o.default.LIVE_EDGE_SEARCH_COMPLETED,r,this),s.off(o.default.TIME_SYNCHRONIZATION_COMPLETED,n,this),u=0,l=!1,d=NaN}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/EventBus.js")),o=n(e("../../core/events/Events.js")),s=n(e("../../core/FactoryMaker.js"));a.__dashjs_factory_name="TimelineConverter",r.default=s.default.getSingletonFactory(a),t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11}],26:[function(e,t,r){"use strict";function n(e,t){var r=e.timelineConverter;return{getSegments:function(e,n,a,o){var s,u,l,d,f,c,g,h,p,m,v,y,_,E,T=e.adaptation.period.mpd.manifest.Period_asArray[e.adaptation.period.index].AdaptationSet_asArray[e.adaptation.index].Representation_asArray[e.index].SegmentTemplate,M=T.SegmentTimeline,S=e.availableSegmentsNumber>0,R=0,I=0,C=-1,A=[],j=!1,D=function(n){return(0,i.getTimeBasedSegment)(r,t,e,R,n.d,E,T.media,n.mediaRange,C)};for(E=e.timescale,s=M.S_asArray,(p=(0,i.decideSegmentListRangeForTimeline)(r,t,n,a,o))?(y=p.start,_=p.end):v=r.calcMediaTimeFromPresentationTime(n||0,e),l=0,d=s.length;d>l;l++){if(c=0,(u=s[l]).hasOwnProperty("r")&&(c=u.r),u.hasOwnProperty("t")&&(I=(R=u.t)/E),0>c){if((h=s[l+1])&&h.hasOwnProperty("t"))g=h.t/E;else{var b=e.segmentAvailabilityRange?e.segmentAvailabilityRange.end:r.calcSegmentAvailabilityRange(e,t).end;g=r.calcMediaTimeFromPresentationTime(b,e),e.segmentDuration=u.d/E}c=Math.ceil((g-I)/(u.d/E))-1}if(m){if(S)break;C+=c+1}else for(f=0;c>=f;f++){if(C++,p){if(C>_){if(m=!0,S)break;continue}C>=y&&A.push(D(u))}else{if(A.length>10){if(m=!0,S)break;continue}j?A.push(D(u)):I>=v-u.d/E*1.5&&(j=!0,A.push(D(u)))}I=(R+=u.d)/E}}return S||(e.availableSegmentsNumber=C+1),A}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js")),i=e("./SegmentsUtils.js");n.__dashjs_factory_name="TimelineSegmentsGetter";var o=a.default.getClassFactory(n);r.default=o,t.exports=r.default},{"../../core/FactoryMaker.js":9,"./SegmentsUtils.js":23}],27:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.period=null,this.index=-1,this.type=null},t.exports=r.default},{}],28:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function e(t,r,n,a){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.url=t||"",this.serviceLocation=r||t||"",this.dvb_priority=n||1,this.dvb_weight=a||1};n.DEFAULT_DVB_PRIORITY=1,n.DEFAULT_DVB_WEIGHT=1,r.default=n,t.exports=r.default},{}],29:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.duration=NaN,this.presentationTime=NaN,this.id=NaN,this.messageData="",this.eventStream=null,this.presentationTimeDelta=NaN},t.exports=r.default},{}],30:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.adaptionSet=null,this.representation=null,this.period=null,this.timescale=1,this.value="",this.schemeIdUri=""},t.exports=r.default},{}],31:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.manifest=null,this.suggestedPresentationDelay=0,this.availabilityStartTime=null,this.availabilityEndTime=Number.POSITIVE_INFINITY,this.timeShiftBufferDepth=Number.POSITIVE_INFINITY,this.maxSegmentDuration=Number.POSITIVE_INFINITY,this.checkTime=NaN,this.clientServerTimeShift=0,this.isClientServerTimeSyncCompleted=!1},t.exports=r.default},{}],32:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.id=null,this.index=-1,this.duration=NaN,this.start=NaN,this.mpd=null};n.DEFAULT_ID="defaultId",r.default=n,t.exports=r.default},{}],33:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.id=null,this.index=-1,this.adaptation=null,this.segmentInfoType=null,this.initialization=null,this.segmentDuration=NaN,this.timescale=1,this.startNumber=1,this.indexRange=null,this.range=null,this.presentationTimeOffset=0,this.MSETimeOffset=NaN,this.segmentAvailabilityRange=null,this.availableSegmentsNumber=0,this.bandwidth=NaN,this.maxPlayoutRate=NaN},t.exports=r.default},{}],34:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.indexRange=null,this.index=null,this.mediaRange=null,this.media=null,this.duration=NaN,this.replacementTime=null,this.replacementNumber=NaN,this.mediaStartTime=NaN,this.presentationStartTime=NaN,this.availabilityStartTime=NaN,this.availabilityEndTime=NaN,this.availabilityIdx=NaN,this.wallStartTime=NaN,this.representation=null},t.exports=r.default},{}],35:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.schemeIdUri="",this.value=""},t.exports=r.default},{}],36:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t=this.context,r=(0,u.default)(t).getInstance(),n=void 0,a=void 0;return n={checkForExistence:function(e){var t=function(t){r.trigger(l.default.CHECK_FOR_EXISTENCE_COMPLETED,{request:e,exists:t})};if(e){var n=new o.default(e.url);a.load({request:n,success:function(){t(!0)},error:function(){t(!1)}})}else t(!1)},load:function(e){var t=function(t,a){r.trigger(l.default.LOADING_COMPLETED,{request:e,response:t||null,error:a||null,sender:n})};e?a.load({request:e,progress:function(){r.trigger(l.default.LOADING_PROGRESS,{request:e})},success:function(e){t(e)},error:function(e,r,n){t(void 0,new s.default(f,n,r))}}):t(void 0,new s.default(c,g))},abort:function(){a&&a.abort()},reset:function(){a&&(a.abort(),a=null)}},a=(0,i.default)(t).create({errHandler:e.errHandler,metricsModel:e.metricsModel,requestModifier:e.requestModifier}),n}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./XHRLoader.js")),o=n(e("./vo/HeadRequest.js")),s=n(e("./vo/Error.js")),u=n(e("./../core/EventBus.js")),l=n(e("./../core/events/Events.js")),d=n(e("../core/FactoryMaker.js")),f=1,c=2,g="request is null";a.__dashjs_factory_name="FragmentLoader";var h=d.default.getClassFactory(a);h.FRAGMENT_LOADER_ERROR_LOADING_FAILURE=f,h.FRAGMENT_LOADER_ERROR_NULL_REQUEST=c,r.default=h,t.exports=r.default},{"../core/FactoryMaker.js":9,"./../core/EventBus.js":8,"./../core/events/Events.js":11,"./XHRLoader.js":47,"./vo/Error.js":149,"./vo/HeadRequest.js":151}],37:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e){n.trigger(c.default.INTERNAL_MANIFEST_LOADED,{manifest:e.manifest})}var r=this.context,n=(0,f.default)(r).getInstance(),a=(0,s.default)(r).getInstance(),g=e.parser,v=void 0,y=void 0,_=void 0;return v={load:function(e){var t=new u.default(e,d.default.MPD_TYPE);y.load({request:t,success:function(t,r,i){var o,s;i.responseURL&&i.responseURL!==e?(s=a.parseBaseUrl(i.responseURL),o=i.responseURL):(a.isRelative(e)&&(e=a.parseBaseUrl(window.location.href)+e),s=a.parseBaseUrl(e));var u=g.parse(t,_);u?(u.url=o||e,u.originalUrl||(u.originalUrl=u.url),u.baseUri=s,u.loadedTime=new Date,_.resolveManifestOnLoad(u)):n.trigger(c.default.INTERNAL_MANIFEST_LOADED,{manifest:null,error:new l.default(h,m)})},error:function(t,r,a){n.trigger(c.default.INTERNAL_MANIFEST_LOADED,{manifest:null,error:new l.default(p,"Failed loading manifest: "+e+", "+a)})}})},reset:function(){n.off(c.default.XLINK_READY,t,v),_&&(_.reset(),_=null),y&&(y.abort(),y=null)}},n.on(c.default.XLINK_READY,t,v),y=(0,o.default)(r).create({errHandler:e.errHandler,metricsModel:e.metricsModel,requestModifier:e.requestModifier}),_=(0,i.default)(r).create({errHandler:e.errHandler,metricsModel:e.metricsModel,requestModifier:e.requestModifier}),v}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./controllers/XlinkController.js")),o=n(e("./XHRLoader.js")),s=n(e("./utils/URLUtils.js")),u=n(e("./vo/TextRequest.js")),l=n(e("./vo/Error.js")),d=n(e("./vo/metrics/HTTPRequest.js")),f=n(e("../core/EventBus.js")),c=n(e("../core/events/Events.js")),g=n(e("../core/FactoryMaker.js")),h=1,p=2,m="parsing failed";a.__dashjs_factory_name="ManifestLoader";var v=g.default.getClassFactory(a);v.MANIFEST_LOADER_ERROR_PARSING_FAILURE=h,v.MANIFEST_LOADER_ERROR_LOADING_FAILURE=p,r.default=v,t.exports=r.default},{"../core/EventBus.js":8,"../core/FactoryMaker.js":9,"../core/events/Events.js":11,"./XHRLoader.js":47,"./controllers/XlinkController.js":63,"./utils/URLUtils.js":145,"./vo/Error.js":149,"./vo/TextRequest.js":157,"./vo/metrics/HTTPRequest.js":166}],38:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){null!==p&&(clearInterval(p),p=null)}function t(){e(),isNaN(h)||(c("Refresh manifest in "+h+" seconds."),p=setTimeout(n,Math.min(1e3*h,Math.pow(2,31)-1),this))}function r(e){var r,n,a=new Date;_.setValue(e),c("Manifest has been refreshed at "+a+"["+a.getTime()/1e3+"] "),r=E.getRefreshDelay(e),n=((new Date).getTime()-e.loadedTime.getTime())/1e3,h=Math.max(r-n,0),g.trigger(o.default.MANIFEST_UPDATED,{manifest:e}),m||t()}function n(){var e,t;m||v||(v=!0,t=(e=_.getValue()).url,e.hasOwnProperty("Location")&&(t=e.Location),y.load(t))}function a(e){e.error||r(e.manifest)}function s(){m=!1,t()}function l(){m=!0,e()}function d(){v=!1}var f=this.context,c=(0,u.default)(f).getInstance().log,g=(0,i.default)(f).getInstance(),h=void 0,p=void 0,m=void 0,v=void 0,y=void 0,_=void 0,E=void 0;return{initialize:function(e){y=e,h=NaN,p=null,v=!1,m=!0,g.on(o.default.STREAMS_COMPOSED,d,this),g.on(o.default.PLAYBACK_STARTED,s,this),g.on(o.default.PLAYBACK_PAUSED,l,this),g.on(o.default.INTERNAL_MANIFEST_LOADED,a,this)},setManifest:function(e){r(e)},getManifestLoader:function(){return y},setConfig:function(e){e&&(e.manifestModel&&(_=e.manifestModel),e.dashManifestModel&&(E=e.dashManifestModel))},reset:function(){g.off(o.default.PLAYBACK_STARTED,s,this),g.off(o.default.PLAYBACK_PAUSED,l,this),g.off(o.default.STREAMS_COMPOSED,d,this),g.off(o.default.INTERNAL_MANIFEST_LOADED,a,this),m=!0,v=!1,e(),h=NaN}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../core/EventBus.js")),o=n(e("../core/events/Events.js")),s=n(e("../core/FactoryMaker.js")),u=n(e("../core/Debug.js"));a.__dashjs_factory_name="ManifestUpdater",r.default=s.default.getSingletonFactory(a),t.exports=r.default},{"../core/Debug.js":7,"../core/EventBus.js":8,"../core/FactoryMaker.js":9,"../core/events/Events.js":11}],39:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){return!!Oe&&!!ce}function t(){if(!pe)throw ie;return je.isPaused()}function r(e){var t=$();if(!t)return 0;var r=t.range.start+e;return r>t.range.end&&(r=t.range.end),r}function n(e){if(!pe)throw ie;var t=z().currentTime;if(void 0!==e&&(t=Ce.getTimeRelativeToStreamId(t,e)),je.getIsDynamic()){var r=$();t=null===r?0:a()-(r.range.end-r.time)}return t}function a(){if(!pe)throw ie;var e=z().duration;if(je.getIsDynamic()){var t,r=$();if(!r)return 0;e=(t=r.range.end-r.range.start)<r.manifestInfo.DVRWindowSize?t:r.manifestInfo.DVRWindowSize}return e}function U(e){me=e}function K(){return De}function H(e){return Me.getReadOnlyMetricsFor(e)}function q(e){if(!pe)throw ie;var t=Ce.getActiveStreamInfo();return t?ye.getTracksFor(e,t):[]}function Y(e,t){G(e,t);var r=new i.default;r.schemeIdUri=e,r.value=t,Se.getUTCTimingSources().push(r)}function G(e,t){var r=Se.getUTCTimingSources();r.forEach(function(n,a){n.schemeIdUri===e&&n.value===t&&r.splice(a,1)})}function V(){Y(E.default.DEFAULT_UTC_TIMING_SOURCE.scheme,E.default.DEFAULT_UTC_TIMING_SOURCE.value)}function z(){if(!Oe)throw oe;return Oe.getElement()}function W(e){if(!he)throw se;Oe=null,e&&((Oe=(0,I.default)(ue).getInstance()).initialize(),Oe.setElement(e),J(),function(){if(Ee)return Ee;var e=dashjs.MetricsReporting;if("function"==typeof e){var t=e(ue).create();Ee=t.createMetricsReporting({log:fe,eventBus:le,mediaElement:z(),dashManifestModel:be,metricsModel:Me})}}()),X()}function Q(e){if(!he)throw se;if("string"==typeof e){var t=(0,y.default)(ue).getInstance();t.initialize(),ce=t.parseURI(e)}else ce=e;X()}function X(){pe?(pe=!1,Te.reset(),Ce.reset(),je.reset(),ve.reset(),Ae.reset(),ye.reset(),Ce=null,Ee=null,e()&&re()):e()&&re()}function Z(){return(0,l.default)(ue).create({errHandler:Re,parser:(0,L.default)(ue).create(),metricsModel:Me,requestModifier:(0,m.default)(ue).getInstance()})}function J(){if(_e)return _e;var e=dashjs.Protection;if("function"==typeof e){var t=e(ue).create();return P.default.extend(e.events),w.default.extend(e.events,{publicOnly:!0}),_e=t.createProtectionSystem({log:fe,videoModel:Oe,capabilities:Ie,eventBus:le,adapter:Te})}return null}function $(){var e=Me.getReadOnlyMetricsFor("video")||Me.getReadOnlyMetricsFor("audio");return De.getCurrentDVRInfo(e)}function ee(e){var t=$();return t?e+(t.manifestInfo.availableFrom.getTime()/1e3+t.range.start):0}function te(){if(!pe)throw ie;var e=Ce.getActiveStreamInfo();return e?Ce.getStreamById(e.id):null}function re(){pe||(pe=!0,fe("Playback Initialized"),function(){var e=(0,A.default)(ue).getInstance();e.initialize();var t=(0,R.default)(ue).getInstance();t.initialize();var r=(0,h.default)(ue).getInstance();r.setConfig({dashManifestModel:be});var n=(0,p.default)(ue).getInstance();n.setConfig({sourceBufferController:r}),ye.initialize(),ye.setConfig({errHandler:Re}),(Ae=(0,C.default)(ue).getInstance()).initialize(),Ae.setConfig({abrRulesCollection:t,synchronizationRulesCollection:e}),(Ce=(0,s.default)(ue).getInstance()).setConfig({capabilities:Ie,manifestLoader:Z(),manifestModel:(0,_.default)(ue).getInstance(),dashManifestModel:be,protectionController:_e,adapter:Te,metricsModel:Me,dashMetrics:De,liveEdgeFinder:(0,d.default)(ue).getInstance(),mediaSourceController:(0,j.default)(ue).getInstance(),timeSyncController:(0,S.default)(ue).getInstance(),baseURLController:(0,D.default)(ue).getInstance(),virtualBuffer:n,errHandler:Re,timelineConverter:(0,k.default)(ue).getInstance()}),Ce.initialize(me,ge),ve.setConfig({abrRulesCollection:t,rulesController:Ae,streamController:Ce})}(),"string"==typeof ce?Ce.load(ce):Ce.loadWithManifest(ce))}var ne,ae="2.1.0",ie="You must first call play() to init playback before calling this method",oe="You must first call attachView() to set the video element before calling this method",se="MediaPlayer not initialized!",ue=this.context,le=(0,O.default)(ue).getInstance(),de=(0,b.default)(ue).getInstance(),fe=de.log,ce=void 0,ge=void 0,he=void 0,pe=void 0,me=void 0,ve=void 0,ye=void 0,_e=void 0,Ee=void 0,Te=void 0,Me=void 0,Se=void 0,Re=void 0,Ie=void 0,Ce=void 0,Ae=void 0,je=void 0,De=void 0,be=void 0,Oe=void 0,Pe=void 0;return ne={initialize:function(e,t,r){return Ie=(0,c.default)(ue).getInstance(),Re=(0,f.default)(ue).getInstance(),Ie.supportsMediaSource()?void(he||(he=!0,ve=(0,M.default)(ue).getInstance(),je=(0,o.default)(ue).getInstance(),ye=(0,u.default)(ue).getInstance(),ye.initialize(),be=(0,F.default)(ue).getInstance(),De=(0,B.default)(ue).getInstance(),Me=(0,T.default)(ue).getInstance(),Me.setConfig({adapter:(Te=(0,x.default)(ue).getInstance(),Te.initialize(),Te.setConfig({dashManifestModel:be}),Te)}),V(),U(void 0===r||r),e&&W(e),t&&Q(t),fe("[dash.js 2.1.0] MediaPlayer has been initialized"))):void Re.capabilityError("mediasource")},on:function(e,t,r){le.on(e,t,r)},off:function(e,t,r){le.off(e,t,r)},extend:function(e,t,r){N.default.extend(e,t,r,ue)},attachView:W,attachSource:Q,isReady:e,play:function(){if(!pe)throw ie;(!me||t()&&pe)&&je.play()},isPaused:t,pause:function(){if(!pe)throw ie;je.pause()},isSeeking:function(){if(!pe)throw ie;return je.isSeeking()},seek:function(e){if(!pe)throw ie;var t=je.getIsDynamic()?r(e):e;je.seek(t)},setMute:function(e){if(!Oe)throw oe;z().muted=e},isMuted:function(){if(!Oe)throw oe;return z().muted},setVolume:function(e){if(!Oe)throw oe;z().volume=e},getVolume:function(){if(!Oe)throw oe;return z().volume},time:n,duration:a,timeAsUTC:function(){if(!pe)throw ie;return n()<0?NaN:ee(n())},durationAsUTC:function(){if(!pe)throw ie;return ee(a())},getActiveStream:te,getDVRWindowSize:function(){var e=$();return e?e.manifestInfo.DVRWindowSize:0},getDVRSeekOffset:r,convertToTimeCode:function(e){e=Math.max(e,0);var t=Math.floor(e/3600),r=Math.floor(e%3600/60),n=Math.floor(e%3600%60);return(0===t?"":10>t?"0"+t.toString()+":":t.toString()+":")+(10>r?"0"+r.toString():r.toString())+":"+(10>n?"0"+n.toString():n.toString())},formatUTC:function(e,t,r){var n=new Date(1e3*e),a=n.toLocaleDateString(t);return n.toLocaleTimeString(t,{hour12:r})+" "+a},getVersion:function(){return ae},getDebug:function(){return de},getBufferLength:function(e){if(e){if("video"===e||"audio"===e||"fragmentedText"===e){var t=K().getCurrentBufferLevel(H(e));return t?t.toPrecision(3):NaN}return fe("Warning  - getBufferLength requested for invalid type"),NaN}var r=q("video").length>0?K().getCurrentBufferLevel(H("video")):Number.MAX_SAFE_INTEGER,n=q("audio").length>0?K().getCurrentBufferLevel(H("audio")):Number.MAX_SAFE_INTEGER,a=q("fragmentedText").length>0?K().getCurrentBufferLevel(H("fragmentedText")):Number.MAX_SAFE_INTEGER;return Math.min(r,n,a).toPrecision(3)},getVideoModel:function(){if(!Oe)throw oe;return Oe},getVideoContainer:function(){return Oe?Oe.getVideoContainer():null},getTTMLRenderingDiv:function(){return Oe?Oe.getTTMLRenderingDiv():null},getVideoElement:z,getSource:function(){if(!ce)throw"You must first call attachSource() with a valid source before calling this method";return ce},setLiveDelayFragmentCount:function(e){Se.setLiveDelayFragmentCount(e)},setLiveDelay:function(e){Se.setLiveDelay(e)},useSuggestedPresentationDelay:function(e){Se.setUseSuggestedPresentationDelay(e)},enableLastBitrateCaching:function(e,t){Se.setLastBitrateCachingInfo(e,t)},enableLastMediaSettingsCaching:function(e,t){Se.setLastMediaSettingsCachingInfo(e,t)},setMaxAllowedBitrateFor:function(e,t){ve.setMaxAllowedBitrateFor(e,t)},getMaxAllowedBitrateFor:function(e){return ve.getMaxAllowedBitrateFor(e)},setMaxAllowedRepresentationRatioFor:function(e,t){ve.setMaxAllowedRepresentationRatioFor(e,t)},getMaxAllowedRepresentationRatioFor:function(e){return ve.getMaxAllowedRepresentationRatioFor(e)},setAutoPlay:U,getAutoPlay:function(){return me},setScheduleWhilePaused:function(e){Se.setScheduleWhilePaused(e)},getScheduleWhilePaused:function(){return Se.getScheduleWhilePaused()},getDashMetrics:K,getMetricsFor:H,getQualityFor:function(e){if(!pe)throw ie;return ve.getQualityFor(e,Ce.getActiveStreamInfo())},setQualityFor:function(e,t){if(!pe)throw ie;ve.setPlaybackQuality(e,Ce.getActiveStreamInfo(),t)},getLimitBitrateByPortal:function(){return ve.getLimitBitrateByPortal()},setLimitBitrateByPortal:function(e){ve.setLimitBitrateByPortal(e)},setTextTrack:function(e){if(!pe)throw ie;void 0===Pe&&(Pe=(0,v.default)(ue).getInstance());for(var t=z().textTracks,r=t.length,n=0;r>n;n++){var a=t[n],i=e===n?"showing":"hidden";a.mode!==i&&(a.mode=i)}Pe.setTextTrack()},getBitrateInfoListFor:function(e){if(!pe)throw ie;var t=te();return t?t.getBitrateListFor(e):[]},setInitialBitrateFor:function(e,t){ve.setInitialBitrateFor(e,t)},getInitialBitrateFor:function(e){if(!pe)throw ie;return ve.getInitialBitrateFor(e)},setInitialRepresentationRatioFor:function(e,t){ve.setInitialRepresentationRatioFor(e,t)},getInitialRepresentationRatioFor:function(e){return ve.getInitialRepresentationRatioFor(e)},getStreamsFromManifest:function(e){if(!pe)throw ie;return Te.getStreamsInfo(e)},getTracksFor:q,getTracksForTypeFromManifest:function(e,t,r){if(!pe)throw ie;return(r=r||Te.getStreamsInfo(t)[0])?Te.getAllMediaInfoForType(t,r,e):[]},getCurrentTrackFor:function(e){if(!pe)throw ie;var t=Ce.getActiveStreamInfo();return t?ye.getCurrentTrackFor(e,t):null},setInitialMediaSettingsFor:function(e,t){ye.setInitialSettings(e,t)},getInitialMediaSettingsFor:function(e){return ye.getInitialSettings(e)},setCurrentTrack:function(e){if(!pe)throw ie;ye.setTrack(e)},getTrackSwitchModeFor:function(e){return ye.getSwitchMode(e)},setTrackSwitchModeFor:function(e,t){ye.setSwitchMode(e,t)},setSelectionModeForInitialTrack:function(e){ye.setSelectionModeForInitialTrack(e)},getSelectionModeForInitialTrack:function(){return ye.getSelectionModeForInitialTrack()},getAutoSwitchQuality:function(){return ve.getAutoSwitchBitrateFor("video")||ve.getAutoSwitchBitrateFor("audio")},setAutoSwitchQuality:function(e){ve.setAutoSwitchBitrateFor("video",e),ve.setAutoSwitchBitrateFor("audio",e)},getAutoSwitchQualityFor:function(e){return ve.getAutoSwitchBitrateFor(e)},setAutoSwitchQualityFor:function(e,t){ve.setAutoSwitchBitrateFor(e,t)},enableBufferOccupancyABR:function(e){Se.setBufferOccupancyABREnabled(e)},setBandwidthSafetyFactor:function(e){Se.setBandwidthSafetyFactor(e)},getBandwidthSafetyFactor:function(){return Se.getBandwidthSafetyFactor()},setAbandonLoadTimeout:function(e){Se.setAbandonLoadTimeout(e)},retrieveManifest:function(e,t){var r=Z(),n=this;le.on(P.default.INTERNAL_MANIFEST_LOADED,function e(a){a.error?t(null,a.error):t(a.manifest),le.off(P.default.INTERNAL_MANIFEST_LOADED,e,n),r.reset()},n);var a=(0,y.default)(ue).getInstance();a.initialize(),r.load(a.parseURI(e))},addUTCTimingSource:Y,removeUTCTimingSource:G,clearDefaultUTCTimingSources:function(){Se.setUTCTimingSources([])},restoreDefaultUTCTimingSources:V,setBufferToKeep:function(e){Se.setBufferToKeep(e)},setBufferPruningInterval:function(e){Se.setBufferPruningInterval(e)},setStableBufferTime:function(e){Se.setStableBufferTime(e)},setBufferTimeAtTopQuality:function(e){Se.setBufferTimeAtTopQuality(e)},setFragmentLoaderRetryAttempts:function(e){Se.setFragmentRetryAttempts(e)},setFragmentLoaderRetryInterval:function(e){Se.setFragmentRetryInterval(e)},setBufferTimeAtTopQualityLongForm:function(e){Se.setBufferTimeAtTopQualityLongForm(e)},setLongFormContentDurationThreshold:function(e){Se.setLongFormContentDurationThreshold(e)},setRichBufferThreshold:function(e){Se.setRichBufferThreshold(e)},getProtectionController:function(){return J()},attachProtectionController:function(e){_e=e},setProtectionData:function(e){ge=e},enableManifestDateHeaderTimeSource:function(e){Se.setUseManifestDateHeaderTimeSource(e)},displayCaptionsOnTop:function(e){var t=(0,g.default)(ue).getInstance();t.setConfig({videoModel:Oe}),t.initialize(),t.displayCConTop(e)},attachVideoContainer:function(e){if(!Oe)throw oe;Oe.setVideoContainer(e)},attachTTMLRenderingDiv:function(e){if(!Oe)throw oe;Oe.setTTMLRenderingDiv(e)},reset:function(){Q(null),W(null),ge=null,_e=null}},he=!1,pe=!1,me=!0,_e=null,ge=null,Te=null,P.default.extend(w.default),Se=(0,E.default)(ue).getInstance(),ne}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../dash/vo/UTCTiming.js")),o=n(e("./controllers/PlaybackController.js")),s=n(e("./controllers/StreamController.js")),u=n(e("./controllers/MediaController.js")),l=n(e("./ManifestLoader.js")),d=n(e("./utils/LiveEdgeFinder.js")),f=n(e("./utils/ErrorHandler.js")),c=n(e("./utils/Capabilities.js")),g=n(e("./TextTracks.js")),h=n(e("./controllers/SourceBufferController.js")),p=n(e("./VirtualBuffer.js")),m=n(e("./utils/RequestModifier.js")),v=n(e("./TextSourceBuffer.js")),y=n(e("./models/URIQueryAndFragmentModel.js")),_=n(e("./models/ManifestModel.js")),E=n(e("./models/MediaPlayerModel.js")),T=n(e("./models/MetricsModel.js")),M=n(e("./controllers/AbrController.js")),S=n(e("./controllers/TimeSyncController.js")),R=n(e("./rules/abr/ABRRulesCollection.js")),I=n(e("./models/VideoModel.js")),C=n(e("./rules/RulesController.js")),A=n(e("./rules/synchronization/SynchronizationRulesCollection.js")),j=n(e("./controllers/MediaSourceController.js")),D=n(e("./controllers/BaseURLController.js")),b=n(e("./../core/Debug.js")),O=n(e("./../core/EventBus.js")),P=n(e("./../core/events/Events.js")),w=n(e("./MediaPlayerEvents.js")),N=n(e("../core/FactoryMaker.js")),x=n(e("../dash/DashAdapter.js")),L=n(e("../dash/DashParser.js")),F=n(e("../dash/models/DashManifestModel.js")),B=n(e("../dash/DashMetrics.js")),k=n(e("../dash/utils/TimelineConverter.js"));a.__dashjs_factory_name="MediaPlayer";var U=N.default.getClassFactory(a);U.events=w.default,r.default=U,t.exports=r.default},{"../core/FactoryMaker.js":9,"../dash/DashAdapter.js":13,"../dash/DashMetrics.js":15,"../dash/DashParser.js":16,"../dash/models/DashManifestModel.js":19,"../dash/utils/TimelineConverter.js":25,"../dash/vo/UTCTiming.js":35,"./../core/Debug.js":7,"./../core/EventBus.js":8,"./../core/events/Events.js":11,"./ManifestLoader.js":37,"./MediaPlayerEvents.js":40,"./TextSourceBuffer.js":44,"./TextTracks.js":45,"./VirtualBuffer.js":46,"./controllers/AbrController.js":49,"./controllers/BaseURLController.js":50,"./controllers/MediaController.js":55,"./controllers/MediaSourceController.js":56,"./controllers/PlaybackController.js":57,"./controllers/SourceBufferController.js":59,"./controllers/StreamController.js":60,"./controllers/TimeSyncController.js":62,"./models/ManifestModel.js":89,"./models/MediaPlayerModel.js":90,"./models/MetricsModel.js":91,"./models/URIQueryAndFragmentModel.js":92,"./models/VideoModel.js":93,"./rules/RulesController.js":118,"./rules/abr/ABRRulesCollection.js":120,"./rules/synchronization/SynchronizationRulesCollection.js":133,"./utils/Capabilities.js":136,"./utils/ErrorHandler.js":139,"./utils/LiveEdgeFinder.js":141,"./utils/RequestModifier.js":143}],40:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){function t(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,t),function(e,t,r){for(var n=!0;n;){var a=e,i=t,o=r;n=!1,null===a&&(a=Function.prototype);var s=Object.getOwnPropertyDescriptor(a,i);if(void 0!==s){if("value"in s)return s.value;var u=s.get;if(void 0===u)return;return u.call(o)}var l=Object.getPrototypeOf(a);if(null===l)return;e=l,t=i,r=o,n=!0,s=l=void 0}}(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),this.BUFFER_EMPTY="bufferstalled",this.BUFFER_LOADED="bufferloaded",this.BUFFER_LEVEL_STATE_CHANGED="bufferStateChanged",this.ERROR="error",this.LOG="log",this.MANIFEST_LOADED="manifestloaded",this.METRICS_CHANGED="metricschanged",this.METRIC_CHANGED="metricchanged",this.METRIC_ADDED="metricadded",this.METRIC_UPDATED="metricupdated",this.PERIOD_SWITCH_COMPLETED="streamswitchcompleted",this.PERIOD_SWITCH_STARTED="streamswitchstarted",this.STREAM_INITIALIZED="streaminitialized",this.TEXT_TRACKS_ADDED="alltexttracksadded",this.TEXT_TRACK_ADDED="texttrackadded",this.CAN_PLAY="canPlay",this.PLAYBACK_ENDED="playbackEnded",this.PLAYBACK_ERROR="playbackError",this.PLAYBACK_METADATA_LOADED="playbackMetaDataLoaded",this.PLAYBACK_PAUSED="playbackPaused",this.PLAYBACK_PLAYING="playbackPlaying",this.PLAYBACK_PROGRESS="playbackProgress",this.PLAYBACK_RATE_CHANGED="playbackRateChanged",this.PLAYBACK_SEEKED="playbackSeeked",this.PLAYBACK_SEEKING="playbackSeeking",this.PLAYBACK_STARTED="playbackStarted",this.PLAYBACK_TIME_UPDATED="playbackTimeUpdated"}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(function(e){return e&&e.__esModule?e:{default:e}}(e("../core/events/EventsBase.js")).default),a=new n;r.default=a,t.exports=r.default},{"../core/events/EventsBase.js":12}],41:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./MediaPlayer.js")),a=function(){function e(e,t,a){if(!e||"VIDEO"!==e.nodeName)return null;if(e._dashjs_player)return e._dashjs_player;var i,o=e.id||e.name||"video element";if(!(t=t||[].slice.call(e.querySelectorAll("source")).filter(function(e){return e.type==r})[0])&&e.src)(t=document.createElement("source")).src=e.src;else if(!t&&!e.src)return null;return a=a||{},(i=(0,n.default)(a).create()).initialize(e,t.src,e.autoplay),i.getDebug().log("Converted "+o+" to dash.js player and added content: "+t.src),e._dashjs_player=i,i}function t(e){for(var t=!0;t;){var r=e;if(t=!1,"video"===r.nodeName.toLowerCase())return r;e=r.parentNode,t=!0}}var r="application/dash+xml";return{create:e,createAll:function(n,a){var i=[];n=n||"[data-dashjs-player]";for(var o=(a=a||document).querySelectorAll(n),s=0;s<o.length;s++){var u=e(o[s],null);i.push(u)}var l=a.querySelectorAll('source[type="'+r+'"]');for(s=0;s<l.length;s++)u=e(t(l[s]),null),i.push(u);return i}}}();!(window&&window.dashjs&&window.dashjs.skipAutoCreate)&&window&&window.addEventListener&&("complete"===window.document.readyState?a.createAll():window.addEventListener("load",function e(){window.removeEventListener("load",e),a.createAll()})),r.default=a,t.exports=r.default},{"./MediaPlayer.js":39}],42:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(){for(var e=q.length,t=0;e>t;t++)q[t].reset();q=[],Y=!1,G=!1,te&&te.clear(),N.off(y.default.CURRENT_TRACK_CHANGED,E,H)}function r(){J&&(J.pause(),J=null),ee&&(ee.reset(),ee=null),Z.abortSearch(),t(),$=null,re=null,L=null,x=null,F=null,B=null,w=null,k=null,W=!1,Q=!1,z={},N.off(y.default.DATA_UPDATE_COMPLETED,D,H),N.off(y.default.BUFFERING_COMPLETED,j,H),N.off(y.default.KEY_ERROR,n,H),N.off(y.default.SERVER_CERTIFICATE_UPDATED,n,H),N.off(y.default.LICENSE_REQUEST_COMPLETE,n,H),N.off(y.default.KEY_SYSTEM_SELECTED,n,H),N.off(y.default.KEY_SESSION_CREATED,n,H)}function n(e){e.error&&(k.mediaKeySessionError(e.error),w(e.error),r())}function a(e,t,r){var n,a,i=e.type;if("muxed"===i&&e)return w(a="Multiplexed representations are intentionally not supported, as they are not compliant with the DASH-AVC/264 guidelines"),k.manifestError(a,"multiplexedrep",x.getValue()),!1;if("text"===i||"fragmentedText"===i||"embeddedText"===i)return!0;if(n=e.codec,w(i+" codec: "+n),e.contentProtection&&!B.supportsEncryptedMedia())k.capabilityError("encryptedmedia");else if(!B.supportsCodec((0,f.default)(P).getInstance().getElement(),n))return a=i+"Codec ("+n+") is not supported.",k.manifestError(a,"codec",r),w(a),!1;return!0}function E(e){if(e.newMediaInfo.streamInfo.id===V.id){var t=function(e){return!!e&&b().filter(function(t){return t.getType()===e.type})[0]}(e.oldMediaInfo);if(t){var r=J.getTime(),n=t.getBuffer(),a=e.newMediaInfo,i=x.getValue(),o=q.indexOf(t),s=t.getMediaSource();"fragmentedText"!==a.type?(t.reset(!0),S(a,i,s,{buffer:n,replaceIdx:o,currentTime:r}),J.seek(J.getTime())):t.updateMediaInfo(i,a)}}}function M(){var e=(0,p.default)(P).getInstance();return e.setConfig({baseURLController:K}),e.initialize(),(0,h.default)(P).create({segmentBaseLoader:e,timelineConverter:U,dashMetrics:(0,m.default)(P).getInstance(),metricsModel:(0,c.default)(P).getInstance(),baseURLController:K})}function S(e,t,r,n){var a=(0,o.default)(P).create({indexHandler:M(),timelineConverter:U,adapter:F,manifestModel:x}),i=F.getAllMediaInfoForType(t,V,e.type);if(a.initialize(function(e){return"text"===e.type?e.mimeType:e.type}(e),ee,r,H,te),re.updateTopQualityIndex(e),n?(a.setBuffer(n.buffer),q[n.replaceIdx]=a):q.push(a),"text"===e.type||"fragmentedText"===e.type){for(var s,u=0;u<i.length;u++)i[u].index===e.index&&(s=u),a.updateMediaInfo(t,i[u]);"fragmentedText"===e.type&&a.updateMediaInfo(t,i[s])}else a.updateMediaInfo(t,e);return a}function R(e,t){var r=x.getValue(),n=F.getAllMediaInfoForType(r,V,e),i=null;if(n&&0!==n.length){for(var o=0,s=n.length;s>o;o++)if(i=n[o],"embeddedText"===e)ne.addEmbeddedTrack(i);else{if(!a(i,0,r))continue;$.isMultiTrackSupportedByType(i.type)&&$.addTrack(i,V)}"embeddedText"!==e&&0!==$.getTracksFor(e,V).length&&($.checkInitialMediaSettingsForType(e,V),S($.getCurrentTrackFor(e,V),r,t))}else w("No "+e+" data.")}function I(){for(var e=q.length,t=z.audio||z.video?new Error(O,"Data update failed",null):null,r=0;e>r;r++)if(q[r].isUpdating()||W)return;Q=!0,Y=!0,G&&(X&&X.initialize(x.getValue(),C("audio"),C("video")),N.trigger(y.default.STREAM_INITIALIZED,{streamInfo:V,error:t}))}function C(e){for(var t=q.length,r=null,n=0;t>n;n++)if((r=q[n]).getType()===e)return r.getMediaInfo();return null}function A(){for(var e=0,t=q.length;t>e;e++)q[e].createBuffer()}function j(e){if(e.streamInfo===V){for(var t=b(),r=t.length,n=0;r>n;n++)if(!t[n].isBufferingCompleted())return;N.trigger(y.default.STREAM_BUFFERING_COMPLETED,{streamInfo:V})}}function D(e){var t=e.sender.getStreamProcessor();t.getStreamInfo()===V&&(z[t.getType()]=e.error,I())}function b(){for(var e,t,r=q.length,n=[],a=0;r>a;a++)("audio"===(e=(t=q[a]).getType())||"video"===e||"fragmentedText"===e)&&n.push(t);return n}var O=1,P=this.context,w=(0,_.default)(P).getInstance().log,N=(0,v.default)(P).getInstance(),x=e.manifestModel,L=e.manifestUpdater,F=e.adapter,B=e.capabilities,k=e.errHandler,U=e.timelineConverter,K=e.baseURLController,H=void 0,q=void 0,Y=void 0,G=void 0,V=void 0,z=void 0,W=void 0,Q=void 0,X=void 0,Z=void 0,J=void 0,$=void 0,ee=void 0,te=void 0,re=void 0,ne=void 0;return H={initialize:function(e,t){V=e,(X=t)&&(N.on(y.default.KEY_ERROR,n,H),N.on(y.default.SERVER_CERTIFICATE_UPDATED,n,H),N.on(y.default.LICENSE_REQUEST_COMPLETE,n,H),N.on(y.default.KEY_SYSTEM_SELECTED,n,H),N.on(y.default.KEY_SESSION_CREATED,n,H))},activate:function(e){Y?A():(N.on(y.default.CURRENT_TRACK_CHANGED,E,H),function(e){var t,r=x.getValue();if((te=(0,u.default)(P).getInstance()).initialize(),te.setConfig({manifestModel:x,manifestUpdater:L}),t=F.getEventsFor(r,V),te.addInlineEvents(t),W=!0,R("video",e),R("audio",e),R("text",e),R("fragmentedText",e),R("embeddedText",e),R("muxed",e),A(),G=!0,W=!1,0===q.length){var n="No streams to play.";k.manifestError(n,"nostreams",r),w(n)}else Z.initialize(U,q[0]),I()}(e))},deactivate:t,getDuration:function(){return V.duration},getStartTime:function(){return V.start},getStreamIndex:function(){return V.index},getId:function(){return V.id},getStreamInfo:function(){return V},hasMedia:function(e){return null!==C(e)},getBitrateListFor:function(e){var t=C(e);return re.getBitrateList(t)},startEventController:function(){te&&te.start()},isActivated:function(){return Y},isInitialized:function(){return Q},updateData:function(e){var t,r,n,a=q.length,i=x.getValue(),o=0;for(Y=!1,V=e,w("Manifest updated... set new data on buffers."),te&&(r=F.getEventsFor(i,V),te.addInlineEvents(r)),W=!0,Q=!1;a>o;o++)n=q[o],t=F.getMediaInfoForType(i,V,n.getType()),re.updateTopQualityIndex(t),n.updateMediaInfo(i,t);W=!1,I()},reset:r,getProcessors:b},q=[],Y=!1,G=!1,V=null,z={},W=!1,Q=!1,Z=(0,i.default)(P).getInstance(),J=(0,g.default)(P).getInstance(),re=(0,d.default)(P).getInstance(),$=(0,s.default)(P).getInstance(),ee=(0,l.default)(P).create(),ne=(0,T.default)(P).getInstance(),N.on(y.default.BUFFERING_COMPLETED,j,H),N.on(y.default.DATA_UPDATE_COMPLETED,D,H),H}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./utils/LiveEdgeFinder.js")),o=n(e("./StreamProcessor.js")),s=n(e("./controllers/MediaController.js")),u=n(e("./controllers/EventController.js")),l=n(e("./controllers/FragmentController.js")),d=n(e("./controllers/AbrController.js")),f=n(e("./models/VideoModel.js")),c=n(e("./models/MetricsModel.js")),g=n(e("./controllers/PlaybackController.js")),h=n(e("../dash/DashHandler.js")),p=n(e("../dash/SegmentBaseLoader.js")),m=n(e("../dash/DashMetrics.js")),v=n(e("../core/EventBus.js")),y=n(e("../core/events/Events.js")),_=n(e("../core/Debug.js")),E=n(e("../core/FactoryMaker.js")),T=n(e("./TextSourceBuffer.js"));a.__dashjs_factory_name="Stream",r.default=E.default.getClassFactory(a),t.exports=r.default},{"../core/Debug.js":7,"../core/EventBus.js":8,"../core/FactoryMaker.js":9,"../core/events/Events.js":11,"../dash/DashHandler.js":14,"../dash/DashMetrics.js":15,"../dash/SegmentBaseLoader.js":17,"./StreamProcessor.js":43,"./TextSourceBuffer.js":44,"./controllers/AbrController.js":49,"./controllers/EventController.js":53,"./controllers/FragmentController.js":54,"./controllers/MediaController.js":55,"./controllers/PlaybackController.js":57,"./models/MetricsModel.js":91,"./models/VideoModel.js":93,"./utils/LiveEdgeFinder.js":141}],43:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t,r=this.context,n=e.indexHandler,a=e.timelineConverter,R=e.adapter,I=e.manifestModel,C=void 0,A=void 0,j=void 0,D=void 0,b=void 0,O=void 0,P=void 0,w=void 0,N=void 0,x=void 0,L=void 0,F=void 0,B=void 0;return t={initialize:function(e,t,A,D,k){j=e,O=k,L=t,C=(b=D).getStreamInfo().manifestInfo.isDynamic,n.initialize(this),(P=(0,i.default)(r).getInstance()).initialize(j,this),w=function(e){return"video"===e||"audio"===e||"fragmentedText"===e?(0,o.default)(r).create({metricsModel:(0,g.default)(r).getInstance(),manifestModel:I,sourceBufferController:(0,m.default)(r).getInstance(),errHandler:(0,S.default)(r).getInstance(),mediaSourceController:(0,_.default)(r).getInstance(),streamController:(0,s.default)(r).getInstance(),mediaController:(0,u.default)(r).getInstance(),adapter:R,virtualBuffer:(0,y.default)(r).getInstance(),textSourceBuffer:(0,v.default)(r).getInstance()}):(0,l.default)(r).create({errHandler:(0,S.default)(r).getInstance(),sourceBufferController:(0,m.default)(r).getInstance()})}(e),w.initialize(j,A,this),(N=(0,d.default)(r).create({metricsModel:(0,g.default)(r).getInstance(),manifestModel:I,adapter:R,dashMetrics:(0,T.default)(r).getInstance(),dashManifestModel:(0,E.default)(r).getInstance(),timelineConverter:a,rulesController:(0,f.default)(r).getInstance(),mediaPlayerModel:(0,c.default)(r).getInstance()})).initialize(j,this),F=(0,h.default)(r).create({metricsModel:(0,g.default)(r).getInstance(),errHandler:(0,S.default)(r).getInstance(),requestModifier:(0,p.default)(r).getInstance()}),(x=(0,M.default)(r).create()).initialize(this),(B=N.getFragmentModel()).setLoader(F)},isUpdating:function(){return x.isUpdating()},getType:function(){return j},getBufferController:function(){return w},getABRController:function(){return P},getFragmentLoader:function(){return F},getFragmentModel:function(){return B},getScheduleController:function(){return N},getEventController:function(){return O},getFragmentController:function(){return L},getRepresentationController:function(){return x},getIndexHandler:function(){return n},getCurrentRepresentationInfo:function(){return R.getCurrentRepresentationInfo(I.getValue(),x)},getRepresentationInfoForQuality:function(e){return R.getRepresentationInfoForQuality(I.getValue(),x,e)},isBufferingCompleted:function(){return w.getIsBufferingCompleted()},createBuffer:function(){return w.getBuffer()||w.createBuffer(A)},getStreamInfo:function(){return b.getStreamInfo()},updateMediaInfo:function(e,t){t===A||t&&A&&t.type!==A.type||(A=t),-1===D.indexOf(t)&&D.push(t),R.updateData(e,this)},getMediaInfoArr:function(){return D},getMediaInfo:function(){return A},getMediaSource:function(){return w.getMediaSource()},getBuffer:function(){return w.getBuffer()},setBuffer:function(e){w.setBuffer(e)},start:function(){N.start()},stop:function(){N.stop()},isDynamic:function(){return C},reset:function(e){B&&(B.reset(),B=null),n.reset(),w&&(w.reset(e),w=null),N&&(N.reset(),N=null),x&&(x.reset(),x=null),L=null,F=null,O=null,b=null,C=null,A=null,D=[],j=null}},D=[],t}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./controllers/AbrController.js")),o=n(e("./controllers/BufferController.js")),s=n(e("./controllers/StreamController.js")),u=n(e("./controllers/MediaController.js")),l=n(e("./controllers/TextController.js")),d=n(e("./controllers/ScheduleController.js")),f=n(e("./rules/RulesController.js")),c=n(e("./models/MediaPlayerModel.js")),g=n(e("./models/MetricsModel.js")),h=n(e("./FragmentLoader.js")),p=n(e("./utils/RequestModifier.js")),m=n(e("./controllers/SourceBufferController")),v=n(e("./TextSourceBuffer.js")),y=n(e("./VirtualBuffer.js")),_=n(e("./controllers/MediaSourceController.js")),E=n(e("../dash/models/DashManifestModel.js")),T=n(e("../dash/DashMetrics.js")),M=n(e("../dash/controllers/RepresentationController.js")),S=n(e("./utils/ErrorHandler.js")),R=n(e("../core/FactoryMaker.js"));a.__dashjs_factory_name="StreamProcessor",r.default=R.default.getClassFactory(a),t.exports=r.default},{"../core/FactoryMaker.js":9,"../dash/DashMetrics.js":15,"../dash/controllers/RepresentationController.js":18,"../dash/models/DashManifestModel.js":19,"./FragmentLoader.js":36,"./TextSourceBuffer.js":44,"./VirtualBuffer.js":46,"./controllers/AbrController.js":49,"./controllers/BufferController.js":52,"./controllers/MediaController.js":55,"./controllers/MediaSourceController.js":56,"./controllers/ScheduleController.js":58,"./controllers/SourceBufferController":59,"./controllers/StreamController.js":60,"./controllers/TextController.js":61,"./models/MediaPlayerModel.js":90,"./models/MetricsModel.js":91,"./rules/RulesController.js":118,"./utils/ErrorHandler.js":139,"./utils/RequestModifier.js":143}],44:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){q=[],P=[],k=(0,f.default)(_).getInstance(),(w=(0,c.default)(_).getInstance()).setConfig({videoModel:k}),w.initialize(),S=(0,s.default)(_).getInstance(),(O=(0,o.default)(_).getInstance()).setConfig({boxParser:S}),N=!1,H=null,Y=0,z=[],V=[],G=null,T=!0}function t(e){var t,r=function(e,t,r){var n=e.length;if(n>0){if(t>=e[n-1][0])e.push([t,r]);else for(var a=n-1;a>=0;a--)if(t<e[a][0]){e.splice(a,0,[t,r]);break}}else e.push([t,r])},n=S.parse(e),a=n.getBox("moof"),i=n.getBox("tfdt"),o=n.getBoxes("trun");if(0===o.length)return null;t=o[0],o.length>1&&E("Warning: Too many truns");for(var s=a.offset+t.data_offset,u=t.sample_count,l=s,d=i.baseMediaDecodeTime,f=new DataView(e),c={startTime:null,endTime:null,fields:[[],[]]},g=0,p=0;u>p;p++){for(var m=t.samples[p],v=d+g+m.sample_composition_time_offset,y=h.default.findCea608Nalus(f,l,m.sample_size),_=0;_<y.length;_++)for(var T=h.default.extractCea608DataFromRange(f,y[_]),M=0;2>M;M++)T[M].length>0&&r(c.fields[M],v,T[M]);g+=m.sample_duration,l+=m.sample_size}var R=d+g;return c.startTime=d,c.endTime=R,c}function r(e){for(var t="",r=0;r<e.length;++r)t+=e[r].uchar;return t.length-t.replace(/^\s+/,"").length}function n(e){return"left: "+3.125*e.x+"%; top: "+6.66*e.y1+"%; width: "+(100-3.125*e.x)+"%; height: "+6.66*Math.max(e.y2-1-e.y1,1)+"%; align-items: flex-start; overflow: visible; -webkit-writing-mode: horizontal-tb;"}function a(e){return"red"==e?"rgb(255, 0, 0)":"green"==e?"rgb(0, 255, 0)":"blue"==e?"rgb(0, 0, 255)":"cyan"==e?"rgb(0, 255, 255)":"magenta"==e?"rgb(255, 0, 255)":"yellow"==e?"rgb(255, 255, 0)":"white"==e?"rgb(255, 255, 255)":"black"==e?"rgb(0, 0, 0)":e}function l(e,t){var r=e.videoHeight/15;return t?"font-size: "+r+"px; font-family: Menlo, Consolas, 'Cutive Mono', monospace; color: "+(t.foreground?a(t.foreground):"rgb(255, 255, 255)")+"; font-style: "+(t.italics?"italic":"normal")+"; text-decoration: "+(t.underline?"underline":"none")+"; white-space: pre; background-color: "+(t.background?a(t.background):"transparent")+";":"font-size: "+r+"px; font-family: Menlo, Consolas, 'Cutive Mono', monospace; justify-content: flex-start; text-align: left; color: rgb(255, 255, 255); font-style: normal; white-space: pre; line-height: normal; font-weight: normal; text-decoration: none; width: 100%; display: flex;"}function p(e){return e.replace(/^\s+/g,"")}function m(e){return e.replace(/\s+$/g,"")}function v(e,t,a,i){var o=null,s=!1,u=-1,d={start:t,end:a,spans:[]},f="style_cea608_white_black",c={},g={},h=[],v=void 0,y=void 0;for(v=0;15>v;++v){var _=i.rows[v],E="",T=null;if(!1===_.isEmpty()){var S=r(_.chars);null===o&&(o={x:S,y1:v,y2:v+1,p:[]}),S!==u&&s&&(o.p.push(d),d={start:t,end:a,spans:[]},o.y2=v,o.name="region_"+o.x+"_"+o.y1+"_"+o.y2,!1===c.hasOwnProperty(o.name)?(h.push(o),c[o.name]=o):c[o.name].p.contat(o.p),o={x:S,y1:v,y2:v+1,p:[]});for(var R=0;R<_.chars.length;++R){var I=_.chars[R],C=I.penState;if(null===T||!C.equals(T)){E.trim().length>0&&(d.spans.push({name:f,line:E,row:v}),E="");var A="style_cea608_"+C.foreground+"_"+C.background;C.underline&&(A+="_underline"),C.italics&&(A+="_italics"),g.hasOwnProperty(A)||(g[A]=JSON.parse(JSON.stringify(C))),T=C,f=A}E+=I.uchar}E.trim().length>0&&d.spans.push({name:f,line:E,row:v}),s=!0,u=S}else s=!1,u=-1,o&&(o.p.push(d),d={start:t,end:a,spans:[]},o.y2=v,o.name="region_"+o.x+"_"+o.y1+"_"+o.y2,!1===c.hasOwnProperty(o.name)?(h.push(o),c[o.name]=o):c[o.name].p.contat(o.p),o=null)}o&&(o.p.push(d),o.y2=v+1,o.name="region_"+o.x+"_"+o.y1+"_"+o.y2,!1===c.hasOwnProperty(o.name)?(h.push(o),c[o.name]=o):c[o.name].p.contat(o.p),o=null);var j=[];for(v=0;v<h.length;++v){var D=h[v],b="sub_"+M++,O=document.createElement("div");O.id="subtitle_"+b;var P=n(D);O.style.cssText="position: absolute; margin: 0; display: flex; box-sizing: border-box; pointer-events: none;"+P;var w=document.createElement("div");w.className="paragraph bodyStyle",w.style.cssText=l(e);var N=document.createElement("div");N.className="cueUniWrapper",N.style.cssText="unicode-bidi: normal; direction: ltr;";for(var x=0;x<D.p.length;++x){var L=D.p[x],F=0;for(y=0;y<L.spans.length;++y){var B=L.spans[y];if(B.line.length>0){if(0!==y&&F!=B.row){var k=document.createElement("br");k.className="lineBreak",N.appendChild(k)}var U=!1;F===B.row&&(U=!0),F=B.row;var K=g[B.name],H=document.createElement("span");H.className="spanPadding "+B.name+" customSpanColor",H.style.cssText=l(e,K),0!==y&&U?y===L.spans.length-1?H.textContent=m(B.line):H.textContent=B.line:0===y&&L.spans.length>1&&B.row===L.spans[1].row?H.textContent=p(B.line):H.textContent=B.line.trim(),N.appendChild(H)}}}w.appendChild(N),O.appendChild(w);var q={bodyStyle:90};for(y in g)g.hasOwnProperty(y)&&(q[y]=90);j.push({type:"html",start:t,end:a,cueHTMLElement:O,cueID:b,cellResolution:[32,15],isFromCEA608:!0,regions:h,regionID:D.name,videoHeight:e.videoHeight,videoWidth:e.videoWidth,fontSize:q||{defaultFontSize:"100"},lineHeight:{},linePadding:{}})}return j}function y(e){var t;return e.search("vtt")>=0?t=D:(e.search("ttml")>=0||e.search("stpp")>=0)&&(t=b).setConfig({videoModel:k}),t}var _=this.context,E=(0,d.default)(_).getInstance().log,T=!1,M=0,S=void 0,R=void 0,I=void 0,C=void 0,A=void 0,j=void 0,D=void 0,b=void 0,O=void 0,P=void 0,w=void 0,N=void 0,x=void 0,L=void 0,F=void 0,B=void 0,k=void 0,U=void 0,K=void 0,H=void 0,q=void 0,Y=void 0,G=void 0,V=void 0,z=void 0;return{initialize:function(t,r){A=!1,j=null,x=null,L=!1,F=NaN,B=[],K=null,T||e();var n=r.getStreamProcessor();if(P=n.getMediaInfoArr(),w.setConfig({videoModel:k}),w.initialize(),N=!I.getIsTextTrack(t),S=(0,s.default)(_).getInstance(),(O=(0,o.default)(_).getInstance()).setConfig({boxParser:S}),N){x=n.getFragmentModel(),this.buffered=(0,u.default)(_).create(),B=C.getTracksFor("fragmentedText",U.getActiveStreamInfo());for(var a=C.getCurrentTrackFor("fragmentedText",U.getActiveStreamInfo()),i=0;i<B.length;i++)if(B[i]===a){H=i;break}}},append:function(e,r){function n(e,t){var r=new i.default,n={subtitle:"subtitles",caption:"captions"};r.captionData=e,r.lang=t.lang,r.label=t.id,r.index=t.index,r.isTTML=function(){var e=!1;return t.codec&&t.codec.search("stpp")>=0&&(e=!0),t.mimeType&&t.mimeType.search("ttml")>=0&&(e=!0),e}(),r.video=k.getElement(),r.defaultTrack=function(e){var t=!1;return q.length>1?t=e.id&&"CC1"===e.id:1===q.length?e.id&&"CC"===e.id.substring(0,2)&&(t=!0):t=e.index===P[0].index,t}(t),r.isFragmented=N,r.isEmbedded=!!t.isEmbedded,r.kind=function(){var e=t.roles.length>0?n[t.roles[0]]:n.caption;return e===n.caption||e===n.subtitle?e:n.caption}();var a=(P?P.length:0)+q.length;w.addTextTrack(r,a)}var a,o,s,u,l=r.mediaInfo,d=l.type,f=l.mimeType,c=l.codec||f;if(c){if("fragmentedText"===d)if(L)if(o=O.getSamplesInfo(e).sampleList,!K&&o.length>0&&(K=o[0].cts-r.start*F),c.search("stpp")>=0)for(j=null!==j?j:y(c),s=0;s<o.length;s++){var p=o[s],m=p.cts,_=m-K;this.buffered.add(_/F,(_+p.duration)/F);var T=new DataView(e,p.offset,p.size);u=g.default.Utils.dataViewToString(T,"utf-8");try{a=j.parse(u,m/F,(m+p.duration)/F),w.addCaptions(H,K/F,a)}catch(e){E("TTML parser error: "+e.message)}}else{var M=[];for(s=0;s<o.length;s++){var S=o[s];S.cts-=K,this.buffered.add(S.cts/F,(S.cts+S.duration)/F);for(var I=e.slice(S.offset,S.offset+S.size),C=g.default.parseBuffer(I),A=0;A<C.boxes.length;A++){var D=C.boxes[A];if(E("VTT box1: "+D.type),"vtte"!==D.type&&"vttc"===D.type){E("VTT vttc boxes.length = "+D.boxes.length);for(var b=0;b<D.boxes.length;b++){var x=D.boxes[b];if(E("VTT box2: "+x.type),"payl"===x.type){var B=x.cue_text;E("VTT cue_text = "+B);var U=S.cts/F,W=(S.cts+S.duration)/F;M.push({start:U,end:W,data:B,styles:{}}),E("VTT "+U+"-"+W+" : "+B)}}}}}M.length>0&&w.addCaptions(H,0,M)}else{for(L=!0,s=0;s<P.length;s++)n(null,P[s]);F=O.getMediaTimescaleFromMoov(e)}else if("text"===d){T=new DataView(e,0,e.byteLength),u=g.default.Utils.dataViewToString(T,"utf-8");try{n(a=y(c).parse(u),l)}catch(e){R.timedTextError(e,"parse",u)}}else if("video"===d)if("InitializationSegment"===r.segmentType){if(0===Y)for(Y=O.getMediaTimescaleFromMoov(e),s=0;s<q.length;s++)n(null,q[s])}else{if(0===Y)return void E("CEA-608: No timescale for embeddedTextTrack yet");var Q=function(e,t){return function(e,r,n){var a;(a=k.getTTMLRenderingDiv()?v(k.getElement(),e,r,n):[{start:e,end:r,data:n.getDisplayText(),styles:{}}])&&w.addCaptions(t,0,a)}},X=O.getSamplesInfo(e).sequenceNumber;if(!z[0]&&!z[1]){var Z=void 0,J=void 0;for(s=0;s<q.length;s++){if("CC1"===q[s].id?J=w.getTrackIdxForId("CC1"):"CC3"===q[s].id&&(J=w.getTrackIdxForId("CC3")),-1===J)return void E("CEA-608: data before track is ready.");Z=Q(0,J),z[s]=new h.default.Cea608Parser(s,{newCue:Z},null)}}if(Y&&-1==V.indexOf(X)){if(null!==G&&X!==G+1)for(s=0;s<z.length;s++)z[s]&&z[s].reset();for(var $=t(e),ee=0;ee<z.length;ee++){var te=$.fields[ee],re=z[ee];if(re){for(s=0;s<te.length;s++)re.addData(te[s][0]/Y,te[s][1]);$.endTime&&re.cueSplitAtTime($.endTime/Y)}}G=X,V.push(X)}}}else E("No text type defined")},abort:function(){w.deleteAllTextTracks(),A=!1,j=null,O=null,P=null,w=null,N=!1,x=null,L=!1,F=NaN,B=[],k=null,U=null,T=!1,q=null},getAllTracksAreDisabled:function(){return A},setTextTrack:function(){for(var e=k.getElement().textTracks,t=e.length,r=t-q.length,n=w.getCurrentTrackIdx(),a=0;t>a;a++){var i=e[a];if(A="showing"!==i.mode,"showing"===i.mode){if(n!==a&&(w.setCurrentTrackIdx(a),w.addCaptions(a,0,null),N&&r>a)){var o=C.getCurrentTrackFor("fragmentedText",U.getActiveStreamInfo()),s=B[a];s!==o&&(x.abortRequests(),w.deleteTrackCues(o),C.setTrack(s),H=a)}break}}A&&w.setCurrentTrackIdx(-1)},setConfig:function(e){e&&(e.errHandler&&(R=e.errHandler),e.adapter&&e.adapter,e.dashManifestModel&&(I=e.dashManifestModel),e.mediaController&&(C=e.mediaController),e.videoModel&&(k=e.videoModel),e.streamController&&(U=e.streamController),e.textTracks&&(w=e.textTracks),e.VTTParser&&(D=e.VTTParser),e.TTMLParser&&(b=e.TTMLParser))},addEmbeddedTrack:function(t){T||e(),"CC1"===t.id||"CC3"===t.id?q.push(t):E("Warning: Embedded track "+t.id+" not supported!")},resetEmbedded:function(){T=!1,q=[],z=[null,null],V=[],G=null}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./vo/TextTrackInfo.js")),o=n(e("../dash/utils/FragmentedTextBoxParser.js")),s=n(e("./utils/BoxParser.js")),u=n(e("./utils/CustomTimeRanges.js")),l=n(e("../core/FactoryMaker.js")),d=n(e("../core/Debug.js")),f=n(e("./models/VideoModel.js")),c=n(e("./TextTracks.js")),g=n(e("codem-isoboxer")),h=n(e("../../externals/cea608-parser.js"));a.__dashjs_factory_name="TextSourceBuffer",r.default=l.default.getSingletonFactory(a),t.exports=r.default},{"../../externals/cea608-parser.js":2,"../core/Debug.js":7,"../core/FactoryMaker.js":9,"../dash/utils/FragmentedTextBoxParser.js":20,"./TextTracks.js":45,"./models/VideoModel.js":93,"./utils/BoxParser.js":135,"./utils/CustomTimeRanges.js":137,"./vo/TextTrackInfo.js":158,"codem-isoboxer":6}],45:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e){var t=_[e].kind,r=void 0!==_[e].label?_[e].label:_[e].lang,n=_[e].lang,a=j?document.createElement("track"):y.addTextTrack(t,r,n);return j&&(a.kind=t,a.label=r,a.srclang=n),a}function t(e,t,r,n,a,i){var o=0,s=0;e/t>r/n?o=(s=t)/n*r:s=(o=e)/r*n;var u=0,l=0,d=0,f=0;return o/s>a?(f=s,u=(e-(d=s/(1/a)))/2,l=0):(d=o,u=0,l=(t-(f=o/a))/2),i?{x:u+.1*d,y:l+.1*f,w:.8*d,h:.8*f}:{x:u,y:l,w:d,h:f}}function r(){var e=this.getCurrentTextTrack();if(e&&"html"===e.renderingType){var r=1;e.cellResolution&&(r=e.cellResolution[0]/e.cellResolution[1]);var n=!1;e.isFromCEA608&&(r=3.5/3,n=!0);var a=t.call(this,y.clientWidth,y.clientHeight,y.videoWidth,y.videoHeight,r,n),i=a.w,o=a.h;if(i!=R||o!=I){M=a.x,S=a.y,R=i,I=o,C.style.left=M+"px",C.style.top=S+"px",C.style.width=R+"px",C.style.height=I+"px";for(var s=0;e.activeCues&&s<e.activeCues.length;++s){var u=e.activeCues[s];u.scaleCue(u)}D&&document[D]||b?C.style.zIndex=O:C.style.zIndex=null}}}function n(e){var t,r,n,a=I,i=[R/e.cellResolution[0],a/e.cellResolution[1]];if(e.linePadding)for(t in e.linePadding)if(e.linePadding.hasOwnProperty(t)){r=(e.linePadding[t]*i[0]).toString();for(var o=document.getElementsByClassName("spanPadding"),s=0;s<o.length;s++)o[s].style.cssText=o[s].style.cssText.replace(/(padding-left\s*:\s*)[\d.,]+(?=\s*px)/gi,"$1"+r),o[s].style.cssText=o[s].style.cssText.replace(/(padding-right\s*:\s*)[\d.,]+(?=\s*px)/gi,"$1"+r)}if(e.fontSize)for(t in e.fontSize)if(e.fontSize.hasOwnProperty(t)){r=(e.fontSize[t]/100*i[1]).toString(),n="defaultFontSize"!==t?document.getElementsByClassName(t):document.getElementsByClassName("paragraph");for(var u=0;u<n.length;u++)n[u].style.cssText=n[u].style.cssText.replace(/(font-size\s*:\s*)[\d.,]+(?=\s*px)/gi,"$1"+r)}if(e.lineHeight)for(t in e.lineHeight)if(e.lineHeight.hasOwnProperty(t)){r=(e.lineHeight[t]/100*i[1]).toString(),n=document.getElementsByClassName(t);for(var l=0;l<n.length;l++)n[l].style.cssText=n[l].style.cssText.replace(/(line-height\s*:\s*)[\d.,]+(?=\s*px)/gi,"$1"+r)}}function a(e){T=e,c.call(this),e>=0&&"html"===y.textTracks[e].renderingType?d.call(this):f.call(this)}function s(e){return y.textTracks[e]}function l(e){if(e.cues){for(var t=e.cues,r=t.length-1;r>=0;r--)e.removeCue(t[r]);e.mode="disabled"}}function d(){if(j){var e=document.getElementById("native-cue-style");if(!e){(e=document.createElement("style")).id="native-cue-style",document.head.appendChild(e);var t=e.sheet;y.id?t.insertRule("#"+y.id+"::cue {background: transparent}",0):0!==y.classList.length?t.insertRule("."+y.className+"::cue {background: transparent}",0):t.insertRule("video::cue {background: transparent}",0)}}}function f(){if(j){var e=document.getElementById("native-cue-style");e&&document.head.removeChild(e)}}function c(){if(C)for(;C.firstChild;)C.removeChild(C.firstChild)}var g=this.context,h=(0,i.default)(g).getInstance(),p=(0,u.default)(g).getInstance().log,m=void 0,v=void 0,y=void 0,_=void 0,E=void 0,T=void 0,M=void 0,S=void 0,R=void 0,I=void 0,C=void 0,A=void 0,j=void 0,D=void 0,b=void 0,O=void 0;return{initialize:function(){m=window.VTTCue||window.TextTrackCue,_=[],E=[],T=-1,M=0,S=0,R=0,I=0,C=null,A=null,b=!1,O=2147483647,j=!!navigator.userAgent.match(/Chrome/)&&!navigator.userAgent.match(/Edge/),void 0!==document.fullscreenElement?D="fullscreenElement":void 0!==document.webkitIsFullScreen?D="webkitIsFullScreen":document.msFullscreenElement?D="msFullscreenElement":document.mozFullScreen&&(D="mozFullScreen")},displayCConTop:function(e){b=e,C&&!document[D]&&(C.style.zIndex=e?O:null)},addTextTrack:function(t,r){if(_.length!==r){if(_.push(t),void 0===y&&(y=t.video),_.length===r){_.sort(function(e,t){return e.index-t.index}),C=v.getTTMLRenderingDiv();for(var n=-1,i=0;i<_.length;i++){var s=e.call(this,i);E.push(s),_[i].defaultTrack&&(s.default=!0,n=i),j&&y.appendChild(s);var u=y.textTracks[i];u.nonAddedCues=[],C&&(_[i].isTTML||_[i].isEmbedded)?u.renderingType="html":u.renderingType="default",this.addCaptions(i,0,_[i].captionData),h.trigger(o.default.TEXT_TRACK_ADDED)}a.call(this,n),n>=0&&(y.textTracks[n].mode="showing",this.addCaptions(n,0,null)),h.trigger(o.default.TEXT_TRACKS_ADDED,{index:T,tracks:_})}}else p("Trying to add too many tracks.")},addCaptions:function(e,t,a){var i=e>=0?y.textTracks[e]:null,o=this;if(i){if("showing"!==i.mode)return void(a&&a.length>0&&(i.nonAddedCues=i.nonAddedCues.concat(a)));if(a||(a=i.nonAddedCues,i.nonAddedCues=[]),a&&0!==a.length)for(var s in a){var u,l=a[s];i.cellResolution=l.cellResolution,i.isFromCEA608=l.isFromCEA608,A||"html"!=l.type||(A=setInterval(r.bind(this),500)),"image"==l.type?((u=new m(l.start-t,l.end-t,"")).image=l.data,u.id=l.id,u.size=0,u.type="image",u.onenter=function(){var e=new Image;e.id="ttmlImage_"+this.id,e.src=this.image,e.className="cue-image",C?C.appendChild(e):y.parentNode.appendChild(e)},u.onexit=function(){var e,t,r;for(r=(e=C||y.parentNode).childNodes,t=0;t<r.length;t++)r[t].id=="ttmlImage_"+this.id&&e.removeChild(r[t])}):"html"===l.type?((u=new m(l.start-t,l.end-t,"")).cueHTMLElement=l.cueHTMLElement,u.regions=l.regions,u.regionID=l.regionID,u.cueID=l.cueID,u.videoWidth=l.videoWidth,u.videoHeight=l.videoHeight,u.cellResolution=l.cellResolution,u.fontSize=l.fontSize,u.lineHeight=l.lineHeight,u.linePadding=l.linePadding,u.scaleCue=n.bind(o),C.style.left=M+"px",C.style.top=S+"px",C.style.width=R+"px",C.style.height=I+"px",u.onenter=function(){"showing"==i.mode&&(C.appendChild(this.cueHTMLElement),n.call(o,this))},u.onexit=function(){for(var e=C.childNodes,t=0;t<e.length;++t)e[t].id=="subtitle_"+this.cueID&&C.removeChild(e[t])}):(u=new m(l.start-t,l.end-t,l.data),l.styles&&(void 0!==l.styles.align&&u.hasOwnProperty("align")&&(u.align=l.styles.align),void 0!==l.styles.line&&u.hasOwnProperty("line")&&(u.line=l.styles.line),void 0!==l.styles.position&&u.hasOwnProperty("position")&&(u.position=l.styles.position),void 0!==l.styles.size&&u.hasOwnProperty("size")&&(u.size=l.styles.size))),i.addCue(u)}}},getTextTrack:s,getCurrentTextTrack:function(){return T>=0?y.textTracks[T]:null},getCurrentTrackIdx:function(){return T},setCurrentTrackIdx:a,getTrackIdxForId:function(e){for(var t=-1,r=0;r<y.textTracks.length;r++)if(y.textTracks[r].label===e){t=r;break}return t},deleteTrackCues:l,deleteAllTextTracks:function(){for(var e=E.length,t=0;e>t;t++)if(j)y.removeChild(E[t]);else{var r=s.call(this,t);r.nonAddedCues=[],l.call(this,r)}E=[],_=[],A&&(clearInterval(A),A=null),c.call(this)},deleteTextTrack:function(e){y.removeChild(E[e]),E.splice(e,1)},setConfig:function(e){e&&e.videoModel&&(v=e.videoModel)}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../core/EventBus.js")),o=n(e("../core/events/Events.js")),s=n(e("../core/FactoryMaker.js")),u=n(e("../core/Debug.js"));a.__dashjs_factory_name="TextTracks",r.default=s.default.getSingletonFactory(a),t.exports=r.default},{"../core/Debug.js":7,"../core/EventBus.js":8,"../core/FactoryMaker.js":9,"../core/events/Events.js":11}],46:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e){var t=function(e){var t=e.streamId,r=e.mediaType;return f[t]?f[t][r]:null}(e),n=e.segmentType,o=e.appended,s=e.removeOrigin,u=e.limit||Number.POSITIVE_INFINITY,l=(0,i.default)(a).getInstance(),d=0,c=[];return t?(delete e.streamId,delete e.mediaType,delete e.segmentType,delete e.removeOrigin,delete e.limit,delete e.appended,c=(o?t.appended:n?t[n]:[]).filter(function(r,n,a){if(d>=u)return!1;for(var i in e){if("mediaInfo"===i)return l.isTracksEqual(r[i],e[i]);if(e.hasOwnProperty(i)&&r[i]!=e[i])return!1}return s&&(t.calculatedBufferedRanges.remove(r.start,r.end),a.splice(n,1)),d++,!0}),e.forRange&&(c=r(c,e.forRange,!1)),c):c}function t(e,t){e.sort(function(e,r){return e[t]<r[t]?-1:e[t]>r[t]?1:0})}function r(e,t,r){var n,a,i,o,s=t.start,u=t.end,l=[];return e.forEach(function(e){n=e.bufferedRange.start,a=e.bufferedRange.end,o=a>s&&u>=a,((i=n>=s&&u>n)||o)&&(l.push(e),r&&(e.bufferedRange.start=i?n:s,e.bufferedRange.end=o?a:u))}),l}var n,a=this.context,d=(0,u.default)(a).getInstance(),f=void 0,c=void 0;return n={append:function(e){var r=e.streamId,n=e.mediaInfo.type,i=e.segmentType,u=e.start,c=e.end;f[r]=f[r]||function(){var e={};return e.audio={calculatedBufferedRanges:(0,o.default)(a).create(),actualBufferedRanges:(0,o.default)(a).create(),appended:[]},e.audio[s.default.MEDIA_SEGMENT_TYPE]=[],e.audio[s.default.INIT_SEGMENT_TYPE]=[],e.video={calculatedBufferedRanges:(0,o.default)(a).create(),actualBufferedRanges:(0,o.default)(a).create(),appended:[]},e.video[s.default.MEDIA_SEGMENT_TYPE]=[],e.video[s.default.INIT_SEGMENT_TYPE]=[],e.fragmentedText={calculatedBufferedRanges:(0,o.default)(a).create(),actualBufferedRanges:(0,o.default)(a).create(),appended:[]},e.fragmentedText[s.default.MEDIA_SEGMENT_TYPE]=[],e.fragmentedText[s.default.INIT_SEGMENT_TYPE]=[],e}(),f[r][n][i].push(e),t(f[r][n][i],"index"),isNaN(u)||isNaN(c)||(f[r][n].calculatedBufferedRanges.add(u,c),d.trigger(l.default.CHUNK_APPENDED,{chunk:e,sender:this}))},extract:function(t){return t.removeOrigin=!0,e(t)},getChunks:e,storeAppendedChunk:function(r,n){if(r&&n){var a,i,o=r.streamId,s=r.mediaInfo.type,u=f[o][s].actualBufferedRanges,l=e({streamId:o,mediaType:s,appended:!0,start:r.start})[0];if(l?(i=f[o][s].appended.indexOf(l),f[o][s].appended[i]=r):f[o][s].appended.push(r),t(f[o][s].appended,"start"),!(a=c.getRangeDifference(u,n)))return void(r.bufferedRange=l?l.bufferedRange:{start:r.start,end:r.end});r.bufferedRange=a,u.add(a.start,a.end),l&&(r.bufferedRange.start=Math.min(l.bufferedRange.start,a.start),r.bufferedRange.end=Math.max(l.bufferedRange.end,a.end))}},updateBufferedRanges:function(t,n){if(t){var i,s,u=t.streamId,l=t.mediaType,d=e({streamId:u,mediaType:l,appended:!0}),c=[];if(f[u][l].actualBufferedRanges=(0,o.default)(a).create(),!n||0===n.length)return void(f[u][l].appended=[]);for(var g=0,h=n.length;h>g;g++)i=n.start(g),s=n.end(g),f[u][l].actualBufferedRanges.add(i,s),c=c.concat(r(d,{start:i,end:s},!0));f[u][l].appended=c}},getTotalBufferLevel:function(e){var t=e.type,r=0;for(var n in f)f.hasOwnProperty(n)&&(r+=c.getTotalBufferedTime({buffered:f[n][t].calculatedBufferedRanges}));return r},setConfig:function(e){e&&e.sourceBufferController&&(c=e.sourceBufferController)},reset:function(){f={}}},f={},n}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./controllers/MediaController.js")),o=n(e("./utils/CustomTimeRanges.js")),s=n(e("./vo/metrics/HTTPRequest.js")),u=n(e("../core/EventBus.js")),l=n(e("../core/events/Events.js")),d=n(e("../core/FactoryMaker.js"));a.__dashjs_factory_name="VirtualBuffer",r.default=d.default.getSingletonFactory(a),t.exports=r.default},{"../core/EventBus.js":8,"../core/FactoryMaker.js":9,"../core/events/Events.js":11,"./controllers/MediaController.js":55,"./utils/CustomTimeRanges.js":137,"./vo/metrics/HTTPRequest.js":166}],47:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i(e){function t(e,r){var n=e.request,a=new XMLHttpRequest,o=[],u=!0,p=!0,m=new Date,v=m,y=0,_=function(e){p=!1,n.requestStartDate=m,n.requestEndDate=new Date,n.firstByteDate=n.firstByteDate||m,n.checkExistenceOnly||l.addHttpRequest(n.mediaType,null,n.type,n.url,a.responseURL||null,n.serviceLocation||null,n.range||null,n.requestStartDate,n.firstByteDate,n.requestEndDate,a.status,n.duration,a.getAllResponseHeaders(),e?o:null)},E=function(){-1!==f.indexOf(a)&&(f.splice(f.indexOf(a),1),p&&(_(!1),r>0?(r--,g.push(setTimeout(function(){t(e,r)},i.getRetryIntervalForType(n.type)))):(s.downloadError(h[n.type],n.url,n),e.error&&e.error(n,"error",a.statusText),e.complete&&e.complete(n,a.statusText))))};try{var T=d.modifyRequestURL(n.url),M=n.checkExistenceOnly?"HEAD":"GET";a.open(M,T,!0),n.responseType&&(a.responseType=n.responseType),n.range&&a.setRequestHeader("Range","bytes="+n.range),n.requestStartDate||(n.requestStartDate=m),(a=d.modifyRequestHeader(a)).onload=function(){a.status>=200&&a.status<=299&&(_(!0),e.success&&e.success(a.response,a.statusText,a),e.complete&&e.complete(n,a.statusText))},a.onloadend=E,a.onerror=E,a.onprogress=function(t){var r=new Date;u&&(u=!1,(!t.lengthComputable||t.lengthComputable&&t.total!==t.loaded)&&(n.firstByteDate=r)),t.lengthComputable&&(n.bytesLoaded=t.loaded,n.bytesTotal=t.total),o.push({s:v,d:r.getTime()-v.getTime(),b:[t.loaded?t.loaded-y:0]}),v=r,y=t.loaded,e.progress&&e.progress()};var S=(new Date).getTime();isNaN(n.delayLoadingTime)||S>=n.delayLoadingTime?(f.push(a),a.send()):function(){var e={xhr:a};c.push(e),e.delayTimeout=setTimeout(function(){if(-1!==c.indexOf(e)){c.splice(c.indexOf(e),1);try{f.push(e.xhr),e.xhr.send()}catch(t){e.xhr.onerror()}}},n.delayLoadingTime-S)}()}catch(e){a.onerror()}}var r,n=this.context,i=(0,u.default)(n).getInstance(),s=e.errHandler,l=e.metricsModel,d=e.requestModifier,f=void 0,c=void 0,g=void 0,h=void 0;return r={load:function(e){e.request&&t(e,i.getRetryAttemptsForType(e.request.type))},abort:function(){g.forEach(function(e){return clearTimeout(e)}),g=[],c.forEach(function(e){return clearTimeout(e.delayTimeout)}),c=[],f.forEach(function(e){e.onloadend=e.onerror=void 0,e.abort()}),f=[]}},function(){var e;f=[],c=[],g=[],a(e={},o.default.MPD_TYPE,s.DOWNLOAD_ERROR_ID_MANIFEST),a(e,o.default.XLINK_EXPANSION_TYPE,s.DOWNLOAD_ERROR_ID_XLINK),a(e,o.default.INIT_SEGMENT_TYPE,s.DOWNLOAD_ERROR_ID_CONTENT),a(e,o.default.MEDIA_SEGMENT_TYPE,s.DOWNLOAD_ERROR_ID_CONTENT),a(e,o.default.INDEX_SEGMENT_TYPE,s.DOWNLOAD_ERROR_ID_CONTENT),a(e,o.default.BITSTREAM_SWITCHING_SEGMENT_TYPE,s.DOWNLOAD_ERROR_ID_CONTENT),a(e,o.default.OTHER_TYPE,s.DOWNLOAD_ERROR_ID_CONTENT),h=e}(),r}Object.defineProperty(r,"__esModule",{value:!0});var o=n(e("./vo/metrics/HTTPRequest.js")),s=n(e("../core/FactoryMaker.js")),u=n(e("./models/MediaPlayerModel.js"));i.__dashjs_factory_name="XHRLoader";var l=s.default.getClassFactory(i);r.default=l,t.exports=r.default},{"../core/FactoryMaker.js":9,"./models/MediaPlayerModel.js":90,"./vo/metrics/HTTPRequest.js":166}],48:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t=this.context,r=(0,l.default)(t).getInstance(),n=(0,o.default)(t).create({errHandler:e.errHandler,metricsModel:e.metricsModel,requestModifier:e.requestModifier});return{load:function(e,t,a){var o=function(n,o){t.resolved=!0,t.resolvedContent=n||null,r.trigger(d.default.XLINK_ELEMENT_LOADED,{element:t,resolveObject:a,error:n||o?null:new i.default(c,"Failed loading Xlink element: "+e)})};if("urn:mpeg:dash:resolve-to-zero:2013"===e)o(null,!0);else{var l=new u.default(e,s.default.XLINK_TYPE);n.load({request:l,success:function(e){o(e)},error:function(){o(null)}})}},reset:function(){n&&(n.abort(),n=null)}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./vo/Error.js")),o=n(e("./XHRLoader.js")),s=n(e("./vo/metrics/HTTPRequest.js")),u=n(e("./vo/TextRequest.js")),l=n(e("../core/EventBus.js")),d=n(e("../core/events/Events.js")),f=n(e("../core/FactoryMaker.js")),c=1;a.__dashjs_factory_name="XlinkLoader";var g=f.default.getClassFactory(a);g.XLINK_LOADER_ERROR_LOADING_FAILURE=c,r.default=g,t.exports=r.default},{"../core/EventBus.js":8,"../core/FactoryMaker.js":9,"../core/events/Events.js":11,"./XHRLoader.js":47,"./vo/Error.js":149,"./vo/TextRequest.js":157,"./vo/metrics/HTTPRequest.js":166}],49:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){N={video:!0,audio:!0},x={},L={},F={},B={},k={},U={},H={},K={},Y=!1,Q=(0,s.default)(D).getInstance(),W=(0,l.default)(D).getInstance(),G=(0,h.default)(D).getInstance(),V=(0,p.default)(D).getInstance(),z=(0,m.default)(D).getInstance()}function t(e,t){var a;return x[t]=x[t]||{},x[t].hasOwnProperty(e)||(x[t][e]=0),a=function(e,t){var n=r(t);if(isNaN(n)||!K[t])return e;var a=M(K[t].getMediaInfo(),n);return Math.min(e,a)}(x[t][e],e),a=function(e,t,r){var a=n(t);return isNaN(a)||a>=1||0>a?e:Math.min(e,Math.round(r*a))}(a,e,x[t][e]),function(e,t){if("video"!==t||!Y||!K[t])return e;var r=z.getElement(),n=r.clientWidth,a=r.clientHeight,i=G.getValue(),o=V.getAdaptationForType(i,0,t).Representation,s=e;if(n>0&&a>0){for(;s>0&&o[s]&&n<o[s].width&&n-o[s-1].width<o[s].width-n;)s-=1;o.length-2>=s&&o[s].width===o[s+1].width&&(s=Math.min(e,s+1))}return s}(a,e)}function r(e){return B.hasOwnProperty("max")&&B.max.hasOwnProperty(e)?B.max[e]:NaN}function n(e){return k.hasOwnProperty("max")&&k.max.hasOwnProperty(e)?k.max[e]:1}function a(e){return N[e]}function g(e,r,n){var a=r.id,i=R(e,r);if(null===n||isNaN(n)||n%1!=0)throw"argument is not an integer";n!==i&&n>=0&&n<=t(e,a)&&(I(e,a,n),b.trigger(c.default.QUALITY_CHANGED,{mediaType:e,streamInfo:r,oldQuality:i,newQuality:n}))}function T(e,t){H[e].state=t}function M(e,t){var r=S(e);if(!r||0===r.length)return-1;for(var n=r.length-1;n>=0;n--)if(1e3*t>=r[n].bitrate)return n;return 0}function S(e){if(!e||!e.bitrateList)return null;for(var t,r=e.bitrateList,n=e.type,a=[],i=0,s=r.length;s>i;i++)(t=new o.default).mediaType=n,t.qualityIndex=i,t.bitrate=r[i].bandwidth,t.width=r[i].width,t.height=r[i].height,a.push(t);return a}function R(e,t){var r=t.id;return L[r]=L[r]||{},L[r].hasOwnProperty(e)||(L[r][e]=0),L[r][e]}function I(e,t,r){L[t]=L[t]||{},L[t][e]=r}function C(e,t){return F[t]=F[t]||{},F[t].hasOwnProperty(e)||(F[t][e]=0),F[t][e]}function A(e){var t=e.request.mediaType;if(a(t)){var r=O.getRules(u.default.ABANDON_FRAGMENT_RULES),n=K[t].getScheduleController();if(!n)return;var o=n.getFragmentModel();P.applyRules(r,K[t],function(e){if(e.confidence===i.default.STRONG){var r=o.getRequests({state:d.default.FRAGMENT_MODEL_LOADING}),a=e.value;R(t,w.getActiveStreamInfo())>a&&(o.abortRequests(),T(t,v),g(t,w.getActiveStreamInfo(),a),n.replaceCanceledRequests(r),function(e){q=setTimeout(function(){T(e,y)},W.getAbandonLoadTimeout())}(t))}},e,function(e,t){return t})}}var j,D=this.context,b=(0,f.default)(D).getInstance(),O=void 0,P=void 0,w=void 0,N=void 0,x=void 0,L=void 0,F=void 0,B=void 0,k=void 0,U=void 0,K=void 0,H=void 0,q=void 0,Y=void 0,G=void 0,V=void 0,z=void 0,W=void 0,Q=void 0;return j={isPlayingAtTopQuality:function(e){var r=e.id,n=R("audio",e),a=R("video",e);return n===t("audio",r)&&a===t("video",r)},updateTopQualityIndex:function(e){var t=e.type,r=e.streamInfo.id,n=e.representationCount-1;return function(e,t,r){x[t]=x[t]||{},x[t][e]=r}(t,r,n),n},getAverageThroughput:function(e){return U[e]},getBitrateList:S,getQualityForBitrate:M,getMaxAllowedBitrateFor:r,setMaxAllowedBitrateFor:function(e,t){B.max=B.max||{},B.max[e]=t},getMaxAllowedRepresentationRatioFor:n,setMaxAllowedRepresentationRatioFor:function(e,t){k.max=k.max||{},k.max[e]=t},getInitialBitrateFor:function(e){var t=Q.getSavedBitrateSettings(e);if(!B.hasOwnProperty(e))if(k.hasOwnProperty(e)){var r=G.getValue(),n=V.getAdaptationForType(r,0,e).Representation;if(Array.isArray(n)){var a=Math.max(Math.round(n.length*k[e])-1,0);B[e]=n[a].bandwidth}else B[e]=0}else isNaN(t)?B[e]="video"===e?_:E:B[e]=t;return B[e]},setInitialBitrateFor:function(e,t){B[e]=t},getInitialRepresentationRatioFor:function(e){return k.hasOwnProperty(e)?k[e]:null},setInitialRepresentationRatioFor:function(e,t){k[e]=t},setAutoSwitchBitrateFor:function(e,t){N[e]=t},getAutoSwitchBitrateFor:a,setLimitBitrateByPortal:function(e){Y=e},getLimitBitrateByPortal:function(){return Y},getConfidenceFor:C,getQualityFor:R,getAbandonmentStateFor:function(e){return H[e].state},setAbandonmentStateFor:T,setPlaybackQuality:g,getPlaybackQuality:function(e,r){var n,o,s,l,d=e.getType(),f=e.getStreamInfo(),g=f.id;n=R(d,f),l=C(d,g),a(d)?(s=O.getRules(u.default.QUALITY_SWITCH_RULES),P.applyRules(s,e,function(a){var i=t(d,g);n=a.value,l=a.confidence,0>n&&(n=0),n>i&&(n=i),o=R(d,f),n!==o&&(H[d].state===y||n>o)&&(I(d,g,n),function(e,t,r){F[t]=F[t]||{},F[t][e]=r}(d,g,l),b.trigger(c.default.QUALITY_CHANGED,{mediaType:d,streamInfo:e.getStreamInfo(),oldQuality:o,newQuality:n})),r&&r()},n,function(e,t){return e=e===i.default.NO_CHANGE?0:e,Math.max(e,t)})):r&&r()},setAverageThroughput:function(e,t){U[e]=t},getTopQualityIndexFor:t,initialize:function(e,t){K[e]=t,H[e]=H[e]||{},H[e].state=y,b.on(c.default.LOADING_PROGRESS,A,this)},setConfig:function(e){e&&(e.abrRulesCollection&&(O=e.abrRulesCollection),e.rulesController&&(P=e.rulesController),e.streamController&&(w=e.streamController))},reset:function(){b.off(c.default.LOADING_PROGRESS,A,this),clearTimeout(q),q=null,e()}},e(),j}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../rules/SwitchRequest")),o=n(e("../vo/BitrateInfo.js")),s=n(e("../utils/DOMStorage.js")),u=n(e("../rules/abr/ABRRulesCollection.js")),l=n(e("../models/MediaPlayerModel.js")),d=n(e("../models/FragmentModel.js")),f=n(e("../../core/EventBus.js")),c=n(e("../../core/events/Events.js")),g=n(e("../../core/FactoryMaker.js")),h=n(e("../models/ManifestModel.js")),p=n(e("../../dash/models/DashManifestModel.js")),m=n(e("../models/VideoModel.js")),v="abandonload",y="allowload",_=1e3,E=100;a.__dashjs_factory_name="AbrController";var T=g.default.getSingletonFactory(a);T.ABANDON_LOAD=v,r.default=T,t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../../dash/models/DashManifestModel.js":19,"../models/FragmentModel.js":88,"../models/ManifestModel.js":89,"../models/MediaPlayerModel.js":90,"../models/VideoModel.js":93,"../rules/SwitchRequest":119,"../rules/abr/ABRRulesCollection.js":120,"../utils/DOMStorage.js":138,"../vo/BitrateInfo.js":147}],50:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e,t=this.context,r=(0,d.default)(t).getInstance(),n=(0,s.default)(t).getInstance(),a=void 0,l=void 0;return e={reset:function(){a.reset(),l.reset()},initialize:function(e){!function(e){a.update(e),l.chooseSelectorFromManifest(e)}(e)},resolve:function(e){var t=a.getForPath(e).reduce(function(e,t){var r=l.select(t);return r&&(n.isRelative(r.url)?e.url+=r.url:(e.url=r.url,e.serviceLocation=r.serviceLocation)),e},new u.default);return n.isRelative(t.url)?void 0:t}},a=(0,i.default)(t).create(),l=(0,o.default)(t).create(),r.on(f.default.SERVICE_LOCATION_BLACKLIST_CHANGED,function(e){a.invalidateSelectedIndexes(e.entry)},e),e}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../models/BaseURLTreeModel.js")),o=n(e("../utils/BaseURLSelector.js")),s=n(e("../utils/URLUtils.js")),u=n(e("../../dash/vo/BaseURL.js")),l=n(e("../../core/FactoryMaker.js")),d=n(e("../../core/EventBus.js")),f=n(e("../../core/events/Events.js"));a.__dashjs_factory_name="BaseURLController",r.default=l.default.getSingletonFactory(a),t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../../dash/vo/BaseURL.js":28,"../models/BaseURLTreeModel.js":87,"../utils/BaseURLSelector.js":134,"../utils/URLUtils.js":145}],51:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e){-1===n.indexOf(e)&&(n.push(e),a.trigger(i,{entry:e}))}function r(e){e.error&&t(e.request.serviceLocation)}var n=[],a=(0,o.default)(this.context).getInstance(),i=e.updateEventName,s=e.loadFailedEventName;return function(){s&&a.on(s,r,this)}(),{add:t,contains:function(e){return!!(n.length&&e&&e.length)&&-1!==n.indexOf(e)},reset:function(){n=[]}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/FactoryMaker.js")),o=n(e("../../core/EventBus.js"));a.__dashjs_factory_name="BlackListController",r.default=i.default.getClassFactory(a),t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9}],52:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e){var t;e.fragmentModel===Me.getFragmentModel()&&(Y("Initialization finished loading"),t=e.chunk,ee.append(t),a(N(),ne))}function r(e){if(e.fragmentModel===Me.getFragmentModel()){var t,r=e.chunk,a=r.bytes,o=r.quality,s=r.index,u=Me.getFragmentModel().getRequests({state:i.default.FRAGMENT_MODEL_EXECUTED,quality:o,index:s})[0],l=Me.getRepresentationInfoForQuality(o),d=z.getValue(),f=$.getEventsFor(d,l.mediaInfo,Me),c=$.getEventsFor(d,l,Me);(f.length>0||c.length>0)&&(t=function(e,t,r,n){var a,i,o,s=Math.max(isNaN(t.startTime)?0:t.startTime,0),u=[],l=[];Ee=!1,o=r.concat(n);for(var d=0;d<o.length;d++)u[o[d].schemeIdUri]=o[d];for(var f=0,c=(a=(0,p.default)(q).getInstance().parse(e).getBoxes("emsg")).length;c>f;f++)(i=$.getEvent(a[f],u,s))&&l.push(i);return l}(a,u,f,c),Me.getEventController().addInbandEvents(t)),r.bytes=function(e){if(!Ee)return e;for(var t,r,n=e.length,a=Math.pow(256,2),i=Math.pow(256,3),o=new Uint8Array(e.length),s=0,u=0;n>s;){if(t=String.fromCharCode(e[s+4],e[s+5],e[s+6],e[s+7]),r=e[s]*i+e[s+1]*a+256*e[s+2]+1*e[s+3],"emsg"!=t)for(var l=s;s+r>l;l++)o[u]=e[l],u++;s+=r}return o.subarray(0,u)}(a),ee.append(r),n()}}function n(){if(ge&&!ye&&D()){var e,t=N();if(ve)e=ve;else{if(!(e=ee.extract({streamId:t,mediaType:ce,segmentType:s.default.MEDIA_SEGMENT_TYPE,limit:1})[0]))return;ve=e}e.quality===ae?(ve=!1,m(e)):a(t,ve.quality)}}function a(e,t){var r={streamId:e,mediaType:ce,segmentType:s.default.INIT_SEGMENT_TYPE,quality:t},n=ee.getChunks(r)[0];if(n){if(!ge)return;m(n)}else G.trigger(h.default.INIT_REQUESTED,{sender:re,requiredQuality:t})}function m(e){ye=!0,pe=e,W.append(ge,e),"video"===e.mediaInfo.type&&e.mediaInfo.embeddedCaptions&&te.append(e.bytes,e)}function T(e){if(ge===e.buffer){var t,r,a;if(R(),ie&&Me.getStreamInfo().isLast&&X.signalEndOfStream(le),e.error)return e.error.code===u.default.QUOTA_EXCEEDED_ERROR_CODE&&(ee.append(pe),ue=.8*W.getTotalBufferedTime(ge),G.trigger(h.default.QUOTA_EXCEEDED,{sender:re,criticalBufferLevel:ue}),O(b())),void(ye=!1);if(D()||(G.trigger(h.default.QUOTA_EXCEEDED,{sender:re,criticalBufferLevel:ue}),O(b())),(t=W.getAllRanges(ge))&&t.length>0)for(r=0,a=t.length;a>r;r++)Y("Buffered Range: "+t.start(r)+" - "+t.end(r));ye=!1,isNaN(pe.index)?(ae=pe.quality,Me.isDynamic()||n()):(ee.storeAppendedChunk(pe,ge),function(){var e,t=ee.getChunks({streamId:N(),mediaType:ce,segmentType:s.default.MEDIA_SEGMENT_TYPE,appended:!0}),r=(0,c.default)(q),n=r.create(),a=r.create(),i=Te.getTime(),o=2*Me.getCurrentRepresentationInfo().fragmentDuration;if(t.forEach(function(e){(J.isCurrentTrack(e.mediaInfo)?a:n).add(e.bufferedRange.start,e.bufferedRange.end)}),0!==n.length&&0!==a.length&&!(o>W.getBufferLength({buffered:a},i)))for(var u=0,l=n.length;l>u;u++)e={start:n.start(u),end:n.end(u)},(J.getSwitchMode(ce)===f.default.TRACK_SWITCH_MODE_ALWAYS_REPLACE||e.start>i)&&O(e)}(),de=Math.max(pe.index,de),A()),G.trigger(h.default.BYTES_APPENDED,{sender:re,quality:pe.quality,startTime:pe.start,index:pe.index,bufferedRanges:t})}}function M(e){var t=e.newQuality;ne!==t&&ce===e.mediaType&&Me.getStreamInfo().id===e.streamInfo.id&&(w(Me.getRepresentationInfoForQuality(t).MSETimeOffset),ne=t)}function S(){ye=!1,R()}function R(){I(),C()}function I(){var e=Te.getTime();oe=W.getBufferLength(ge,e),G.trigger(h.default.BUFFER_LEVEL_UPDATED,{sender:re,bufferLevel:oe}),j()}function C(){Me.getStreamInfo().id===Z.getActiveStreamInfo().id&&(V.addBufferState(ce,he,se),V.addBufferLevel(ce,new Date,1e3*oe))}function A(){de===fe-1&&!ie&&(ie=!0,G.trigger(h.default.BUFFERING_COMPLETED,{sender:re,streamInfo:Me.getStreamInfo()}))}function j(){!function(e){if(!(he===e||"fragmentedText"===ce&&te.getAllTracksAreDisabled())){he=e,C(),G.trigger(h.default.BUFFER_LEVEL_STATE_CHANGED,{sender:re,state:e,mediaType:ce,streamInfo:Me.getStreamInfo()});var t=e===y?h.default.BUFFER_LOADED:h.default.BUFFER_EMPTY;G.trigger(t,{mediaType:ce}),Y(e===y?"Got enough buffer to start.":"Waiting for more buffer before starting playback.")}}(E>oe&&!ie?_:y)}function D(){var e=W.getTotalBufferedTime(ge);return ue>e}function b(){var e,t,r;return ge?(e=Te.getTime(),t=(r=Me.getFragmentModel().getRequests({state:i.default.FRAGMENT_MODEL_EXECUTED,time:e})[0])&&!isNaN(r.startTime)?r.startTime:Math.floor(e),null===W.getBufferRange(ge,e)&&ge.buffered.length>0&&(t=ge.buffered.end(ge.buffered.length-1)),{start:ge.buffered.start(0),end:t}):null}function O(e){if(e&&ge){var t=e.start,r=e.end;W.remove(ge,t,r,le)}}function P(e){ge===e.buffer&&(_e&&(_e=!1),ee.updateBufferedRanges({streamId:N(),mediaType:ce},W.getAllRanges(ge)),I(),G.trigger(h.default.BUFFER_CLEARED,{sender:re,from:e.from,to:e.to,hasEnoughSpaceToAppend:D()}),D()||null===Ie&&(Ie=setTimeout(function(){Ie=null,O(b())},1e3*Me.getStreamInfo().manifestInfo.minBufferTime)))}function w(e){ge&&ge.timestampOffset!==e&&!isNaN(e)&&(ge.timestampOffset=e)}function N(){return Me.getStreamInfo().id}function x(e){e.sender.getStreamProcessor()===Me&&(e.error||w(e.currentRepresentation.MSETimeOffset))}function L(e){e.fragmentModel===Me.getFragmentModel()&&(fe=e.request.index,A())}function F(e){e.sender===ee&&C()}function B(e){if(ge&&e.newMediaInfo.type===ce&&e.newMediaInfo.streamInfo.id===Me.getStreamInfo().id){var t=e.newMediaInfo.type,r=e.switchMode,n={start:0,end:Te.getTime()};if(ce===t)switch(r){case f.default.TRACK_SWITCH_MODE_ALWAYS_REPLACE:O(n);break;case f.default.TRACK_SWITCH_MODE_NEVER_REPLACE:break;default:Y("track switch mode is not supported: "+r)}}}function k(){++me*(Re.getWallclockTimeUpdateInterval()/1e3)>=Re.getBufferPruningInterval()&&!ye&&(me=0,function(){if("fragmentedText"!==ce){Y("try to prune buffer");var e=ge.buffered.length?ge.buffered.start(0):0,t=Te.getTime()-e-Re.getBufferToKeep();t>0&&(Y("pruning buffer: "+t+" seconds."),_e=!0,W.remove(ge,0,Math.round(e+t),le))}}())}function U(){j()}function K(e){ge=e}function H(e){le=e}var q=this.context,Y=(0,v.default)(q).getInstance().log,G=(0,g.default)(q).getInstance(),V=e.metricsModel,z=e.manifestModel,W=e.sourceBufferController,Q=e.errHandler,X=e.mediaSourceController,Z=e.streamController,J=e.mediaController,$=e.adapter,ee=e.virtualBuffer,te=e.textSourceBuffer,re=void 0,ne=void 0,ae=void 0,ie=void 0,oe=void 0,se=void 0,ue=void 0,le=void 0,de=void 0,fe=void 0,ce=void 0,ge=void 0,he=void 0,pe=void 0,me=void 0,ve=void 0,ye=void 0,_e=void 0,Ee=void 0,Te=void 0,Me=void 0,Se=void 0,Re=void 0,Ie=void 0;return re={initialize:function(e,n,a){ce=e,H(n),Me=a,Re=(0,o.default)(q).getInstance(),Te=(0,d.default)(q).getInstance(),Se=(0,l.default)(q).getInstance(),Me.getFragmentController(),Me.getScheduleController(),ne=Se.getQualityFor(ce,Me.getStreamInfo()),G.on(h.default.DATA_UPDATE_COMPLETED,x,this),G.on(h.default.INIT_FRAGMENT_LOADED,t,this),G.on(h.default.MEDIA_FRAGMENT_LOADED,r,this),G.on(h.default.QUALITY_CHANGED,M,this),G.on(h.default.STREAM_COMPLETED,L,this),G.on(h.default.PLAYBACK_PROGRESS,R,this),G.on(h.default.PLAYBACK_TIME_UPDATED,R,this),G.on(h.default.PLAYBACK_RATE_CHANGED,U,this),G.on(h.default.PLAYBACK_SEEKING,S,this),G.on(h.default.WALLCLOCK_TIME_UPDATED,k,this),G.on(h.default.CURRENT_TRACK_CHANGED,B,this),G.on(h.default.SOURCEBUFFER_APPEND_COMPLETED,T,this),G.on(h.default.SOURCEBUFFER_REMOVE_COMPLETED,P,this),G.on(h.default.CHUNK_APPENDED,F,this)},createBuffer:function(e){if(!e||!le||!Me)return null;var t=null;try{(t=W.createSourceBuffer(le,e))&&t.hasOwnProperty("initialize")&&t.initialize(ce,this)}catch(e){Q.mediaSourceError("Error creating "+ce+" source buffer.")}return K(t),w(Me.getRepresentationInfoForQuality(ne).MSETimeOffset),n(),t},getType:function(){return ce},getStreamProcessor:function(){return Me},setStreamProcessor:function(e){Me=e},getBuffer:function(){return ge},setBuffer:K,getBufferLevel:function(){return oe},getCriticalBufferLevel:function(){return ue},setMediaSource:H,getMediaSource:function(){return le},getIsBufferingCompleted:function(){return ie},getIsAppendingInProgress:function(){return ye},reset:function(e){G.off(h.default.DATA_UPDATE_COMPLETED,x,this),G.off(h.default.QUALITY_CHANGED,M,this),G.off(h.default.INIT_FRAGMENT_LOADED,t,this),G.off(h.default.MEDIA_FRAGMENT_LOADED,r,this),G.off(h.default.STREAM_COMPLETED,L,this),G.off(h.default.CURRENT_TRACK_CHANGED,B,this),G.off(h.default.PLAYBACK_PROGRESS,R,this),G.off(h.default.PLAYBACK_TIME_UPDATED,R,this),G.off(h.default.PLAYBACK_RATE_CHANGED,U,this),G.off(h.default.PLAYBACK_SEEKING,S,this),G.off(h.default.WALLCLOCK_TIME_UPDATED,k,this),G.off(h.default.SOURCEBUFFER_APPEND_COMPLETED,T,this),G.off(h.default.SOURCEBUFFER_REMOVE_COMPLETED,P,this),G.off(h.default.CHUNK_APPENDED,F,this),clearTimeout(Ie),Ie=null,ue=Number.POSITIVE_INFINITY,he=_,ae=-1,fe=-1,de=-1,ne=0,pe=null,ve=!1,ie=!1,ye=!1,_e=!1,Te=null,Me=null,Se=null,e||(W.abort(le,ge),W.removeSourceBuffer(le,ge)),ge=null}},ne=-1,ae=-1,ie=!1,oe=0,se=0,ue=Number.POSITIVE_INFINITY,de=-1,fe=-1,ge=null,he=_,me=0,ve=!1,ye=!1,_e=!1,Ee=!1,Ie=null,re}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../models/FragmentModel.js")),o=n(e("../models/MediaPlayerModel.js")),s=n(e("../vo/metrics/HTTPRequest.js")),u=n(e("./SourceBufferController.js")),l=n(e("./AbrController.js")),d=n(e("./PlaybackController.js")),f=n(e("./MediaController.js")),c=n(e("../utils/CustomTimeRanges.js")),g=n(e("../../core/EventBus.js")),h=n(e("../../core/events/Events.js")),p=n(e("../utils/BoxParser.js")),m=n(e("../../core/FactoryMaker.js")),v=n(e("../../core/Debug.js")),y="bufferLoaded",_="bufferStalled",E=.5;a.__dashjs_factory_name="BufferController";var T=m.default.getClassFactory(a);T.BUFFER_LOADED=y,T.BUFFER_EMPTY=_,r.default=T,t.exports=r.default},{"../../core/Debug.js":7,"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../models/FragmentModel.js":88,"../models/MediaPlayerModel.js":90,"../utils/BoxParser.js":135,"../utils/CustomTimeRanges.js":137,"../vo/metrics/HTTPRequest.js":166,"./AbrController.js":49,"./MediaController.js":55,"./PlaybackController.js":57,"./SourceBufferController.js":59}],53:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){null!==p&&T&&(clearInterval(p),p=null,T=!1)}function t(){n(g),n(c),function(){if(h)for(var e=E.getTime(),t=Object.keys(h),r=0;r<t.length;r++){var n=t[r],a=h[n];null!==a&&(a.duration+a.presentationTime)/a.eventStream.timescale<e&&(d("Remove Event "+n+" at time "+e),a=null,delete h[n])}}()}function r(){var e=y.getValue(),t=e.url;e.hasOwnProperty("Location")&&(t=e.Location),d("Refresh manifest @ "+t),_.getManifestLoader().load(t)}function n(e){var t,n=E.getTime();if(e)for(var i=Object.keys(e),s=0;s<i.length;s++){var u=i[s],l=e[u];void 0!==l&&(0===(t=l.presentationTime/l.eventStream.timescale)||n>=t&&t+v>n)&&(d("Start Event "+u+" at "+n),l.duration>0&&(h[u]=l),l.eventStream.schemeIdUri==a&&l.eventStream.value==o?r():f.trigger(l.eventStream.schemeIdUri,{event:l}),delete e[u])}}var a="urn:mpeg:dash:event:2012",o=1,l=this.context,d=(0,s.default)(l).getInstance().log,f=(0,u.default)(l).getInstance(),c=void 0,g=void 0,h=void 0,p=void 0,m=void 0,v=void 0,y=void 0,_=void 0,E=void 0,T=void 0;return{initialize:function(){T=!1,c={},g={},h={},p=null,v=(m=100)/1e3,E=(0,i.default)(l).getInstance()},addInlineEvents:function(e){if(c={},e)for(var t=0;t<e.length;t++){var r=e[t];c[r.id]=r,d("Add inline event with id "+r.id)}d("Added "+e.length+" inline events")},addInbandEvents:function(e){for(var t=0;t<e.length;t++){var r=e[t];r.id in g?d("Repeated event with id "+r.id):(g[r.id]=r,d("Add inband event with id "+r.id))}},clear:e,start:function(){d("Start Event Controller"),T||isNaN(m)||(T=!0,p=setInterval(t,m))},setConfig:function(e){e&&(e.manifestModel&&(y=e.manifestModel),e.manifestUpdater&&(_=e.manifestUpdater))},reset:function(){e(),c=null,g=null,h=null,E=null}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../controllers/PlaybackController.js")),o=n(e("../../core/FactoryMaker.js")),s=n(e("../../core/Debug.js")),u=n(e("../../core/EventBus.js"));a.__dashjs_factory_name="EventController",r.default=o.default.getSingletonFactory(a),t.exports=r.default},{"../../core/Debug.js":7,"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../controllers/PlaybackController.js":57}],54:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e){return e&&e.type&&e.type===i.default.INIT_SEGMENT_TYPE}function t(e){for(var t=p.length,r=0;t>r;r++)if(p[r].getScheduleController()==e)return p[r];return null}function r(e,t,r){var n=new o.default;return n.streamId=r,n.mediaInfo=t.mediaInfo,n.segmentType=t.type,n.start=t.startTime,n.duration=t.duration,n.end=n.start+n.duration,n.bytes=e,n.index=t.index,n.quality=t.quality,n}function n(n){var a=n.sender.getScheduleController();if(t(a)){var i,o=n.request,s=n.response,u=e(o),l=a.getStreamProcessor().getStreamInfo().id;if(!s)return void g("No "+o.mediaType+" bytes to push.");i=r(s,o,l),h.trigger(u?d.default.INIT_FRAGMENT_LOADED:d.default.MEDIA_FRAGMENT_LOADED,{chunk:i,fragmentModel:n.sender})}}var a,f=this.context,g=(0,c.default)(f).getInstance().log,h=(0,l.default)(f).getInstance(),p=void 0;return a={process:function(e){var t=null;return null!=e&&e.byteLength>0&&(t=new Uint8Array(e)),t},getModel:function(e){if(!e)return null;var r=t(e);return r||((r=(0,s.default)(f).create({metricsModel:(0,u.default)(f).getInstance()})).setScheduleController(e),p.push(r)),r},detachModel:function(e){var t=p.indexOf(e);t>-1&&p.splice(t,1)},isInitializationRequest:e,reset:function(){h.off(d.default.FRAGMENT_LOADING_COMPLETED,n,this),p=[]}},p=[],h.on(d.default.FRAGMENT_LOADING_COMPLETED,n,a),a}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../vo/metrics/HTTPRequest.js")),o=n(e("../vo/DataChunk.js")),s=n(e("../models/FragmentModel.js")),u=n(e("../models/MetricsModel.js")),l=n(e("../../core/EventBus.js")),d=n(e("../../core/events/Events.js")),f=n(e("../../core/FactoryMaker.js")),c=n(e("../../core/Debug.js"));a.__dashjs_factory_name="FragmentController",r.default=f.default.getClassFactory(a),t.exports=r.default},{"../../core/Debug.js":7,"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../models/FragmentModel.js":88,"../models/MetricsModel.js":91,"../vo/DataChunk.js":148,"../vo/metrics/HTTPRequest.js":166}],55:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){C={},A={audio:null,video:null},D={audio:c,video:f}}function t(e,t){if(!e||!t)return[];var r=t.id;return C[r]&&C[r][e]?C[r][e].list:[]}function r(e,t){return e&&t?C[t.id][e].current:null}function n(e){if(e){var t=e.type,n=e.streamInfo,a=n.id,o=r(t,n);if(C[a]&&C[a][t]&&(!o||!y(e,o))){C[a][t].current=e,o&&S.trigger(i.default.CURRENT_TRACK_CHANGED,{oldMediaInfo:o,newMediaInfo:e,switchMode:D[t]});var s=function(e){var t={lang:e.lang,viewpoint:e.viewpoint,roles:e.roles,accessibility:e.accessibility,audioChannelConfiguration:e.audioChannelConfiguration};return t.lang||t.viewpoint||t.role&&t.role.length>0||t.accessibility&&t.accessibility.length>0||t.audioChannelConfiguration&&t.audioChannelConfiguration.length>0?t:null}(e);s&&C[a][t].storeLastSettings&&(s.roles&&(s.role=s.roles[0],delete s.roles),s.accessibility&&(s.accessibility=s.accessibility[0]),s.audioChannelConfiguration&&(s.audioChannelConfiguration=s.audioChannelConfiguration[0]),I.setSavedMediaSettings(t,s))}}}function a(e,t){e&&t&&(A[e]=t)}function s(e){return e?A[e]:null}function m(){return j||p}function v(e){return"audio"===e||"video"===e||"text"===e||"fragmentedText"===e}function y(e,t){var r=e.id===t.id,n=e.viewpoint===t.viewpoint,a=e.lang===t.lang,i=e.roles.toString()==t.roles.toString(),o=e.accessibility.toString()==t.accessibility.toString(),s=e.audioChannelConfiguration.toString()==t.audioChannelConfiguration.toString();return r&&n&&a&&i&&o&&s}function _(e,t){var r=!e.lang||e.lang===t.lang,n=!e.viewpoint||e.viewpoint===t.viewpoint,a=!e.role||!!t.roles.filter(function(t){return t===e.role})[0],i=!e.accessibility||!!t.accessibility.filter(function(t){return t===e.accessibility})[0],o=!e.audioChannelConfiguration||!!t.audioChannelConfiguration.filter(function(t){return t===e.audioChannelConfiguration})[0];return r&&n&&a&&i&&o}function E(e){var t=m(),r=[],n=function(e){var t,r=0,n=[];return e.forEach(function(e){(t=Math.max.apply(Math,e.bitrateList.map(function(e){return e.bandwidth})))>r?(r=t,n=[e]):t===r&&n.push(e)}),n},a=function(e){var t,r=0,n=[];return e.forEach(function(e){(t=e.representationCount)>r?(r=t,n=[e]):t===r&&n.push(e)}),n};switch(t){case g:(r=n(e)).length>1&&(r=a(r));break;case h:(r=a(e)).length>1&&(r=n(e));break;default:M("track selection mode is not supported: "+t)}return r[0]}var T=this.context,M=(0,u.default)(T).getInstance().log,S=(0,o.default)(T).getInstance(),R=(0,l.default)(T).getInstance(),I=(0,d.default)(T).getInstance(),C=void 0,A=void 0,j=void 0,D=void 0,b=[c,f],O=[g,h];return{initialize:e,checkInitialMediaSettingsForType:function(e,r){var i=s(e),o=t(e,r),u=[];return"fragmentedText"===e?void n(o[0]):(i||a(e,i=I.getSavedMediaSettings(e)),void(o&&0!==o.length&&(i&&o.forEach(function(e){_(i,e)&&u.push(e)}),n(0===u.length?E(o):u.length>1?E(u):u[0]))))},addTrack:function(e){var t=e?e.type:null,a=e?e.streamInfo.id:null,i=s(t);return!(!e||!v(t)||(C[a]=C[a]||{audio:{list:[],storeLastSettings:!0,current:null},video:{list:[],storeLastSettings:!0,current:null},text:{list:[],storeLastSettings:!0,current:null},fragmentedText:{list:[],storeLastSettings:!0,current:null}},C[a][t].list.indexOf(e)>=0||(C[a][t].list.push(e),i&&_(i,e)&&!r(t,e.streamInfo)&&n(e),0)))},getTracksFor:t,getCurrentTrackFor:r,isCurrentTrack:function(e){var t=e.type,r=e.streamInfo.id;return C[r]&&C[r][t]&&y(C[r][t].current,e)},setTrack:n,setInitialSettings:a,getInitialSettings:s,setSwitchMode:function(e,t){return-1!==b.indexOf(t)?void(D[e]=t):void M("track switch mode is not supported: "+t)},getSwitchMode:function(e){return D[e]},setSelectionModeForInitialTrack:function(e){return-1!==O.indexOf(e)?void(j=e):void M("track selection mode is not supported: "+e)},getSelectionModeForInitialTrack:m,isMultiTrackSupportedByType:v,isTracksEqual:y,setConfig:function(e){e&&e.errHandler&&e.errHandler},reset:function(){e(),R.resetEmbedded()}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/events/Events.js")),o=n(e("../../core/EventBus.js")),s=n(e("../../core/FactoryMaker.js")),u=n(e("../../core/Debug.js")),l=n(e("../TextSourceBuffer.js")),d=n(e("../utils/DOMStorage.js")),f="neverReplace",c="alwaysReplace",g="highestBitrate",h="widestRange",p=g;a.__dashjs_factory_name="MediaController";var m=s.default.getSingletonFactory(a);m.TRACK_SWITCH_MODE_NEVER_REPLACE=f,m.TRACK_SWITCH_MODE_ALWAYS_REPLACE=c,m.TRACK_SELECTION_MODE_HIGHEST_BITRATE=g,m.TRACK_SELECTION_MODE_WIDEST_RANGE=h,m.DEFAULT_INIT_TRACK_SELECTION_MODE=p,r.default=m,t.exports=r.default},{"../../core/Debug.js":7,"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../TextSourceBuffer.js":44,"../utils/DOMStorage.js":138}],56:[function(e,t,r){"use strict";function n(){return{createMediaSource:function(){var e="WebKitMediaSource"in window;return"MediaSource"in window?new MediaSource:e?new WebKitMediaSource:null},attachMediaSource:function(e,t){var r=window.URL.createObjectURL(e);return t.setSource(r),r},detachMediaSource:function(e){e.setSource(null)},setDuration:function(e,t){return e.duration!=t&&(e.duration=t),e.duration},signalEndOfStream:function(e){var t=e.sourceBuffers,r=t.length,n=0;if("open"===e.readyState){for(;r>n;n++){if(t[n].updating)return;if(0===t[n].buffered.length)return}e.endOfStream()}}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js"));n.__dashjs_factory_name="MediaSourceController",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../core/FactoryMaker.js":9}],57:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){X=0,Z=NaN,J=null,te=null,ne=!1,$={},re=(0,s.default)(B).getInstance()}function t(e){e.fromStreamInfo&&$[e.fromStreamInfo.id]&&delete $[e.fromStreamInfo.id]}function r(){return m(!0)+ee.duration-g()}function n(){K?(K.autoplay=!0,K.play()):ne=!0}function a(){return K?K.paused:void 0}function d(){K&&(K.pause(),K.autoplay=!1)}function c(e){Q&&(k("Requesting seek to time: "+e),Q.setCurrentTime(e))}function g(){return K?K.currentTime:void 0}function h(){return K?K.playbackRate:void 0}function p(){return K?K.ended:void 0}function m(e){var t=void 0,r=e?NaN:parseInt((0,o.default)(B).getInstance().getURIFragmentData().s,10);if(te)!isNaN(r)&&r>1262304e3&&((t=r-ee.manifestInfo.availableFrom.getTime()/1e3)>Z||t<Z-ee.manifestInfo.DVRWindowSize)&&(t=null),t=t||Z;else if(!isNaN(r)&&r<Math.max(ee.manifestInfo.duration,ee.duration)&&r>=0)t=r;else{var n=$[ee.id];void 0===n&&(n=H.getActiveStreamCommonEarliestTime()),t=Math.max(n,ee.start)}return t}function v(){null===J&&(J=setInterval(function(){U.trigger(l.default.WALLCLOCK_TIME_UPDATED,{isDynamic:te,time:new Date})},re.getWallclockTimeUpdateInterval()))}function y(){clearInterval(J),J=null}function _(){var e=m(!1);e>0&&(c(e),k("Starting playback at offset: "+e))}function E(){if(!a()&&te&&0!==K.readyState){var e=g(),t=function(e){var t=Y.getReadOnlyMetricsFor("video")||Y.getReadOnlyMetricsFor("audio"),r=G.getCurrentDVRInfo(t),n=r?r.range:null;return n?e>=n.start&&e<=n.end?e:Math.max(n.end-2*ee.manifestInfo.minBufferTime,n.start):NaN}(e);!isNaN(t)&&t!==e&&c(t)}}function T(e){if(!e.error){var t=W.convertDataToTrack(V.getValue(),e.currentRepresentation).mediaInfo.streamInfo;ee.id===t.id&&(ee=t,E())}}function M(e){e.error||0===K.readyState||_()}function S(){U.trigger(l.default.CAN_PLAY)}function R(){k("Native video element event: play"),E(),v(),U.trigger(l.default.PLAYBACK_STARTED,{startTime:g()})}function I(){k("Native video element event: playing"),U.trigger(l.default.PLAYBACK_PLAYING,{playingTime:g()})}function C(){k("Native video element event: pause"),U.trigger(l.default.PLAYBACK_PAUSED,{ended:p()})}function A(){var e=g();k("Seeking to: "+e),v(),U.trigger(l.default.PLAYBACK_SEEKING,{seekTime:e})}function j(){k("Native video element event: seeked"),U.trigger(l.default.PLAYBACK_SEEKED)}function D(){var e=g();e!==X&&(X=e,U.trigger(l.default.PLAYBACK_TIME_UPDATED,{timeToEnd:r(),time:e}))}function b(){U.trigger(l.default.PLAYBACK_PROGRESS)}function O(){var e=h();k("Native video element event: ratechange: ",e),U.trigger(l.default.PLAYBACK_RATE_CHANGED,{playbackRate:e})}function P(){k("Native video element event: loadedmetadata"),(!te&&ee.isFirst||q.isTimeSyncCompleted())&&_(),U.trigger(l.default.PLAYBACK_METADATA_LOADED),v()}function w(){k("Native video element event: ended"),d(),y(),U.trigger(l.default.PLAYBACK_ENDED)}function N(e){var t=e.target||e.srcElement;U.trigger(l.default.PLAYBACK_ERROR,{error:t.error})}function x(e){var t=e.bufferedRanges;if(t&&t.length){var r=Math.max(t.start(0),ee.start),n=void 0===$[ee.id]?r:Math.max($[ee.id],r);n!==$[ee.id]&&(!te&&m(!0)<n&&c(n),$[ee.id]=n)}}function L(e){e.streamInfo.id===ee.id&&Q.setStallState(e.mediaType,e.state===i.default.BUFFER_EMPTY)}var F,B=this.context,k=(0,f.default)(B).getInstance().log,U=(0,u.default)(B).getInstance(),K=void 0,H=void 0,q=void 0,Y=void 0,G=void 0,V=void 0,z=void 0,W=void 0,Q=void 0,X=void 0,Z=void 0,J=void 0,$=void 0,ee=void 0,te=void 0,re=void 0,ne=void 0;return F={initialize:function(e){ee=e,(K=Q.getElement()).addEventListener("canplay",S),K.addEventListener("play",R),K.addEventListener("playing",I),K.addEventListener("pause",C),K.addEventListener("error",N),K.addEventListener("seeking",A),K.addEventListener("seeked",j),K.addEventListener("timeupdate",D),K.addEventListener("progress",b),K.addEventListener("ratechange",O),K.addEventListener("loadedmetadata",P),K.addEventListener("ended",w),te=ee.manifestInfo.isDynamic,Z=ee.start,U.on(l.default.DATA_UPDATE_COMPLETED,T,this),U.on(l.default.LIVE_EDGE_SEARCH_COMPLETED,M,this),U.on(l.default.BYTES_APPENDED,x,this),U.on(l.default.BUFFER_LEVEL_STATE_CHANGED,L,this),U.on(l.default.PERIOD_SWITCH_STARTED,t,this),ne&&(ne=!1,n())},setConfig:function(e){e&&(e.streamController&&(H=e.streamController),e.timelineConverter&&(q=e.timelineConverter),e.metricsModel&&(Y=e.metricsModel),e.dashMetrics&&(G=e.dashMetrics),e.manifestModel&&(V=e.manifestModel),e.dashManifestModel&&(z=e.dashManifestModel),e.adapter&&(W=e.adapter),e.videoModel&&(Q=e.videoModel))},getStreamStartTime:m,getTimeToStreamEnd:r,isPlaybackStarted:function(){return g()>0},getStreamId:function(){return ee.id},getStreamDuration:function(){return ee.duration},getTime:g,getPlaybackRate:h,getPlayedRanges:function(){return K?K.played:void 0},getEnded:p,getIsDynamic:function(){return te},setLiveStartTime:function(e){Z=e},getLiveStartTime:function(){return Z},computeLiveDelay:function(e,t){var r,n=z.getMpd(V.getValue());r=re.getUseSuggestedPresentationDelay()&&n.hasOwnProperty("suggestedPresentationDelay")?n.suggestedPresentationDelay:re.getLiveDelay()?re.getLiveDelay():isNaN(e)?2*ee.manifestInfo.minBufferTime:e*re.getLiveDelayFragmentCount();var a=Math.max(t-10,t/2);return Math.min(r,a)},play:n,isPaused:a,pause:d,isSeeking:function(){return K?K.seeking:void 0},seek:c,reset:function(){Q&&K&&(U.off(l.default.DATA_UPDATE_COMPLETED,T,this),U.off(l.default.BUFFER_LEVEL_STATE_CHANGED,L,this),U.off(l.default.LIVE_EDGE_SEARCH_COMPLETED,M,this),U.off(l.default.BYTES_APPENDED,x,this),y(),K.removeEventListener("canplay",S),K.removeEventListener("play",R),K.removeEventListener("playing",I),K.removeEventListener("pause",C),K.removeEventListener("error",N),K.removeEventListener("seeking",A),K.removeEventListener("seeked",j),K.removeEventListener("timeupdate",D),K.removeEventListener("progress",b),K.removeEventListener("ratechange",O),K.removeEventListener("loadedmetadata",P),K.removeEventListener("ended",w)),Q=null,ee=null,K=null,te=null,e()}},e(),F}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./BufferController.js")),o=n(e("../models/URIQueryAndFragmentModel.js")),s=n(e("../../streaming/models/MediaPlayerModel.js")),u=n(e("../../core/EventBus.js")),l=n(e("../../core/events/Events.js")),d=n(e("../../core/FactoryMaker.js")),f=n(e("../../core/Debug.js"));a.__dashjs_factory_name="PlaybackController",r.default=d.default.getSingletonFactory(a),t.exports=r.default},{"../../core/Debug.js":7,"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../../streaming/models/MediaPlayerModel.js":90,"../models/URIQueryAndFragmentModel.js":92,"./BufferController.js":52}],58:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e,t){var r=0,n=null;oe&&!1===ue&&(n=se.start,r=e.getTime()-n.getTime(),se.duration=r,se.stopreason=t,oe.trace.push(se),ue=!0)}function r(){ee&&(L(),ie=!1,ae?a(ne.quality):M(),ae&&(ae=!1),Y("Schedule controller starting for "+$))}function n(){ie||(ie=!0,clearInterval(fe),Y("Schedule controller stopping for "+$))}function a(e){var t=W.getInitRequest(pe,e);return null!==t&&te.executeRequest(t),t}function E(e){var t,r,n,a=e.length;for(n=0;a>n;n++)r=(t=e[n]).startTime+t.duration/2+.1,(t=W.getFragmentRequestForTime(pe,ne,r,{timeThreshold:0,ignoreIsFinished:!0}))&&te.executeRequest(t)}function M(){ie||ge.isPaused()&&!Te||(!_e.execute(pe)||le||!X.getIsTextTrack($)&&ye.getIsAppendingInProgress()?S(1e3):(le=!0,he.getPlaybackQuality(pe,function(){var e=Ee.execute(pe);e?te.executeRequest(e):(le=!1,S(1e3))})))}function S(e){fe=setTimeout(M,e)}function R(e){if($===e.mediaType&&pe.getStreamInfo().id===e.streamInfo.id){if(null==(ne=pe.getRepresentationInfoForQuality(e.newQuality)))throw"Unexpected error! - currentRepresentationInfo is null or undefined";t(new Date,i.default.Trace.REPRESENTATION_SWITCH_STOP_REASON),L()}}function I(e){e.error||(ne=W.convertDataToTrack(z.getValue(),e.currentRepresentation))}function C(e){e.error||(ne=pe.getCurrentRepresentationInfo(),re&&null===ve.getLiveEdge()||(ee=!0),r())}function A(e){e.fragmentModel===te&&(n(),Y("Stream is complete"))}function j(e){e.sender===te&&(isNaN(e.request.index)||(le=!1),e.error&&e.serviceLocation&&!ie&&E([e.request]))}function D(e){e.sender.getStreamProcessor()===pe&&M()}function b(e){e.sender.getStreamProcessor()===pe&&n()}function O(e){e.sender.getStreamProcessor()===pe&&a(e.requiredQuality)}function P(e){e.sender.getStreamProcessor()===pe&&(te.removeExecutedRequestsBeforeTime(e.to),e.hasEnoughSpaceToAppend&&!ye.getIsBufferingCompleted()&&r())}function w(e){e.sender.getStreamProcessor()!==pe||e.state!==u.default.BUFFER_EMPTY||ge.isSeeking()||(Y("Stalling Buffer"),t(new Date,i.default.Trace.REBUFFERING_REASON))}function N(e){e.sender.getStreamProcessor()===pe&&n()}function x(){te.abortRequests(),n()}function L(){oe&&!0===ue&&ne&&(ue=!1,(se=new i.default.Trace).representationid=ne.id,se.start=new Date,se.mstart=1e3*ge.getTime(),se.playbackspeed=ge.getPlaybackRate().toString())}function F(e){e.sender.getStreamProcessor()===pe&&a(e.index)}function B(){r()}function k(e){ce=e.seekTime,ae||(le=!1),ie&&r();var t=V.getMetricsFor("stream"),n=Q.getCurrentManifestUpdate(t),a=ne.DVRWindow?ne.DVRWindow.end-ge.getTime():NaN;V.updateManifestUpdateInfo(n,{latency:a})}function U(e){se&&(se.playbackspeed=e.playbackRate.toString())}function K(e){if(!e.error){var t,n=e.liveEdge,a=ne.mediaInfo.streamInfo.manifestInfo,i=n-ge.computeLiveDelay(ne.fragmentDuration,a.DVRWindowSize/2),o=V.getMetricsFor("stream"),s=Q.getCurrentManifestUpdate(o),u=ge.getLiveStartTime();t=W.getFragmentRequestForTime(pe,ne,i,{ignoreIsFinished:!0}).startTime,ce=t,(isNaN(u)||t>u)&&ge.setLiveStartTime(t),V.updateManifestUpdateInfo(s,{currentTime:t,presentationStartTime:n,latency:n-t,clientTimeOffset:Z.getClientTimeOffset()}),ee=!0,r()}}var H,q=this.context,Y=(0,T.default)(q).getInstance().log,G=(0,y.default)(q).getInstance(),V=e.metricsModel,z=e.manifestModel,W=e.adapter,Q=e.dashMetrics,X=e.dashManifestModel,Z=e.timelineConverter,J=e.mediaPlayerModel,$=void 0,ee=void 0,te=void 0,re=void 0,ne=void 0,ae=void 0,ie=void 0,oe=void 0,se=void 0,ue=void 0,le=void 0,de=void 0,fe=void 0,ce=void 0,ge=void 0,he=void 0,pe=void 0,me=void 0,ve=void 0,ye=void 0,_e=void 0,Ee=void 0,Te=void 0;return H={initialize:function(e,t){$=e,pe=t,ve=(0,v.default)(q).getInstance(),ge=(0,o.default)(q).getInstance(),he=(0,s.default)(q).getInstance(),me=pe.getFragmentController(),ye=pe.getBufferController(),te=me.getModel(this),re=pe.isDynamic(),Te=J.getScheduleWhilePaused(),_e=(0,l.default)(q).create({dashMetrics:(0,g.default)(q).getInstance(),metricsModel:(0,c.default)(q).getInstance(),textSourceBuffer:(0,f.default)(q).getInstance()}),Ee=(0,d.default)(q).create({adapter:(0,h.default)(q).getInstance(),sourceBufferController:(0,p.default)(q).getInstance(),virtualBuffer:(0,m.default)(q).getInstance(),textSourceBuffer:(0,f.default)(q).getInstance()}),X.getIsTextTrack($)&&G.on(_.default.TIMED_TEXT_REQUESTED,F,this),G.on(_.default.LIVE_EDGE_SEARCH_COMPLETED,K,this),G.on(_.default.QUALITY_CHANGED,R,this),G.on(_.default.DATA_UPDATE_STARTED,b,this),G.on(_.default.DATA_UPDATE_COMPLETED,I,this),G.on(_.default.FRAGMENT_LOADING_COMPLETED,j,this),G.on(_.default.STREAM_COMPLETED,A,this),G.on(_.default.STREAM_INITIALIZED,C,this),G.on(_.default.BUFFER_LEVEL_STATE_CHANGED,w,this),G.on(_.default.BUFFER_CLEARED,P,this),G.on(_.default.BYTES_APPENDED,D,this),G.on(_.default.INIT_REQUESTED,O,this),G.on(_.default.QUOTA_EXCEEDED,N,this),G.on(_.default.BUFFER_LEVEL_STATE_CHANGED,w,this),G.on(_.default.PLAYBACK_STARTED,B,this),G.on(_.default.PLAYBACK_SEEKING,k,this),G.on(_.default.PLAYBACK_RATE_CHANGED,U,this),G.on(_.default.URL_RESOLUTION_FAILED,x,this)},getStreamProcessor:function(){return pe},getSeekTarget:function(){return ce},setSeekTarget:function(e){ce=e},getFragmentModel:function(){return te},setTimeToLoadDelay:function(e){de=e},getTimeToLoadDelay:function(){return de},replaceCanceledRequests:E,start:r,stop:n,reset:function(){G.off(_.default.LIVE_EDGE_SEARCH_COMPLETED,K,this),G.off(_.default.DATA_UPDATE_STARTED,b,this),G.off(_.default.DATA_UPDATE_COMPLETED,I,this),G.off(_.default.BUFFER_LEVEL_STATE_CHANGED,w,this),G.off(_.default.QUALITY_CHANGED,R,this),G.off(_.default.FRAGMENT_LOADING_COMPLETED,j,this),G.off(_.default.STREAM_COMPLETED,A,this),G.off(_.default.STREAM_INITIALIZED,C,this),G.off(_.default.QUOTA_EXCEEDED,N,this),G.off(_.default.BYTES_APPENDED,D,this),G.off(_.default.BUFFER_CLEARED,P,this),G.off(_.default.INIT_REQUESTED,O,this),G.off(_.default.PLAYBACK_RATE_CHANGED,U,this),G.off(_.default.PLAYBACK_SEEKING,k,this),G.off(_.default.PLAYBACK_STARTED,B,this),G.off(_.default.URL_RESOLUTION_FAILED,x,this),X.getIsTextTrack($)&&G.off(_.default.TIMED_TEXT_REQUESTED,F,this),n(),me.detachModel(te),le=!1,de=0,ce=NaN,ge=null,oe=null},setPlayList:function(e){oe=e},finalisePlayList:function(e,r){t(e,r),oe=null}},ae=!0,ie=!1,oe=null,se=null,ue=!0,le=!1,de=0,ce=NaN,H}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../vo/metrics/PlayList.js")),o=n(e("./PlaybackController.js")),s=n(e("./AbrController.js")),u=n(e("./BufferController.js")),l=n(e("../rules/scheduling/BufferLevelRule.js")),d=n(e("../rules/scheduling/NextFragmentRequestRule.js")),f=n(e("../TextSourceBuffer.js")),c=n(e("../models/MetricsModel.js")),g=n(e("../../dash/DashMetrics.js")),h=n(e("../../dash/DashAdapter.js")),p=n(e("../controllers/SourceBufferController.js")),m=n(e("../VirtualBuffer.js")),v=n(e("../utils/LiveEdgeFinder.js")),y=n(e("../../core/EventBus.js")),_=n(e("../../core/events/Events.js")),E=n(e("../../core/FactoryMaker.js")),T=n(e("../../core/Debug.js"));a.__dashjs_factory_name="ScheduleController",r.default=E.default.getClassFactory(a),t.exports=r.default},{"../../core/Debug.js":7,"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../../dash/DashAdapter.js":13,"../../dash/DashMetrics.js":15,"../TextSourceBuffer.js":44,"../VirtualBuffer.js":46,"../controllers/SourceBufferController.js":59,"../models/MetricsModel.js":91,"../rules/scheduling/BufferLevelRule.js":129,"../rules/scheduling/NextFragmentRequestRule.js":130,"../utils/LiveEdgeFinder.js":141,"../vo/metrics/PlayList.js":168,"./AbrController.js":49,"./BufferController.js":52,"./PlaybackController.js":57}],59:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e,t,r){var n,a,i=null,o=0,s=0,u=null,l=null,d=0,f=r||.15;try{i=e.buffered}catch(e){return null}if(null!=i){for(a=0,n=i.length;n>a;a++)if(o=i.start(a),s=i.end(a),null===u)d=Math.abs(o-t),(t>=o&&s>t||f>=d)&&(u=o,l=s);else{if(!(f>=(d=o-l)))break;l=s}if(null!==u)return{start:u,end:l}}return null}function t(e){try{return e.buffered}catch(e){return null}}function r(e,t){var r,n=function(){e.updating||(clearInterval(r),t())};if(e.updating)if("function"==typeof e.addEventListener)try{e.addEventListener("updateend",function r(){e.updating||(e.removeEventListener("updateend",r,!1),t())},!1)}catch(e){r=setInterval(n,50)}else r=setInterval(n,50);else t()}var n=this.context,a=(0,p.default)(n).getInstance(),v=void 0;return{append:function(e,t){var n=t.bytes,i="append"in e?"append":"appendBuffer"in e?"appendBuffer":null,o="Object"===Object.prototype.toString.call(e).slice(8,-1);if(i)try{r(e,function(){o?e[i](n,t):e[i](n),r(e,function(){a.trigger(m.default.SOURCEBUFFER_APPEND_COMPLETED,{buffer:e,bytes:n})})})}catch(t){a.trigger(m.default.SOURCEBUFFER_APPEND_COMPLETED,{buffer:e,bytes:n,error:new h.default(t.code,t.message,null)})}},remove:function(e,t,n,i){try{r(e,function(){t>=0&&n>t&&"ended"!==i.readyState&&e.remove(t,n),r(e,function(){a.trigger(m.default.SOURCEBUFFER_REMOVE_COMPLETED,{buffer:e,from:t,to:n})})})}catch(r){a.trigger(m.default.SOURCEBUFFER_REMOVE_COMPLETED,{buffer:e,from:t,to:n,error:new h.default(r.code,r.message,null)})}},abort:function(e,t){try{"open"===e.readyState&&t.abort()}catch(e){}},createSourceBuffer:function(e,t){var r=t.codec,a=null;try{if(r.match(/application\/mp4;\s*codecs="(stpp|wvtt)"/i))throw new h.default("not really supported");a=e.addSourceBuffer(r)}catch(e){if(!t.isText&&-1===r.indexOf('codecs="stpp"')&&-1===r.indexOf('codecs="wvtt"'))throw e;(a=(0,i.default)(n).getInstance()).setConfig({errHandler:(0,u.default)(n).getInstance(),adapter:(0,s.default)(n).getInstance(),dashManifestModel:v,mediaController:(0,o.default)(n).getInstance(),videoModel:(0,g.default)(n).getInstance(),streamController:(0,l.default)(n).getInstance(),textTracks:(0,d.default)(n).getInstance(),VTTParser:(0,f.default)(n).getInstance(),TTMLParser:(0,c.default)(n).getInstance()})}return a},removeSourceBuffer:function(e,t){try{e.removeSourceBuffer(t)}catch(e){}},getBufferRange:e,getAllRanges:t,getTotalBufferedTime:function(e){var r,n,a=t(e),i=0;if(!a)return i;for(n=0,r=a.length;r>n;n++)i+=a.end(n)-a.start(n);return i},getBufferLength:function(t,r,n){var a;return null===(a=e(t,r,n))?0:a.end-r},getRangeDifference:function(e,r){if(!r)return null;var n,a,i,o,s,u,l,d,f=t(r);if(!f)return null;for(var c=0,g=f.length;g>c;c++){if(s=e.length>c?{start:e.start(c),end:e.end(c)}:null,n=f.start(c),a=f.end(c),!s)return{start:n,end:a};if(i=s.start===n,o=s.end===a,!i||!o){if(i)d={start:s.end,end:a};else{if(!o)return{start:n,end:a};d={start:n,end:s.start}}return u=e.length>c+1?{start:e.start(c+1),end:e.end(c+1)}:null,l=g>c+1?{start:f.start(c+1),end:f.end(c+1)}:null,!u||l&&l.start===u.start&&l.end===u.end||(d.end=u.start),d}}return null},setConfig:function(e){e&&e.dashManifestModel&&(v=e.dashManifestModel)}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../TextSourceBuffer.js")),o=n(e("./MediaController.js")),s=n(e("../../dash/DashAdapter.js")),u=n(e("../utils/ErrorHandler.js")),l=n(e("./StreamController.js")),d=n(e("../TextTracks.js")),f=n(e("../utils/VTTParser.js")),c=n(e("../utils/TTMLParser.js")),g=n(e("../models/VideoModel.js")),h=n(e("../vo/Error.js")),p=n(e("../../core/EventBus.js")),m=n(e("../../core/events/Events.js")),v=n(e("../../core/FactoryMaker.js"));a.__dashjs_factory_name="SourceBufferController";var y=v.default.getSingletonFactory(a);y.QUOTA_EXCEEDED_ERROR_CODE=22,r.default=y,t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../../dash/DashAdapter.js":13,"../TextSourceBuffer.js":44,"../TextTracks.js":45,"../models/VideoModel.js":93,"../utils/ErrorHandler.js":139,"../utils/TTMLParser.js":144,"../utils/VTTParser.js":146,"../vo/Error.js":149,"./MediaController.js":55,"./StreamController.js":60}],60:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e,t){t=t||new Date,ue&&(W&&W.getProcessors().forEach(function(r){var n=r.getScheduleController();n&&n.finalisePlayList(t,e)}),B.addPlayList(ue),ue=null)}function t(e){(ue=new h.default).start=new Date,ue.mstart=1e3*ae.getTime(),ue.starttype=e,W&&W.getProcessors().forEach(function(e){var t=e.getScheduleController();t&&t.setPlayList(ue)})}function r(e,t,r){b.trigger(e,{fromStreamInfo:t?t.getStreamInfo():null,toStreamInfo:r.getStreamInfo()})}function n(e){var t=e.error?e.error.code:0,r="";if(-1!==t){switch(t){case 1:r="MEDIA_ERR_ABORTED";break;case 2:r="MEDIA_ERR_NETWORK";break;case 3:r="MEDIA_ERR_DECODE";break;case 4:r="MEDIA_ERR_SRC_NOT_SUPPORTED";break;case 5:r="MEDIA_ERR_ENCRYPTED";break;default:r="UNKNOWN"}ee=!0,e.error.msExtendedCode&&(r+=" (0x"+(e.error.msExtendedCode>>>0).toString(16).toUpperCase()+")"),D("Video Element Error: "+r),e.error&&D(e.error),G.mediaSourceError(r),C()}}function a(e){var t=ne.getPlaybackQuality();t&&B.addDroppedFrames("video",t),ae.isSeeking()||e.timeToEnd<A&&K.signalEndOfStream(re)}function g(){var t=function(){var e=W.getStreamInfo().start,t=W.getStreamInfo().duration;return z.filter(function(r){return r.getStreamInfo().start===e+t})[0]}();t&&E(W,t,NaN),e(t?h.default.Trace.END_OF_PERIOD_STOP_REASON:h.default.Trace.END_OF_CONTENT_STOP_REASON)}function m(r){var n=function(e){var t=0,r=null,n=z.length;n>0&&(t+=z[0].getStartTime());for(var a=0;n>a;a++)if((t+=(r=z[a]).getDuration())>e)return r;return null}(r.seekTime);n&&n!==W?(e(h.default.Trace.END_OF_PERIOD_STOP_REASON),E(W,n,r.seekTime)):e(h.default.Trace.USER_REQUEST_STOP_REASON),t(h.default.SEEK_START_REASON)}function v(){se?(se=!1,t(h.default.INITIAL_PLAYOUT_START_REASON)):oe&&(oe=!1,t(h.default.RESUME_FROM_PAUSE_START_REASON))}function y(t){t.ended||(oe=!0,e(h.default.Trace.USER_REQUEST_STOP_REASON))}function _(e){var t=e.streamInfo.isLast;re&&t&&K.signalEndOfStream(re)}function E(e,t,n){!J&&e&&t&&e!==t&&(J=!0,r(l.default.PERIOD_SWITCH_STARTED,e,t),e.deactivate(),W=t,ae.initialize(W.getStreamInfo()),T(function(){isNaN(n)?function(){var e=ae.getStreamStartTime(!0);W.getProcessors().forEach(function(t){F.setIndexHandlerTime(t,e)}),ae.seek(e)}():ae.seek(n),ae.play(),W.startEventController(),J=!1,r(l.default.PERIOD_SWITCH_COMPLETED,e,t)}))}function T(e){function t(){D("MediaSource is open!"),window.URL.revokeObjectURL(r),re.removeEventListener("sourceopen",t),re.removeEventListener("webkitsourceopen",t),function(){var e,t;e=W.getStreamInfo().manifestInfo.duration,t=K.setDuration(re,e),D("Duration successfully set to: "+t)}(),W.activate(re),e&&e()}var r=void 0;re?K.detachMediaSource(ne):re=K.createMediaSource(),re.addEventListener("sourceopen",t,!1),re.addEventListener("webkitsourceopen",t,!1),r=K.attachMediaSource(re,ne),D("MediaSource attached to element.  Waiting on open...")}function M(){if(!$){var e=z.length,t=0;for(W.isActivated()&&se&&0===W.getStreamInfo().index&&(W.startEventController(),Z&&ae.play());e>t;t++)if(!z[t].isInitialized())return;b.trigger(l.default.STREAMS_COMPOSED)}}function S(){M()}function R(){!function(){var e,t,n,a,i,s,u,d=x.getValue(),f=B.getMetricsFor("stream"),c=k.getCurrentManifestUpdate(f),g=[];if(d){s=F.getStreamsInfo(d),Q&&(b.trigger(l.default.PROTECTION_CREATED,{controller:Q,manifest:d}),Q.setMediaElement(ne.getElement()),X&&Q.setProtectionData(X));try{if(0===s.length)throw new Error("There are no streams");for(B.updateManifestUpdateInfo(c,{currentTime:ae.getTime(),buffered:ne.getElement().buffered,presentationStartTime:s[0].start,clientTimeOffset:V.getClientTimeOffset()}),$=!0,a=0,t=s.length;t>a;a++){for(e=s[a],i=0,n=z.length;n>i;i++)z[i].getId()===e.id&&(u=z[i],g.push(u),u.updateData(e));u||((u=(0,o.default)(j).create({manifestModel:x,manifestUpdater:w,adapter:F,timelineConverter:V,capabilities:P,errHandler:G,baseURLController:q})).initialize(e,Q),b.on(l.default.STREAM_INITIALIZED,S,this),g.push(u),W&&u.updateData(e)),B.addManifestUpdateStreamInfo(c,e.id,e.index,e.start,e.duration),u=null}z=g,W||(W=z[0],r(l.default.PERIOD_SWITCH_STARTED,null,W),ae.initialize(W.getStreamInfo()),r(l.default.PERIOD_SWITCH_COMPLETED,null,W)),re||T(),$=!1,M()}catch(e){G.manifestError(e.message,"nostreamscomposed",d),te=!0,C()}}}()}function I(e){if(e.error)te=!0,C();else{var t,r,n=e.manifest,a=F.getStreamsInfo(n)[0],i=F.getMediaInfoForType(n,a,"video")||F.getMediaInfoForType(n,a,"audio");i&&(t=F.getDataForMedia(i),(r=L.getRepresentationsForAdaptation(n,t)[0].useCalculatedLiveEdgeTime)&&(D("SegmentTimeline detected using calculated Live Edge Time"),ie.setUseManifestDateHeaderTimeSource(!1)));var o=L.getUTCTimingSources(e.manifest),s=!L.getIsDynamic(n)||r?o:o.concat(ie.getUTCTimingSources()),u=(0,d.default)(j).getInstance().isManifestHTTPS();s.forEach(function(e){e.value.replace(/.*?:\/\//g,"")===c.default.DEFAULT_UTC_TIMING_SOURCE.value.replace(/.*?:\/\//g,"")&&(e.value=e.value.replace(u?new RegExp(/^(http:)?\/\//i):new RegExp(/^(https:)?\/\//i),u?"https://":"http://"),D("Matching default timing source protocol to manifest protocol: ",e.value))}),q.initialize(n),H.setConfig({metricsModel:B,dashMetrics:k}),H.initialize(s,ie.getUseManifestDateHeaderTimeSource())}}function C(){H.reset(),e(ee||te?h.default.Trace.FAILURE_STOP_REASON:h.default.Trace.USER_REQUEST_STOP_REASON);for(var t=0,r=z.length;r>t;t++){var i=z[t];b.off(l.default.STREAM_INITIALIZED,S,this),i.reset(ee)}z=[],b.off(l.default.PLAYBACK_TIME_UPDATED,a,this),b.off(l.default.PLAYBACK_SEEKING,m,this),b.off(l.default.PLAYBACK_ERROR,n,this),b.off(l.default.PLAYBACK_STARTED,v,this),b.off(l.default.PLAYBACK_PAUSED,y,this),b.off(l.default.PLAYBACK_ENDED,g,this),b.off(l.default.MANIFEST_UPDATED,I,this),b.off(l.default.STREAM_BUFFERING_COMPLETED,_,this),q.reset(),w.reset(),B.clearAllCurrentMetrics(),x.setValue(null),N.reset(),V.reset(),U.reset(),F.reset(),Y.reset(),J=!1,$=!1,W=null,ee=!1,te=!1,se=!0,oe=!1,re&&(K.detachMediaSource(ne),re=null),ne=null,Q&&(Q.setMediaElement(null),Q=null,X=null,x.getValue()&&b.trigger(l.default.PROTECTION_DESTROYED,{data:x.getValue().url})),b.trigger(l.default.STREAM_TEARDOWN_COMPLETE)}var A=1,j=this.context,D=(0,p.default)(j).getInstance().log,b=(0,u.default)(j).getInstance(),O=void 0,P=void 0,w=void 0,N=void 0,x=void 0,L=void 0,F=void 0,B=void 0,k=void 0,U=void 0,K=void 0,H=void 0,q=void 0,Y=void 0,G=void 0,V=void 0,z=void 0,W=void 0,Q=void 0,X=void 0,Z=void 0,J=void 0,$=void 0,ee=void 0,te=void 0,re=void 0,ne=void 0,ae=void 0,ie=void 0,oe=void 0,se=void 0,ue=void 0;return O={initialize:function(e,t){Z=e,X=t,V.initialize(),(w=(0,s.default)(j).getInstance()).setConfig({log:D,manifestModel:x,dashManifestModel:L}),w.initialize(N),ne=(0,f.default)(j).getInstance(),(ae=(0,i.default)(j).getInstance()).setConfig({streamController:O,timelineConverter:V,metricsModel:B,dashMetrics:k,manifestModel:x,dashManifestModel:L,adapter:F,videoModel:ne}),b.on(l.default.TIME_SYNCHRONIZATION_COMPLETED,R,this),b.on(l.default.PLAYBACK_SEEKING,m,this),b.on(l.default.PLAYBACK_TIME_UPDATED,a,this),b.on(l.default.PLAYBACK_ENDED,g,this),b.on(l.default.PLAYBACK_ERROR,n,this),b.on(l.default.PLAYBACK_STARTED,v,this),b.on(l.default.PLAYBACK_PAUSED,y,this),b.on(l.default.MANIFEST_UPDATED,I,this),b.on(l.default.STREAM_BUFFERING_COMPLETED,_,this)},getAutoPlay:function(){return Z},getActiveStreamInfo:function(){return W?W.getStreamInfo():null},isStreamActive:function(e){return W.getId()===e.id},getStreamById:function(e){return z.filter(function(t){return t.getId()===e})[0]},getTimeRelativeToStreamId:function(e,t){for(var r=null,n=0,a=0,i=null,o=z.length,s=0;o>s;s++){if(a=(r=z[s]).getStartTime(),i=r.getDuration(),Number.isFinite(a)&&(n=a),r.getId()===t)return e-n;Number.isFinite(i)&&(n+=i)}return null},load:function(e){N.load(e)},loadWithManifest:function(e){w.setManifest(e)},getActiveStreamCommonEarliestTime:function(){var e=[];return W.getProcessors().forEach(function(t){e.push(t.getIndexHandler().getEarliestTime())}),Math.min.apply(Math,e)},setConfig:function(e){e&&(e.capabilities&&(P=e.capabilities),e.manifestLoader&&(N=e.manifestLoader),e.manifestModel&&(x=e.manifestModel),e.dashManifestModel&&(L=e.dashManifestModel),e.protectionController&&(Q=e.protectionController),e.adapter&&(F=e.adapter),e.metricsModel&&(B=e.metricsModel),e.dashMetrics&&(k=e.dashMetrics),e.liveEdgeFinder&&(U=e.liveEdgeFinder),e.mediaSourceController&&(K=e.mediaSourceController),e.timeSyncController&&(H=e.timeSyncController),e.baseURLController&&(q=e.baseURLController),e.virtualBuffer&&(Y=e.virtualBuffer),e.errHandler&&(G=e.errHandler),e.timelineConverter&&(V=e.timelineConverter))},reset:C},Q=null,z=[],ie=(0,c.default)(j).getInstance(),Z=!0,J=!1,$=!1,oe=!1,se=!0,ue=null,ee=!1,te=!1,O}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./PlaybackController.js")),o=n(e("../Stream.js")),s=n(e("../ManifestUpdater.js")),u=n(e("../../core/EventBus.js")),l=n(e("../../core/events/Events.js")),d=n(e("../models/URIQueryAndFragmentModel.js")),f=n(e("../models/VideoModel.js")),c=n(e("../models/MediaPlayerModel.js")),g=n(e("../../core/FactoryMaker.js")),h=n(e("../vo/metrics/PlayList.js")),p=n(e("../../core/Debug.js"));a.__dashjs_factory_name="StreamController",r.default=g.default.getSingletonFactory(a),t.exports=r.default},{"../../core/Debug.js":7,"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../ManifestUpdater.js":38,"../Stream.js":42,"../models/MediaPlayerModel.js":90,"../models/URIQueryAndFragmentModel.js":92,"../models/VideoModel.js":93,"../vo/metrics/PlayList.js":168,"./PlaybackController.js":57}],61:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e){c=e}function r(e){e.sender.getStreamProcessor()===p&&u.trigger(o.default.TIMED_TEXT_REQUESTED,{index:0,sender:e.sender})}function n(e){e.fragmentModel===p.getFragmentModel()&&e.chunk.bytes&&l.append(g,e.chunk)}var a,s=this.context,u=(0,i.default)(s).getInstance(),l=e.sourceBufferController,d=e.errHandler,f=void 0,c=void 0,g=void 0,h=void 0,p=void 0;return a={initialize:function(e,r,n){h=e,t(r),(p=n).getRepresentationController()},createBuffer:function(e){try{g=l.createSourceBuffer(c,e),f||(g.hasOwnProperty("initialize")&&g.initialize(h,this),f=!0)}catch(e){d.mediaSourceError("Error creating "+h+" source buffer.")}return g},getBuffer:function(){return g},setBuffer:function(e){g=e},getStreamProcessor:function(){return p},setMediaSource:t,reset:function(e){u.off(o.default.DATA_UPDATE_COMPLETED,r,this),u.off(o.default.INIT_FRAGMENT_LOADED,n,this),e||(l.abort(c,g),l.removeSourceBuffer(c,g))}},function(){f=!1,c=null,g=null,h=null,p=null,u.on(o.default.DATA_UPDATE_COMPLETED,r,this),u.on(o.default.INIT_FRAGMENT_LOADED,n,this)}(),a}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/EventBus.js")),o=n(e("../../core/events/Events.js")),s=n(e("../../core/FactoryMaker.js"));a.__dashjs_factory_name="TextController",r.default=s.default.getClassFactory(a),t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11}],62:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e){T=e}function t(e){E=e}function r(e){var t=Date.parse(e);return isNaN(t)&&(t=function(e){var t,r,n=/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2})(?::([0-9]*)(\.[0-9]*)?)?(?:([+\-])([0-9]{2})([0-9]{2}))?/.exec(e);return t=Date.UTC(parseInt(n[1],10),parseInt(n[2],10)-1,parseInt(n[3],10),parseInt(n[4],10),parseInt(n[5],10),n[6]&&(parseInt(n[6],10)||0),n[7]&&1e3*parseFloat(n[7])||0),n[9]&&n[10]&&(r=60*parseInt(n[9],10)+parseInt(n[10],10),t+=("+"===n[8]?-1:1)*r*60*1e3),new Date(t).getTime()}(e)),t}function n(e){return Date.parse(e)}function a(e){return Date.parse(e)}function u(e,t,r){r()}function c(e,t,n){var a=r(e);return isNaN(a)?void n():void t(a)}function g(e,t,r,n,a){var i,o,s=!1,u=new XMLHttpRequest,l=a?"HEAD":"GET",d=t.match(/\S+/g);t=d.shift(),i=function(){s||(s=!0,d.length?g(e,d.join(" "),r,n,a):n())},o=function(){var t,n;200===u.status&&(t=a?u.getResponseHeader("Date"):u.response,n=e(t),isNaN(n)||(r(n),s=!0))},u.open(l,t),u.timeout=f||0,u.onload=o,u.onloadend=i,u.send()}function h(e,t,r){g(a,e,t,r,!0)}function p(t,r,n){e(!1),_.trigger(s.default.TIME_SYNCHRONIZATION_COMPLETED,{time:r,offset:n,error:t?new i.default(d):null})}function m(r,n){var a=n||0,i=r[a],o=function(e,r){var n=!e||!r;n&&M?function(){var e=R.getReadOnlyMetricsFor("stream"),r=I.getLatestMPDRequestHeaderValueByID(e,"Date"),n=null!==r?new Date(r).getTime():Number.NaN;isNaN(n)?p(!0):(t(n-(new Date).getTime()),p(!1,n/1e3,E))}():p(n,e,r)};e(!0),i?S.hasOwnProperty(i.schemeIdUri)?S[i.schemeIdUri](i.value,function(e){var r=(new Date).getTime(),n=e-r;t(n),y("Local time:      "+new Date(r)),y("Server time:     "+new Date(e)),y("Difference (ms): "+n),o(e,n)},function(){m(r,a+1)}):m(r,a+1):(t(0),o())}var v=this.context,y=(0,l.default)(v).getInstance().log,_=(0,o.default)(v).getInstance(),E=void 0,T=void 0,M=void 0,S=void 0,R=void 0,I=void 0;return{initialize:function(e,t){M=t,E=0,T=!1,S={"urn:mpeg:dash:utc:http-head:2014":h,"urn:mpeg:dash:utc:http-xsdate:2014":g.bind(null,r),"urn:mpeg:dash:utc:http-iso:2014":g.bind(null,n),"urn:mpeg:dash:utc:direct:2014":c,"urn:mpeg:dash:utc:http-head:2012":h,"urn:mpeg:dash:utc:http-xsdate:2012":g.bind(null,r),"urn:mpeg:dash:utc:http-iso:2012":g.bind(null,n),"urn:mpeg:dash:utc:direct:2012":c,"urn:mpeg:dash:utc:http-ntp:2014":u,"urn:mpeg:dash:utc:ntp:2014":u,"urn:mpeg:dash:utc:sntp:2014":u},T||m(e)},getOffsetToDeviceTimeMs:function(){return E},setConfig:function(e){e&&(e.metricsModel&&(R=e.metricsModel),e.dashMetrics&&(I=e.dashMetrics))},reset:function(){e(!1)}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./../vo/Error.js")),o=n(e("./../../core/EventBus.js")),s=n(e("./../../core/events/Events.js")),u=n(e("../../core/FactoryMaker.js")),l=n(e("../../core/Debug.js")),d=1,f=5e3;a.__dashjs_factory_name="TimeSyncController";var c=u.default.getSingletonFactory(a);c.TIME_SYNC_FAILED_ERROR_CODE=d,c.HTTP_TIMEOUT_MS=f,r.default=c,t.exports=r.default},{"../../core/Debug.js":7,"../../core/FactoryMaker.js":9,"./../../core/EventBus.js":8,"./../../core/events/Events.js":11,"./../vo/Error.js":149}],63:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e,t,r){var a,i,o,s={};for(s.elements=e,s.type=t,s.resolveType=r,0===s.elements.length&&n(s),o=0;o<s.elements.length;o++)i=-1!==(a=s.elements[o]).url.indexOf("http://")?a.url:a.originalContent.BaseURL+a.url,R.load(i,a,s)}function r(e){var t,r,a,i="";t=e.element,r=e.resolveObject,t.resolvedContent&&(a=t.resolvedContent.indexOf(">")+1,i=t.resolvedContent.substr(0,a)+"<response>"+t.resolvedContent.substr(a)+"</response>",t.resolvedContent=S.xml_str2json(i)),function(e){var t;for(t=0;t<e.elements.length;t++)if(!1===e.elements[t].resolved)return!1;return!0}(r)&&n(r)}function n(e){var r,n,i=[];if(function(e){var t,r,n,a,i,o,s=[];for(a=e.elements.length-1;a>=0;a--){if(r=(t=e.elements[a]).type+"_asArray",!t.resolvedContent||m())delete t.originalContent["xlink:actuate"],delete t.originalContent["xlink:href"],s.push(t.originalContent);else if(t.resolvedContent)for(i=0;i<t.resolvedContent[r].length;i++)n=t.resolvedContent[r][i],s.push(n);for(t.parentElement[r].splice(t.index,1),o=0;o<s.length;o++)t.parentElement[r].splice(t.index+o,0,s[o]);s=[]}e.elements.length>0&&T.run(M)}(e),e.resolveType===f&&y.trigger(s.default.XLINK_READY,{manifest:M}),e.resolveType===d)switch(e.type){case c:for(r=0;r<M[c+"_asArray"].length;r++)(n=M[c+"_asArray"][r]).hasOwnProperty(g+"_asArray")&&(i=i.concat(a(n[g+"_asArray"],n,g,d))),n.hasOwnProperty(h+"_asArray")&&(i=i.concat(a(n[h+"_asArray"],n,h,d)));t(i,g,d);break;case g:y.trigger(s.default.XLINK_READY,{manifest:M})}}function a(e,t,r,n){var a,i,o,s=[];for(i=e.length-1;i>=0;i--)(a=e[i]).hasOwnProperty("xlink:href")&&a["xlink:href"]===p&&e.splice(i,1);for(i=0;i<e.length;i++)(a=e[i]).hasOwnProperty("xlink:href")&&a.hasOwnProperty("xlink:actuate")&&a["xlink:actuate"]===n&&(o=u(a["xlink:href"],t,r,i,n,a),s.push(o));return s}function u(e,t,r,n,a,i){return{url:e,parentElement:t,type:r,index:n,resolveType:a,originalContent:i,resolvedContent:null,resolved:!1}}function m(){return!1}var v=this.context,y=(0,o.default)(v).getInstance(),_=void 0,E=void 0,T=void 0,M=void 0,S=void 0,R=void 0;return _={resolveManifestOnLoad:function(e){S=new l.default(E,"",!0),t(a((M=e).Period_asArray,M,c,d),c,d)},setMatchers:function(e){E=e},setIron:function(e){T=e},reset:function(){y.off(s.default.XLINK_ELEMENT_LOADED,r,_),R&&(R.reset(),R=null)}},y.on(s.default.XLINK_ELEMENT_LOADED,r,_),R=(0,i.default)(v).create({errHandler:e.errHandler,metricsModel:e.metricsModel,requestModifier:e.requestModifier}),_}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../XlinkLoader.js")),o=n(e("../../core/EventBus.js")),s=n(e("../../core/events/Events.js")),u=n(e("../../core/FactoryMaker.js")),l=n(e("../../../externals/xml2json.js")),d="onLoad",f="onActuate",c="Period",g="AdaptationSet",h="EventStream",p="urn:mpeg:dash:resolve-to-zero:2013";a.__dashjs_factory_name="XlinkController",r.default=u.default.getClassFactory(a),t.exports=r.default},{"../../../externals/xml2json.js":4,"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../XlinkLoader.js":48}],64:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e=this.context;return{createMetricsReporting:function(t){return(0,i.default)(e).getInstance({eventBus:t.eventBus,metricsModel:t.metricsModel}),(0,s.default)(e).create(t)},getReportingFactory:function(){return(0,l.default)(e).getInstance()},getMetricsHandlerFactory:function(){return(0,u.default)(e).getInstance()}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./utils/DVBErrorsTranslator.js")),o=n(e("./MetricsReportingEvents.js")),s=n(e("./controllers/MetricsCollectionController.js")),u=n(e("./metrics/MetricsHandlerFactory.js")),l=n(e("./reporting/ReportingFactory.js")),d=n(e("../../core/FactoryMaker.js"));a.__dashjs_factory_name="MetricsReporting";var f=d.default.getClassFactory(a);f.events=o.default,r.default=f,t.exports=r.default},{"../../core/FactoryMaker.js":9,"./MetricsReportingEvents.js":65,"./controllers/MetricsCollectionController.js":66,"./metrics/MetricsHandlerFactory.js":71,"./reporting/ReportingFactory.js":76,"./utils/DVBErrorsTranslator.js":78}],65:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){function t(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,t),function(e,t,r){for(var n=!0;n;){var a=e,i=t,o=r;n=!1,null===a&&(a=Function.prototype);var s=Object.getOwnPropertyDescriptor(a,i);if(void 0!==s){if("value"in s)return s.value;var u=s.get;if(void 0===u)return;return u.call(o)}var l=Object.getPrototypeOf(a);if(null===l)return;e=l,t=i,r=o,n=!0,s=l=void 0}}(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),this.METRICS_INITIALISATION_COMPLETE="internal_metricsReportingInitialized",this.BECAME_REPORTING_PLAYER="internal_becameReportingPlayer"}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/events/EventsBase.js")).default),a=new n;r.default=a,t.exports=r.default},{"../../core/events/EventsBase.js":12}],66:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t={},r=this.context,n=e.eventBus;return n.on(l.default.MANIFEST_UPDATED,function(a){if(!a.error){var s=Object.keys(t);(0,o.default)(r).getInstance({dashManifestModel:e.dashManifestModel}).getMetrics(a.manifest).forEach(function(n){var a=JSON.stringify(n);if(t.hasOwnProperty(a))s.splice(a,1);else try{var o=(0,i.default)(r).create(e);o.initialize(n),t[a]=o}catch(e){}}),s.forEach(function(e){t[e].reset(),delete t[e]}),n.trigger(u.default.METRICS_INITIALISATION_COMPLETE)}}),n.on(l.default.STREAM_TEARDOWN_COMPLETE,function(){Object.keys(t).forEach(function(e){t[e].reset()}),t={}}),{}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./MetricsController.js")),o=n(e("../utils/ManifestParsing.js")),s=n(e("../../../core/FactoryMaker.js")),u=n(e("../MetricsReportingEvents.js")),l=n(e("../../../core/events/Events.js"));a.__dashjs_factory_name="MetricsCollectionController",r.default=s.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../../core/events/Events.js":11,"../MetricsReportingEvents.js":65,"../utils/ManifestParsing.js":80,"./MetricsController.js":67}],67:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(){r&&r.reset(),n&&n.reset(),a&&a.reset()}var r=void 0,n=void 0,a=void 0,i=this.context;return{initialize:function(l){try{(a=(0,o.default)(i).create({mediaElement:e.mediaElement})).initialize(l.Range),(n=(0,s.default)(i).create({log:e.log})).initialize(l.Reporting,a),(r=(0,u.default)(i).create({log:e.log,eventBus:e.eventBus})).initialize(l.metrics,n)}catch(e){throw t(),e}},reset:t}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../core/FactoryMaker.js")),o=n(e("./RangeController.js")),s=n(e("./ReportingController.js")),u=n(e("./MetricsHandlersController.js"));a.__dashjs_factory_name="MetricsController",r.default=i.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"./MetricsHandlersController.js":68,"./RangeController.js":69,"./ReportingController.js":70}],68:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e){r.forEach(function(t){t.handleNewMetric(e.metric,e.value,e.mediaType)})}var r=[],n=void 0,a=this.context,o=e.eventBus,u=(0,i.default)(a).getInstance({log:e.log,eventBus:e.eventBus});return n={initialize:function(e,a){e.split(",").forEach(function(e,t,n){var i;if(-1!==e.indexOf("(")&&-1===e.indexOf(")")){var o=n[t+1];o&&-1===o.indexOf("(")&&-1!==o.indexOf(")")&&(e+=","+o,delete n[t+1])}(i=u.create(e,a))&&r.push(i)}),o.on(s.default.METRIC_ADDED,t,n),o.on(s.default.METRIC_UPDATED,t,n)},reset:function(){o.off(s.default.METRIC_ADDED,t,n),o.off(s.default.METRIC_UPDATED,t,n),r.forEach(function(e){return e.reset()}),r=[]}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../metrics/MetricsHandlerFactory.js")),o=n(e("../../../core/FactoryMaker.js")),s=n(e("../../MediaPlayerEvents.js"));a.__dashjs_factory_name="MetricsHandlersController",r.default=o.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../MediaPlayerEvents.js":40,"../metrics/MetricsHandlerFactory.js":71}],69:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t,r=!1,n=this.context,a=void 0,i=e.mediaElement;return t={initialize:function(e){e&&e.length&&(e.forEach(function(e){var t=e.starttime,r=t+e.duration;a.add(t,r)}),r=!!e[0]._useWallClockTime)},reset:function(){a.clear()},isEnabled:function(){var e,t=a.length;if(!t)return!0;e=r?(new Date).getTime()/1e3:i.currentTime;for(var n=0;t>n;n+=1){var o=a.start(n),s=a.end(n);if(e>=o&&s>e)return!0}return!1}},a=(0,o.default)(n).create(),t}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../core/FactoryMaker.js")),o=n(e("../../utils/CustomTimeRanges.js"));a.__dashjs_factory_name="RangeController",r.default=i.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../utils/CustomTimeRanges.js":137}],70:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t=[],r=(0,o.default)(this.context).getInstance({log:e.log});return{initialize:function(e,n){e.some(function(e){var a=r.create(e,n);return a?(t.push(a),!0):void 0})},reset:function(){t.forEach(function(e){return e.reset()}),t=[]},report:function(e,r){t.forEach(function(t){return t.report(e,r)})}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../core/FactoryMaker.js")),o=n(e("../reporting/ReportingFactory.js"));a.__dashjs_factory_name="ReportingController",r.default=i.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../reporting/ReportingFactory.js":76}],71:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t=e.log,r=/([a-zA-Z]*)(\(([0-9]*)(\,\s*([a-zA-Z]*))?\))?/,n=this.context,a={BufferLevel:o.default,DVBErrors:s.default,HttpList:u.default,PlayList:l.default,RepSwitchList:l.default,TcpList:l.default};return{create:function(i,o){var s,u=i.match(r);if(u){try{(s=a[u[1]](n).create({eventBus:e.eventBus})).initialize(u[1],o,u[3],u[5])}catch(e){s=null,t("MetricsHandlerFactory: Could not create handler for type "+u[1]+" with args "+u[3]+", "+u[5]+" ("+e.message+")")}return s}},register:function(e,t){a[e]=t},unregister:function(e){delete a[e]}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../core/FactoryMaker.js")),o=n(e("./handlers/BufferLevelHandler.js")),s=n(e("./handlers/DVBErrorsHandler.js")),u=n(e("./handlers/HttpListHandler.js")),l=n(e("./handlers/GenericMetricHandler.js"));a.__dashjs_factory_name="MetricsHandlerFactory",r.default=i.default.getSingletonFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"./handlers/BufferLevelHandler.js":72,"./handlers/DVBErrorsHandler.js":73,"./handlers/GenericMetricHandler.js":74,"./handlers/HttpListHandler.js":75}],72:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){var e=function(){try{return Object.keys(l).map(function(e){return l[e]}).reduce(function(e,t){return e.level<t.level?e:t})}catch(e){return}}();e&&i!==e.t&&(i=e.t,t.report(n,e))}var t=void 0,r=void 0,n=void 0,a=void 0,i=void 0,s=this.context,u=(0,o.default)(s).getInstance(),l=[];return{initialize:function(i,o,s){o&&(r=u.validateN(s),t=o,n=u.reconstructFullMetricName(i,s),a=setInterval(e,r))},reset:function(){clearInterval(a),a=null,r=0,t=null,i=null},handleNewMetric:function(e,t,r){"BufferLevel"===e&&(l[r]=t)}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../../core/FactoryMaker.js")),o=n(e("../../utils/HandlerHelpers.js"));a.__dashjs_factory_name="BufferLevelHandler",r.default=i.default.getClassFactory(a),t.exports=r.default},{"../../../../core/FactoryMaker.js":9,"../../utils/HandlerHelpers.js":79}],73:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(r,"__esModule",{value:!0});var a=n(e("../../../../core/FactoryMaker.js")),i=n(e("../../MetricsReportingEvents.js"));r.default=a.default.getClassFactory(function(e){function t(){n.off(i.default.METRICS_INITIALISATION_COMPLETE,t,this),n.trigger(i.default.BECAME_REPORTING_PLAYER)}var r=void 0,n=e.eventBus;return{initialize:function(e,a){a&&(r=a,n.on(i.default.METRICS_INITIALISATION_COMPLETE,t,this))},reset:function(){r=null},handleNewMetric:function(e,t){"DVBErrors"===e&&r&&r.report(e,t)}}}),t.exports=r.default},{"../../../../core/FactoryMaker.js":9,"../../MetricsReportingEvents.js":65}],74:[function(e,t,r){"use strict";function n(){var e=void 0,t=void 0;return{initialize:function(r,n){e=r,t=n},reset:function(){t=null,e=void 0},handleNewMetric:function(r,n){r===e&&t&&t.report(e,n)}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../../../core/FactoryMaker.js"));n.__dashjs_factory_name="GenericMetricHandler",r.default=a.default.getClassFactory(n),t.exports=r.default},{"../../../../core/FactoryMaker.js":9}],75:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){var e=s;e.length&&t&&t.report(a,e),s=[]}var t=void 0,r=void 0,n=void 0,a=void 0,i=void 0,s=[],u=(0,o.default)(this.context).getInstance();return{initialize:function(o,s,l,d){s&&(r=u.validateN(l),t=s,d&&d.length&&(n=d),a=u.reconstructFullMetricName(o,l,d),i=setInterval(e,r))},reset:function(){clearInterval(i),i=null,r=null,n=null,s=[],t=null},handleNewMetric:function(e,t){"HttpList"===e&&(n&&n!==t.type||s.push(t))}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../../core/FactoryMaker.js")),o=n(e("../../utils/HandlerHelpers.js"));a.__dashjs_factory_name="HttpListHandler",r.default=i.default.getClassFactory(a),t.exports=r.default},{"../../../../core/FactoryMaker.js":9,"../../utils/HandlerHelpers.js":79}],76:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t={"urn:dvb:dash:reporting:2014":o.default},r=this.context,n=e.log;return{create:function(e,a){var i;try{(i=t[e.schemeIdUri](r).create()).initialize(e,a)}catch(t){i=null,n("ReportingFactory: could not create Reporting with schemeIdUri "+e.schemeIdUri+" ("+t.message+")")}return i},register:function(e,r){t[e]=r},unregister:function(e){delete t[e]}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../core/FactoryMaker.js")),o=n(e("./reporters/DVBReporting.js"));a.__dashjs_factory_name="ReportingFactory",r.default=i.default.getSingletonFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"./reporters/DVBReporting.js":77}],77:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e=this.context,t=(0,o.default)(e).getInstance(),r=(0,s.default)(e).getInstance(),n=!1,a=!1,i=null,u=null,l=[];return{report:function(e,r){Array.isArray(r)||(r=[r]),n&&u.isEnabled()&&r.forEach(function(r){var a=t.serialise(r);"DVBErrors"!==e&&(a="metricname="+e+"&"+a),function(e,t,r){var n=new XMLHttpRequest,a=function(){var e=l.indexOf(n);-1!==e&&(l.splice(e,1),!(n.status>=200&&n.status<300)&&(r&&r()))};l.push(n);try{n.open("GET",e),n.onloadend=a,n.onerror=a,n.send()}catch(e){n.onerror()}}(a=i+"?"+a,0,function(){n=!1})})},initialize:function(e,t){var o;if(u=t,!(i=e["dvb:reportingUrl"]))throw new Error("required parameter missing (dvb:reportingUrl)");a||((o=e["dvb:probability"]||e["dvb:priority"]||0)&&(1e3===o||o/1e3>=r.random())&&(n=!0),a=!0)},reset:function(){a=!1,n=!1,i=null,u=null}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../../core/FactoryMaker.js")),o=n(e("../../utils/MetricSerialiser.js")),s=n(e("../../utils/RNG.js"));a.__dashjs_factory_name="DVBReporting",r.default=i.default.getClassFactory(a),t.exports=r.default},{"../../../../core/FactoryMaker.js":9,"../../utils/MetricSerialiser.js":81,"../../utils/RNG.js":82}],78:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e){var t=new i.default;if(p){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);t.mpdurl||(t.mpdurl=p.originalUrl||p.url),t.terror||(t.terror=new Date),h.addDVBErrors(t)}}function r(e){e.error||(p=e.manifest)}function n(e){t({errorcode:i.default.BASE_URL_CHANGED,servicelocation:e.entry})}function a(){t({errorcode:i.default.BECAME_REPORTER})}function l(e){"HttpList"===e.metric&&function(e){(0===e.responsecode||e.responsecode>=400||e.responsecode<100||e.responsecode>=600)&&t({errorcode:e.responsecode||i.default.CONNECTION_ERROR,url:e.url,terror:e.tresponse,servicelocation:e._serviceLocation})}(e.value)}function d(e){var r;switch(e.error?e.error.code:0){case MediaError.MEDIA_ERR_NETWORK:r=i.default.CONNECTION_ERROR;break;case MediaError.MEDIA_ERR_DECODE:r=i.default.CORRUPT_MEDIA_OTHER;break;default:return}t({errorcode:r})}function f(){g.on(o.default.MANIFEST_UPDATED,r,c),g.on(o.default.SERVICE_LOCATION_BLACKLIST_CHANGED,n,c),g.on(s.default.METRIC_ADDED,l,c),g.on(s.default.METRIC_UPDATED,l,c),g.on(s.default.PLAYBACK_ERROR,d,c),g.on(u.default.BECAME_REPORTING_PLAYER,a,c)}var c=void 0,g=e.eventBus,h=e.metricsModel,p=void 0;return c={initialise:f,reset:function(){g.off(o.default.MANIFEST_UPDATED,r,c),g.off(o.default.SERVICE_LOCATION_BLACKLIST_CHANGED,n,c),g.off(s.default.METRIC_ADDED,l,c),g.off(s.default.METRIC_UPDATED,l,c),g.off(s.default.PLAYBACK_ERROR,d,c),g.off(u.default.BECAME_REPORTING_PLAYER,a,c)}},f(),c}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../vo/DVBErrors.js")),o=n(e("../../../core/events/Events.js")),s=n(e("../../MediaPlayerEvents.js")),u=n(e("../MetricsReportingEvents.js")),l=n(e("../../../core/FactoryMaker.js"));a.__dashjs_factory_name="DVBErrorsTranslator",r.default=l.default.getSingletonFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../../core/events/Events.js":11,"../../MediaPlayerEvents.js":40,"../MetricsReportingEvents.js":65,"../vo/DVBErrors.js":83}],79:[function(e,t,r){"use strict";function n(){return{reconstructFullMetricName:function(e,t,r){var n=e;return t&&(n+="("+t,r&&r.length&&(n+=","+r),n+=")"),n},validateN:function(e){if(!e)throw"missing n";if(isNaN(e))throw"n is NaN";if(0>e)throw"n must be positive";return e}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../../core/FactoryMaker.js"));n.__dashjs_factory_name="HandlerHelpers",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../../core/FactoryMaker.js":9}],80:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t=e.dashManifestModel;return{getMetrics:function(e){var r=[];return e.Metrics_asArray&&e.Metrics_asArray.forEach(function(n){var a=new i.default,u=t.getIsDynamic(e);n.hasOwnProperty("metrics")&&(a.metrics=n.metrics,n.Range_asArray&&n.Range_asArray.forEach(function(r){var n=new o.default;n.starttime=function(e,r,n){var a,i,o=t.getMpd(e),s=0;return r?s=o.availabilityStartTime.getTime()/1e3:(a=this.getRegularPeriods(e,o)).length&&(s=a[0].start),i=s,n&&n.hasOwnProperty("starttime")&&(i+=n.starttime),i}(e,u,r),r.hasOwnProperty("duration")?n.duration=r.duration:n.duration=t.getDuration(e),n._useWallClockTime=u,a.Range.push(n)}),n.Reporting_asArray&&(n.Reporting_asArray.forEach(function(e){var t=new s.default;if(e.hasOwnProperty("schemeIdUri")){for(var r in t.schemeIdUri=e.schemeIdUri,e)e.hasOwnProperty(r)&&(t[r]=e[r]);a.Reporting.push(t)}}),r.push(a)))}),r}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../vo/Metrics.js")),o=n(e("../vo/Range.js")),s=n(e("../vo/Reporting.js")),u=n(e("../../../core/FactoryMaker.js"));a.__dashjs_factory_name="ManifestParsing",r.default=u.default.getSingletonFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../vo/Metrics.js":84,"../vo/Range.js":85,"../vo/Reporting.js":86}],81:[function(e,t,r){"use strict";function n(){return{serialise:function e(t){var r,n,a=[],i=[];for(r in t)if(t.hasOwnProperty(r)&&0!==r.indexOf("_")){if(null==(n=t[r])&&(n=""),Array.isArray(n)){if(!n.length)continue;i=[],n.forEach(function(t){var r="Object"!==Object.prototype.toString.call(t).slice(8,-1);i.push(r?t:e(t))}),n=encodeURIComponent(i.join(","))}else"string"==typeof n?n=encodeURIComponent(n):n instanceof Date?n=n.toISOString():"number"==typeof n&&(n=Math.round(n));a.push(r+"="+n)}return a.join("&")}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../../core/FactoryMaker.js"));n.__dashjs_factory_name="MetricSerialiser",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../../core/FactoryMaker.js":9}],82:[function(e,t,r){"use strict";function n(){function e(){r&&(o||(o=new n(i)),r.getRandomValues(o),s=0)}var t,r=window.crypto||window.msCrypto,n=Uint32Array,a=Math.pow(2,8*n.BYTES_PER_ELEMENT)-1,i=10,o=void 0,s=void 0;return t={random:function(t,n){var i;return t||(t=0),n||(n=1),r?(s===o.length&&e(),i=o[s]/a,s+=1):i=Math.random(),i*(n-t)+t}},e(),t}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../../core/FactoryMaker.js"));n.__dashjs_factory_name="RNG",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../../core/FactoryMaker.js":9}],83:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.mpdurl=null,this.errorcode=null,this.terror=null,this.url=null,this.ipaddress=null,this.servicelocation=null};n.SSL_CONNECTION_FAILED_PREFIX="SSL",n.DNS_RESOLUTION_FAILED="C00",n.HOST_UNREACHABLE="C01",n.CONNECTION_REFUSED="C02",n.CONNECTION_ERROR="C03",n.CORRUPT_MEDIA_ISOBMFF="M00",n.CORRUPT_MEDIA_OTHER="M01",n.BASE_URL_CHANGED="F00",n.BECAME_REPORTER="S00",r.default=n,t.exports=r.default},{}],84:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.metrics="",this.Range=[],this.Reporting=[]},t.exports=r.default},{}],85:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.starttime=0,this.duration=1/0,this._useWallClockTime=!1},t.exports=r.default},{}],86:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.schemeIdUri="",this.value=""},t.exports=r.default},{}],87:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e,t,r){var n=s.getBaseURLsFromElement(r);e[t]?d.areSimpleEquivalent(n,e[t].data.baseUrls)||(e[t].data.baseUrls=n,e[t].data.selectedIdx=u):e[t]=new l(n)}function t(e,r){var a=r||n;e(a.data),a.children&&a.children.forEach(function(r){return t(e,r)})}var r,n=void 0,a=this.context,s=(0,i.default)(a).getInstance(),d=(0,o.default)(a).getInstance();return r={reset:function(){n=new l},update:function(t){!function(t){var r=s.getBaseURLsFromElement(t);d.areSimpleEquivalent(r,n.data.baseUrls)||(n.data.baseUrls=r,n.data.selectedIdx=u),t.Period_asArray&&t.Period_asArray.forEach(function(t,r){e(n.children,r,t),t.AdaptationSet_asArray&&t.AdaptationSet_asArray.forEach(function(t,a){e(n.children[r].children,a,t),t.Representation_asArray&&t.Representation_asArray.sort(s.getRepresentationSortFunction()).forEach(function(t,i){e(n.children[r].children[a].children,i,t)})})})}(t)},getForPath:function(e){var t=n,r=[t.data];return e.forEach(function(e){(t=t.children[e])&&r.push(t.data)}),r.filter(function(e){return e.baseUrls.length})},invalidateSelectedIndexes:function(e){t(function(t){isNaN(t.selectedIdx)||e===t.baseUrls[t.selectedIdx].serviceLocation&&(t.selectedIdx=u)})}},n=new l,r}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../dash/models/DashManifestModel.js")),o=n(e("../utils/ObjectUtils.js")),s=n(e("../../core/FactoryMaker.js")),u=NaN,l=function e(t,r){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.data={baseUrls:t||null,selectedIdx:r||u},this.children=[]};a.__dashjs_factory_name="BaseURLTreeModel",r.default=s.default.getClassFactory(a),t.exports=r.default},{"../../core/FactoryMaker.js":9,"../../dash/models/DashManifestModel.js":19,"../utils/ObjectUtils.js":142}],88:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e,t){var r=e.indexOf(t);-1!==r&&e.splice(r,1)}function r(e,t,r){var n,a=NaN,i=NaN,o=null;for(n=e.length-1;n>=0;n--)if(i=(a=(o=e[n]).startTime)+o.duration,r=r||o.duration/2,!isNaN(a)&&!isNaN(i)&&t+r>=a&&i>t-r||isNaN(a)&&isNaN(t))return o;return null}function n(e,t){return t?t.hasOwnProperty("time")?[r(e,t.time,t.threshold)]:e.filter(function(e){for(var r in t)if("state"!==r&&t.hasOwnProperty(r)&&e[r]!=t[r])return!1;return!0}):e}function a(e){var t;switch(e){case d:t=T;break;case f:t=E;break;default:t=[]}return t}function s(e,t){if(e){var r=e.mediaType,n=new Date,a=e.type,i=e.startTime,o=e.availabilityStartTime,s=e.duration,u=e.quality,l=e.range;v.addSchedulingInfo(r,n,a,i,o,s,u,l,t),v.addRequestsQueue(r,T,E)}}function g(e){if(e.sender===M){var t=e.request,r=e.response,n=e.error;T.splice(T.indexOf(t),1),r&&!n&&E.push(t),s(t,n?c:f),m.trigger(o.default.FRAGMENT_LOADING_COMPLETED,{request:t,response:r,error:n,sender:this})}}var h=this.context,p=(0,l.default)(h).getInstance().log,m=(0,i.default)(h).getInstance(),v=e.metricsModel,y=void 0,_=void 0,E=void 0,T=void 0,M=void 0;return y={setLoader:function(e){M=e},setScheduleController:function(e){_=e},getScheduleController:function(){return _},getRequests:function(e){var t,r=[],i=[],o=1;if(!e||!e.state)return r;e.state instanceof Array?(o=e.state.length,t=e.state):t=[e.state];for(var s=0;o>s;s++)r=a(t[s]),i=i.concat(n(r,e));return i},isFragmentLoaded:function(e){var t=function(e,t){return e.action===u.default.ACTION_COMPLETE&&e.action===t.action},r=function(e,t){return!isNaN(e.index)&&e.index===t.index&&e.startTime===t.startTime&&e.adaptationIndex===t.adaptationIndex},n=function(e,t){return isNaN(e.index)&&isNaN(t.index)&&e.quality===t.quality},a=function(a){var i,o,s=!1,u=a.length;for(o=0;u>o;o++)if(i=a[o],r(e,i)||n(e,i)||t(e,i)){s=!0;break}return s};return a(T)||a(E)},removeExecutedRequestsBeforeTime:function(e){var r,n=NaN,a=null;for(r=E.length-1;r>=0;r--)n=(a=E[r]).startTime,!isNaN(n)&&e>n&&t(E,a)},abortRequests:function(){var e=[];for(M.abort();T.length>0;)e.push(T[0]),t(T,T[0]);return T=[],e},executeRequest:function(e){if(e)switch(e.action){case u.default.ACTION_COMPLETE:E.push(e),s(e,f),m.trigger(o.default.STREAM_COMPLETED,{request:e,fragmentModel:this});break;case u.default.ACTION_DOWNLOAD:s(e,d),T.push(e),function(e){m.trigger(o.default.FRAGMENT_LOADING_STARTED,{sender:y,request:e}),M.load(e)}(e);break;default:p("Unknown request action.")}},reset:function(){m.off(o.default.LOADING_COMPLETED,g,this),M&&(M.reset(),M=null),h=null,E=[],T=[]}},_=null,M=null,E=[],T=[],m.on(o.default.LOADING_COMPLETED,g,y),y}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/EventBus.js")),o=n(e("../../core/events/Events.js")),s=n(e("../../core/FactoryMaker.js")),u=n(e("../vo/FragmentRequest.js")),l=n(e("../../core/Debug.js")),d="loading",f="executed",c="failed";a.__dashjs_factory_name="FragmentModel";var g=s.default.getClassFactory(a);g.FRAGMENT_MODEL_LOADING=d,g.FRAGMENT_MODEL_EXECUTED=f,g.FRAGMENT_MODEL_CANCELED="canceled",g.FRAGMENT_MODEL_FAILED=c,r.default=g,t.exports=r.default},{"../../core/Debug.js":7,"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../vo/FragmentRequest.js":150}],89:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e=this.context,t=(0,i.default)(e).getInstance(),r=void 0;return{getValue:function(){return r},setValue:function(e){r=e,t.trigger(o.default.MANIFEST_LOADED,{data:e})}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/EventBus.js")),o=n(e("../../core/events/Events.js")),s=n(e("../../core/FactoryMaker.js"));a.__dashjs_factory_name="ManifestModel",r.default=s.default.getSingletonFactory(a),t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11}],90:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i(){var e,t=void 0,r=void 0,n=void 0,i=void 0,o=void 0,A=void 0,j=void 0,D=void 0,b=void 0,O=void 0,P=void 0,w=void 0,N=void 0,x=void 0,L=void 0,F=void 0,B=void 0,k=void 0,U=void 0,K=void 0,H=void 0;return e={setBufferOccupancyABREnabled:function(e){H=e},getBufferOccupancyABREnabled:function(){return H},setBandwidthSafetyFactor:function(e){F=e},getBandwidthSafetyFactor:function(){return F},setAbandonLoadTimeout:function(e){B=e},getAbandonLoadTimeout:function(){return B},setLastBitrateCachingInfo:function(e,t){b.enabled=e,void 0===t||isNaN(t)||"number"!=typeof t||(b.ttl=t)},getLastBitrateCachingInfo:function(){return b},setLastMediaSettingsCachingInfo:function(e,t){O.enabled=e,void 0===t||isNaN(t)||"number"!=typeof t||(O.ttl=t)},getLastMediaSettingsCachingInfo:function(){return O},setStableBufferTime:function(e){P=e},getStableBufferTime:function(){return P},setBufferTimeAtTopQuality:function(e){w=e},getBufferTimeAtTopQuality:function(){return w},setBufferTimeAtTopQualityLongForm:function(e){N=e},getBufferTimeAtTopQualityLongForm:function(){return N},setLongFormContentDurationThreshold:function(e){x=e},getLongFormContentDurationThreshold:function(){return x},setRichBufferThreshold:function(e){L=e},getRichBufferThreshold:function(){return L},setBufferToKeep:function(e){j=e},getBufferToKeep:function(){return j},setBufferPruningInterval:function(e){D=e},getBufferPruningInterval:function(){return D},setFragmentRetryAttempts:function(e){k[s.default.MEDIA_SEGMENT_TYPE]=e},getFragmentRetryAttempts:function(){return k[s.default.MEDIA_SEGMENT_TYPE]},setRetryAttemptsForType:function(e,t){k[e]=t},getRetryAttemptsForType:function(e){return k[e]},setFragmentRetryInterval:function(e){U[s.default.MEDIA_SEGMENT_TYPE]=e},getFragmentRetryInterval:function(){return U[s.default.MEDIA_SEGMENT_TYPE]},setRetryIntervalForType:function(e,t){U[e]=t},getRetryIntervalForType:function(e){return U[e]},setWallclockTimeUpdateInterval:function(e){K=e},getWallclockTimeUpdateInterval:function(){return K},setScheduleWhilePaused:function(e){A=e},getScheduleWhilePaused:function(){return A},getUseSuggestedPresentationDelay:function(){return r},setUseSuggestedPresentationDelay:function(e){r=e},setLiveDelayFragmentCount:function(e){i=e},getLiveDelayFragmentCount:function(){return i},getLiveDelay:function(){return o},setLiveDelay:function(e){o=e},setUseManifestDateHeaderTimeSource:function(e){t=e},getUseManifestDateHeaderTimeSource:function(){return t},setUTCTimingSources:function(e){n=e},getUTCTimingSources:function(){return n},reset:function(){}},function(){var e,q;n=[],r=!1,t=!0,A=!0,H=!1,b={enabled:!0,ttl:l},O={enabled:!0,ttl:d},i=u,o=void 0,j=g,D=h,P=p,w=m,N=v,x=y,L=_,F=f,B=c,K=C,a(e={},s.default.MPD_TYPE,M),a(e,s.default.XLINK_EXPANSION_TYPE,R),a(e,s.default.MEDIA_SEGMENT_TYPE,E),a(e,s.default.INIT_SEGMENT_TYPE,E),a(e,s.default.BITSTREAM_SWITCHING_SEGMENT_TYPE,E),a(e,s.default.INDEX_SEGMENT_TYPE,E),a(e,s.default.OTHER_TYPE,E),k=e,a(q={},s.default.MPD_TYPE,S),a(q,s.default.XLINK_EXPANSION_TYPE,I),a(q,s.default.MEDIA_SEGMENT_TYPE,T),a(q,s.default.INIT_SEGMENT_TYPE,T),a(q,s.default.BITSTREAM_SWITCHING_SEGMENT_TYPE,T),a(q,s.default.INDEX_SEGMENT_TYPE,T),a(q,s.default.OTHER_TYPE,T),U=q}(),e}Object.defineProperty(r,"__esModule",{value:!0});var o=n(e("../../core/FactoryMaker.js")),s=n(e("../vo/metrics/HTTPRequest.js")),u=4,l=36e4,d=36e4,f=.9,c=1e4,g=30,h=30,p=12,m=30,v=60,y=600,_=20,E=3,T=1e3,M=3,S=500,R=1,I=500,C=50;i.__dashjs_factory_name="MediaPlayerModel";var A=o.default.getSingletonFactory(i);A.DEFAULT_UTC_TIMING_SOURCE={scheme:"urn:mpeg:dash:utc:http-xsdate:2014",value:"http://time.akamai.com/?iso"},r.default=A,t.exports=r.default},{"../../core/FactoryMaker.js":9,"../vo/metrics/HTTPRequest.js":166}],91:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){T.trigger(v.default.METRICS_CHANGED)}function t(t){T.trigger(v.default.METRIC_CHANGED,{mediaType:t}),e()}function r(e,r,n){T.trigger(v.default.METRIC_UPDATED,{mediaType:e,metric:r,value:n}),t(e)}function n(e,r,n){T.trigger(v.default.METRIC_ADDED,{mediaType:e,metric:r,value:n}),t(e)}function a(e){var t;return S.hasOwnProperty(e)?t=S[e]:(t=new i.default,S[e]=t),t}var y,E=this.context,T=(0,p.default)(E).getInstance(),M=void 0,S=void 0;return y={metricsChanged:e,metricChanged:t,metricUpdated:r,metricAdded:n,clearCurrentMetricsForType:function(e){delete S[e],t(e)},clearAllCurrentMetrics:function(){S={},e()},getReadOnlyMetricsFor:function(e){return S.hasOwnProperty(e)?S[e]:null},getMetricsFor:a,addTcpConnection:function(e,t,r,i,s,u){var l=new o.default;return l.tcpid=t,l.dest=r,l.topen=i,l.tclose=s,l.tconnect=u,a(e).TcpList.push(l),n(e,M.metricsList.TCP_CONNECTION,l),l},addHttpRequest:function e(t,r,i,o,u,l,d,f,c,g,h,p,m,v){var y=new s.default;return u&&u!==o&&(e(t,null,i,o,null,null,d,f,null,null,null,p,null,null),y.actualurl=u),y.tcpid=r,y.type=i,y.url=o,y.range=d,y.trequest=f,y.tresponse=c,y.responsecode=h,y._tfinish=g,y._stream=t,y._mediaduration=p,y._responseHeaders=m,y._serviceLocation=l,v?v.forEach(function(e){!function(e,t,r,n){var a=new s.default.Trace;a.s=t,a.d=r,a.b=n,e.trace.push(a),e.interval||(e.interval=0),e.interval+=r}(y,e.s,e.d,e.b)}):(delete y.interval,delete y.trace),a(t).HttpList.push(y),n(t,M.metricsList.HTTP_REQUEST,y),y},addRepresentationSwitch:function(e,t,r,i,o){var s=new u.default;return s.t=t,s.mt=r,s.to=i,o?s.lto=o:delete s.lto,a(e).RepSwitchList.push(s),n(e,M.metricsList.TRACK_SWITCH,s),s},addBufferLevel:function(e,t,r){var i=new l.default;return i.t=t,i.level=r,a(e).BufferLevel.push(i),n(e,M.metricsList.BUFFER_LEVEL,i),i},addBufferState:function(e,t,r){var i=new d.default;return i.target=r,i.state=t,a(e).BufferState.push(i),n(e,M.metricsList.BUFFER_STATE,i),i},addDVRInfo:function(e,t,r,i){var o=new f.default;return o.time=t,o.range=i,o.manifestInfo=r,a(e).DVRInfo.push(o),n(e,M.metricsList.DVR_INFO,o),o},addDroppedFrames:function(e,t){var r=new c.default,i=a(e).DroppedFrames;return r.time=t.creationTime,r.droppedFrames=t.droppedVideoFrames,i.length>0&&i[i.length-1]==r?i[i.length-1]:(i.push(r),n(e,M.metricsList.DROPPED_FRAMES,r),r)},addSchedulingInfo:function(e,t,r,i,o,s,u,l,d){var f=new h.default;return f.mediaType=e,f.t=t,f.type=r,f.startTime=i,f.availabilityStartTime=o,f.duration=s,f.quality=u,f.range=l,f.state=d,a(e).SchedulingInfo.push(f),n(e,M.metricsList.SCHEDULING_INFO,f),f},addRequestsQueue:function(e,t,r){var i=new m.default;i.loadingRequests=t,i.executedRequests=r,a(e).RequestsQueue=i,n(e,M.metricsList.REQUESTS_QUEUE,i)},addManifestUpdate:function(e,t,r,i,o,s,u,l,d,f){var c=new g.default,h=a("stream");return c.mediaType=e,c.type=t,c.requestTime=r,c.fetchTime=i,c.availabilityStartTime=o,c.presentationStartTime=s,c.clientTimeOffset=u,c.currentTime=l,c.buffered=d,c.latency=f,h.ManifestUpdate.push(c),n(e,M.metricsList.MANIFEST_UPDATE,c),c},updateManifestUpdateInfo:function(e,t){if(e){for(var n in t)e[n]=t[n];r(e.mediaType,M.metricsList.MANIFEST_UPDATE,e)}},addManifestUpdateStreamInfo:function(e,t,n,a,i){if(e){var o=new g.default.StreamInfo;return o.id=t,o.index=n,o.start=a,o.duration=i,e.streamInfo.push(o),r(e.mediaType,M.metricsList.MANIFEST_UPDATE_STREAM_INFO,e),o}return null},addManifestUpdateRepresentationInfo:function(e,t,n,a,i,o,s,u){if(e){var l=new g.default.TrackInfo;return l.id=t,l.index=n,l.streamIndex=a,l.mediaType=i,l.startNumber=s,l.fragmentInfoType=u,l.presentationTimeOffset=o,e.trackInfo.push(l),r(e.mediaType,M.metricsList.MANIFEST_UPDATE_TRACK_INFO,e),l}return null},addPlayList:function(e){var t="stream";return e.trace&&Array.isArray(e.trace)?e.trace.forEach(function(e){e.hasOwnProperty("subreplevel")&&!e.subreplevel&&delete e.subreplevel}):delete e.trace,a(t).PlayList.push(e),n(t,M.metricsList.PLAY_LIST,e),e},addDVBErrors:function(e){var t="stream";return a(t).DVBErrors.push(e),n(t,M.metricsList.DVB_ERRORS,e),e},updateBolaState:function(e,t){var r=new _.default;return r._s=t,a(e).BolaState=[r],n(e,"BolaState",r),r},setConfig:function(e){e&&e.adapter&&(M=e.adapter)}},S={},y}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../vo/MetricsList.js")),o=n(e("../vo/metrics/TCPConnection.js")),s=n(e("../vo/metrics/HTTPRequest.js")),u=n(e("../vo/metrics/RepresentationSwitch.js")),l=n(e("../vo/metrics/BufferLevel.js")),d=n(e("../vo/metrics/BufferState.js")),f=n(e("../vo/metrics/DVRInfo.js")),c=n(e("../vo/metrics/DroppedFrames.js")),g=n(e("../vo/metrics/ManifestUpdate.js")),h=n(e("../vo/metrics/SchedulingInfo.js")),p=n(e("../../core/EventBus.js")),m=n(e("../vo/metrics/RequestsQueue.js")),v=n(e("../../core/events/Events.js")),y=n(e("../../core/FactoryMaker.js")),_=n(e("../vo/metrics/BolaState.js"));a.__dashjs_factory_name="MetricsModel",r.default=y.default.getSingletonFactory(a),t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../vo/MetricsList.js":155,"../vo/metrics/BolaState.js":161,"../vo/metrics/BufferLevel.js":162,"../vo/metrics/BufferState.js":163,"../vo/metrics/DVRInfo.js":164,"../vo/metrics/DroppedFrames.js":165,"../vo/metrics/HTTPRequest.js":166,"../vo/metrics/ManifestUpdate.js":167,"../vo/metrics/RepresentationSwitch.js":169,"../vo/metrics/RequestsQueue.js":170,"../vo/metrics/SchedulingInfo.js":171,"../vo/metrics/TCPConnection.js":172}],92:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e=void 0,t=void 0,r=void 0;return{initialize:function(){e=new i.default,t=[],r=!1},parseURI:function(n){function a(e,t,r,n){var a=n[0].split(/[=]/);return n.push({key:a[0],value:a[1]}),n.shift(),n}if(!n)return null;var i=[],o=new RegExp(/[?]/),s=new RegExp(/[#]/),u=new RegExp(/^(https:)?\/\//i),l=o.test(n),d=s.test(n);return r=u.test(n),n.split(/[?#]/).map(function(e,r,n){return r>0&&(l&&0===t.length?t=n[r].split(/[&]/):d&&(i=n[r].split(/[&]/))),n}),t.length>0&&(t=t.reduce(a,null)),i.length>0&&(i=i.reduce(a,null)).forEach(function(t){e[t.key]=t.value}),n},getURIFragmentData:function(){return e},getURIQueryData:function(){return t},isManifestHTTPS:function(){return r}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../vo/URIFragmentData.js")),o=n(e("../../core/FactoryMaker.js"));a.__dashjs_factory_name="URIQueryAndFragmentModel",r.default=o.default.getSingletonFactory(a),t.exports=r.default},{"../../core/FactoryMaker.js":9,"../vo/URIFragmentData.js":160}],93:[function(e,t,r){"use strict";function n(){function e(e){!n||n.readyState<2||(n.playbackRate=e)}function t(t){var r=void 0;null===t||n.seeking||-1!==o.indexOf(t)||(o.push(t),1===o.length&&((r=document.createEvent("Event")).initEvent("waiting",!0,!1),s=n.playbackRate,e(0),n.dispatchEvent(r)))}function r(t){var r=o.indexOf(t),a=void 0;null!==t&&(-1!==r&&o.splice(r,1),o.length>0==0&&0===n.playbackRate&&((a=document.createEvent("Event")).initEvent("playing",!0,!1),e(s||1),n.dispatchEvent(a)))}var n=void 0,a=void 0,i=void 0,o=void 0,s=void 0;return{initialize:function(){o=[]},setCurrentTime:function(e){if(n.currentTime!=e)try{n.currentTime=e}catch(t){0===n.readyState&&t.code===t.INVALID_STATE_ERR&&setTimeout(function(){n.currentTime=e},400)}},setStallState:function(e,n){!function(e,n){n?t(e):r(e)}(e,n)},getElement:function(){return n},setElement:function(e){(n=e).preload="auto"},setSource:function(e){e?n.src=e:(n.removeAttribute("src"),n.load())},getSource:function(){return n.src},getVideoContainer:function(){return i},setVideoContainer:function(e){i=e},getTTMLRenderingDiv:function(){return a},setTTMLRenderingDiv:function(e){(a=e).style.position="absolute",a.style.display="flex",a.style.overflow="hidden",a.style.pointerEvents="none",a.style.top=0,a.style.left=0},getPlaybackQuality:function(){var e="webkitDroppedFrameCount"in n,t=null;return"getVideoPlaybackQuality"in n?t=n.getVideoPlaybackQuality():e&&(t={droppedVideoFrames:n.webkitDroppedFrameCount,creationTime:new Date}),t}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js"));n.__dashjs_factory_name="VideoModel",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../core/FactoryMaker.js":9}],94:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../../externals/base64.js")),i=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return n(e,null,[{key:"findCencContentProtection",value:function(e){for(var t=null,r=0;r<e.length;++r){var n=e[r];"urn:mpeg:dash:mp4protection:2011"===n.schemeIdUri.toLowerCase()&&"cenc"===n.value.toLowerCase()&&(t=n)}return t}},{key:"getPSSHData",value:function(e){var t=8,r=new DataView(e),n=r.getUint8(t);return t+=20,n>0&&(t+=4+16*r.getUint32(t)),t+=4,e.slice(t)}},{key:"getPSSHForKeySystem",value:function(t,r){var n=e.parsePSSHList(r);return n.hasOwnProperty(t.uuid.toLowerCase())?n[t.uuid.toLowerCase()]:null}},{key:"parseInitDataFromContentProtection",value:function(e){return"pssh"in e?a.default.decodeArray(e.pssh.__text).buffer:null}},{key:"parsePSSHList",value:function(e){if(null===e)return[];for(var t=new DataView(e),r={},n=0;;){var a,i,o,s=n;if(n>=t.buffer.byteLength)break;if(a=n+t.getUint32(n),n+=4,1886614376===t.getUint32(n))if(n+=4,0===(i=t.getUint8(n))||1===i){var u,l;for(n++,n+=3,o="",u=0;4>u;u++)o+=1===(l=t.getUint8(n+u).toString(16)).length?"0"+l:l;for(n+=4,o+="-",u=0;2>u;u++)o+=1===(l=t.getUint8(n+u).toString(16)).length?"0"+l:l;for(n+=2,o+="-",u=0;2>u;u++)o+=1===(l=t.getUint8(n+u).toString(16)).length?"0"+l:l;for(n+=2,o+="-",u=0;2>u;u++)o+=1===(l=t.getUint8(n+u).toString(16)).length?"0"+l:l;for(n+=2,o+="-",u=0;6>u;u++)o+=1===(l=t.getUint8(n+u).toString(16)).length?"0"+l:l;n+=6,o=o.toLowerCase(),t.getUint32(n),n+=4,r[o]=t.buffer.slice(s,a),n=a}else n=a;else n=a}return r}}]),e}();r.default=i,t.exports=r.default},{"../../../externals/base64.js":1}],95:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e){var n=e.log,a=e.eventBus,i=e.videoModel.getElement();return void 0!==i.onencrypted&&void 0!==i.mediaKeys&&void 0!==navigator.requestMediaKeySystemAccess&&"function"==typeof navigator.requestMediaKeySystemAccess?(n("EME detected on this user agent! (ProtectionModel_21Jan2015)"),(0,u.default)(r).create({log:n,eventBus:a})):t(i,g)?(n("EME detected on this user agent! (ProtectionModel_3Feb2014)"),(0,l.default)(r).create({log:n,eventBus:a,api:t(i,g)})):t(i,c)?(n("EME detected on this user agent! (ProtectionModel_01b)"),(0,d.default)(r).create({log:n,eventBus:a,api:t(i,c)})):(n("No supported version of EME detected on this user agent! - Attempts to play encrypted content will fail!"),null)}function t(e,t){for(var r=0;r<t.length;r++){var n=t[r];if("function"==typeof e[n[Object.keys(n)[0]]])return n}return null}var r=this.context;return{createProtectionSystem:function(t){var n=null,a=(0,o.default)(r).getInstance();a.setConfig({log:t.log}),a.initialize();var s=e(t);return!n&&s&&(n=(0,i.default)(r).create({protectionModel:s,protectionKeyController:a,adapter:t.adapter,eventBus:t.eventBus,log:t.log}),t.capabilities.setEncryptedMediaSupported(!0)),n}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./controllers/ProtectionController.js")),o=n(e("./controllers/ProtectionKeyController.js")),s=n(e("./ProtectionEvents.js")),u=n(e("./models/ProtectionModel_21Jan2015.js")),l=n(e("./models/ProtectionModel_3Feb2014.js")),d=n(e("./models/ProtectionModel_01b.js")),f=n(e("../../core/FactoryMaker.js")),c=[{generateKeyRequest:"generateKeyRequest",addKey:"addKey",cancelKeyRequest:"cancelKeyRequest",needkey:"needkey",keyerror:"keyerror",keyadded:"keyadded",keymessage:"keymessage"},{generateKeyRequest:"webkitGenerateKeyRequest",addKey:"webkitAddKey",cancelKeyRequest:"webkitCancelKeyRequest",needkey:"webkitneedkey",keyerror:"webkitkeyerror",keyadded:"webkitkeyadded",keymessage:"webkitkeymessage"}],g=[{setMediaKeys:"setMediaKeys",MediaKeys:"MediaKeys",release:"close",needkey:"needkey",error:"keyerror",message:"keymessage",ready:"keyadded",close:"keyclose"},{setMediaKeys:"msSetMediaKeys",MediaKeys:"MSMediaKeys",release:"close",needkey:"msneedkey",error:"mskeyerror",message:"mskeymessage",ready:"mskeyadded",close:"mskeyclose"}];a.__dashjs_factory_name="Protection";var h=f.default.getClassFactory(a);h.events=s.default,r.default=h,t.exports=r.default},{"../../core/FactoryMaker.js":9,"./ProtectionEvents.js":96,"./controllers/ProtectionController.js":97,"./controllers/ProtectionKeyController.js":98,"./models/ProtectionModel_01b.js":102,"./models/ProtectionModel_21Jan2015.js":103,"./models/ProtectionModel_3Feb2014.js":104}],96:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){function t(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,t),function(e,t,r){for(var n=!0;n;){var a=e,i=t,o=r;n=!1,null===a&&(a=Function.prototype);var s=Object.getOwnPropertyDescriptor(a,i);if(void 0!==s){if("value"in s)return s.value;var u=s.get;if(void 0===u)return;return u.call(o)}var l=Object.getPrototypeOf(a);if(null===l)return;e=l,t=i,r=o,n=!0,s=l=void 0}}(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),this.INTERNAL_KEY_MESSAGE="internalKeyMessage",this.INTERNAL_KEY_SYSTEM_SELECTED="internalKeySystemSelected",this.KEY_ADDED="public_keyAdded",this.KEY_ERROR="public_keyError",this.KEY_MESSAGE="public_keyMessage",this.KEY_SESSION_CLOSED="public_keySessionClosed",this.KEY_SESSION_CREATED="public_keySessionCreated",this.KEY_SESSION_REMOVED="public_keySessionRemoved",this.KEY_STATUSES_CHANGED="public_keyStatusesChanged",this.KEY_SYSTEM_ACCESS_COMPLETE="keySystemAccessComplete",this.KEY_SYSTEM_SELECTED="public_keySystemSelected",this.LICENSE_REQUEST_COMPLETE="public_licenseRequestComplete",this.NEED_KEY="needkey",this.PROTECTION_CREATED="public_protectioncreated",this.PROTECTION_DESTROYED="public_protectiondestroyed",this.SERVER_CERTIFICATE_UPDATED="serverCertificateUpdated",this.TEARDOWN_COMPLETE="protectionTeardownComplete",this.VIDEO_ELEMENT_SELECTED="videoElementSelected"}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/events/EventsBase.js")).default),a=new n;r.default=a,t.exports=r.default},{"../../core/events/EventsBase.js":12}],97:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e){var t=i.default.getPSSHForKeySystem(R,e);if(t){for(var r=h.getAllInitData(),n=0;n<r.length;n++)if(g.initDataEquals(t,r[n]))return void v("DRM: Ignoring initData because we have already seen it!");try{h.createKeySession(t,S)}catch(e){m.trigger(o.default.KEY_SESSION_CREATED,{data:null,error:"Error creating key session! "+e.message})}}else m.trigger(o.default.KEY_SESSION_CREATED,{data:null,error:"Selected key system is "+R.systemString+".  needkey/encrypted event contains no initData corresponding to that key system!"})}function r(e){e?(h.setMediaElement(e),m.on(o.default.NEED_KEY,f,this),m.on(o.default.INTERNAL_KEY_MESSAGE,l,this)):null===e&&(h.setMediaElement(e),m.off(o.default.NEED_KEY,f,this),m.off(o.default.INTERNAL_KEY_MESSAGE,l,this))}function n(e,r){var n=this,a=[],i=[];E&&i.push(new s.default(E.codec)),_&&a.push(new s.default(_.codec));var l,d=new u.default(a,i,"optional","temporary"===S?"optional":"required",[S]),f=[];if(R){for(l=0;l<e.length;l++)if(R===e[l].ks){var c=function(){return f.push({ks:e[l].ks,configs:[d]}),m.on(o.default.KEY_SYSTEM_ACCESS_COMPLETE,function a(i){m.off(o.default.KEY_SYSTEM_ACCESS_COMPLETE,a,n),i.error?r||m.trigger(o.default.KEY_SYSTEM_SELECTED,{error:"DRM: KeySystem Access Denied! -- "+i.error}):(v("DRM: KeySystem Access Granted"),m.trigger(o.default.KEY_SYSTEM_SELECTED,{data:i.data}),t(e[l].initData))},n),h.requestKeySystemAccess(f),"break"}();if("break"===c)break}}else if(void 0===R){R=null,y.push(e);for(var g=0;g<e.length;g++)f.push({ks:e[g].ks,configs:[d]});var p,T=function e(t){m.off(o.default.KEY_SYSTEM_ACCESS_COMPLETE,e,n),t.error?(R=void 0,m.off(o.default.INTERNAL_KEY_SYSTEM_SELECTED,M,n),r||m.trigger(o.default.KEY_SYSTEM_SELECTED,{data:null,error:"DRM: KeySystem Access Denied! -- "+t.error})):(p=t.data,v("DRM: KeySystem Access Granted ("+p.keySystem.systemString+")!  Selecting key system..."),h.selectKeySystem(p))},M=function e(a){if(m.off(o.default.INTERNAL_KEY_SYSTEM_SELECTED,e,n),m.off(o.default.KEY_SYSTEM_ACCESS_COMPLETE,T,n),a.error)R=void 0,r||m.trigger(o.default.KEY_SYSTEM_SELECTED,{data:null,error:"DRM: Error selecting key system! -- "+a.error});else{R=h.getKeySystem(),m.trigger(o.default.KEY_SYSTEM_SELECTED,{data:p});for(var i=0;i<y.length;i++)for(l=0;l<y[i].length;l++)if(R===y[i][l].ks){t(y[i][l].initData);break}}};m.on(o.default.INTERNAL_KEY_SYSTEM_SELECTED,M,n),m.on(o.default.KEY_SYSTEM_ACCESS_COMPLETE,T,n),h.requestKeySystemAccess(f)}else y.push(e)}function a(e,t){m.trigger(o.default.LICENSE_REQUEST_COMPLETE,{data:e,error:t})}function l(e){if(v("DRM: onKeyMessage"),e.error)v(e.error);else{var t=e.data;m.trigger(o.default.KEY_MESSAGE,{data:t});var r=t.messageType?t.messageType:"license-request",n=t.message,s=t.sessionToken,u=function(e){var t=null,r=e.systemString;return T&&(t=r in T?T[r]:null),t}(R),l=R.systemString,d=g.getLicenseServer(R,u,r),f={sessionToken:s,messageType:r};if(!d)return v("DRM: License server request not required for this message (type = "+e.data.messageType+").  Session ID = "+s.getSessionID()),void a(f);if(g.isClearKey(R)){var c=g.processClearKeyLicenseRequest(u,n);if(c)return v("DRM: ClearKey license request handled by application!"),a(f),void h.updateKeySession(s,c)}var p=new XMLHttpRequest,y=null;if(u)if(u.serverURL){var _=u.serverURL;"string"==typeof _&&""!==_?y=_:"object"==typeof _&&_.hasOwnProperty(r)&&(y=_[r])}else u.laURL&&""!==u.laURL&&(y=u.laURL);else(y=R.getLicenseServerURLFromInitData(i.default.getPSSHData(s.initData)))||(y=e.data.laURL);if(y=d.getServerURLFromMessage(y,n,r)){p.open(d.getHTTPMethod(r),y,!0),p.responseType=d.getResponseType(l,r),p.onload=function(){200==this.status?(a(f),h.updateKeySession(s,d.getLicenseMessage(this.response,l,r))):a(f,"DRM: "+l+' update, XHR status is "'+this.statusText+'" ('+this.status+"), expected to be 200. readyState is "+this.readyState+".  Response is "+(this.response?d.getErrorResponse(this.response,l,r):"NONE"))},p.onabort=function(){a(f,"DRM: "+l+' update, XHR aborted. status is "'+this.statusText+'" ('+this.status+"), readyState is "+this.readyState)},p.onerror=function(){a(f,"DRM: "+l+' update, XHR error. status is "'+this.statusText+'" ('+this.status+"), readyState is "+this.readyState)};var E=function(e){var t;if(e)for(t in e)"authorization"===t.toLowerCase()&&(p.withCredentials=!0),p.setRequestHeader(t,e[t])};u&&E(u.httpRequestHeaders),E(R.getRequestHeadersFromMessage(n)),u&&u.withCredentials&&(p.withCredentials=!0),p.send(R.getLicenseRequestFromMessage(n))}else a(f,"DRM: No license server URL specified!")}}function f(e){if(v("DRM: onNeedKey"),"cenc"===e.key.initDataType){var t=e.key.initData;ArrayBuffer.isView(t)&&(t=t.buffer),v("DRM: initData:",String.fromCharCode.apply(null,new Uint8Array(t)));var r=g.getSupportedKeySystems(t);return 0===r.length?void v("DRM: Received needkey event with initData, but we don't support any of the key systems!"):void n(r,!1)}v("DRM:  Only 'cenc' initData is supported!  Ignoring initData of type: "+e.key.initDataType)}var c,g=e.protectionKeyController,h=e.protectionModel,p=e.adapter,m=e.eventBus,v=e.log,y=void 0,_=void 0,E=void 0,T=void 0,M=void 0,S=void 0,R=void 0;return c={initialize:function(e,t,r){if(!M){var a;t||r||(a=p.getStreamsInfo(e)[0]),_=t||(a?p.getMediaInfoForType(e,a,"audio"):null);var i=(E=r||(a?p.getMediaInfoForType(e,a,"video"):null))||_,o=g.getSupportedKeySystemsFromContentProtection(i.contentProtection);o&&o.length>0&&n(o,!0),M=!0}},createKeySession:t,loadKeySession:function(e){h.loadKeySession(e)},removeKeySession:function(e){h.removeKeySession(e)},closeKeySession:function(e){h.closeKeySession(e)},setServerCertificate:function(e){h.setServerCertificate(e)},setMediaElement:r,setSessionType:function(e){S=e},setProtectionData:function(e){T=e},reset:function(){r(null),R=void 0,h&&(h.reset(),h=null)}},g.getKeySystems(),y=[],M=!1,S="temporary",o.default.extend(d.default.events),c}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../CommonEncryption.js")),o=n(e("../../../core/events/Events.js")),s=n(e("../vo/MediaCapability.js")),u=n(e("../vo/KeySystemConfiguration.js")),l=n(e("../../../core/FactoryMaker.js")),d=n(e("../Protection.js"));a.__dashjs_factory_name="ProtectionController",r.default=l.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../../core/events/Events.js":11,"../CommonEncryption.js":94,"../Protection.js":95,"../vo/KeySystemConfiguration.js":114,"../vo/MediaCapability.js":115}],98:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e=this.context,t=void 0,r=void 0,n=void 0;return{initialize:function(){var t;r=[],t=(0,u.default)(e).getInstance(),r.push(t),t=(0,s.default)(e).getInstance(),r.push(t),t=(0,o.default)(e).getInstance(),r.push(t),n=t},isClearKey:function(e){return e===n},initDataEquals:function(e,t){if(e.byteLength===t.byteLength){for(var r=new Uint8Array(e),n=new Uint8Array(t),a=0;a<r.length;a++)if(r[a]!==n[a])return!1;return!0}return!1},getKeySystems:function(){return r},getKeySystemBySystemString:function(e){for(var t=0;t<r.length;t++)if(r[t].systemString===e)return r[t];return null},getSupportedKeySystemsFromContentProtection:function(e){var t,n,a,i,o=[];if(e)for(a=0;a<r.length;++a)for(n=r[a],i=0;i<e.length;++i)if((t=e[i]).schemeIdUri.toLowerCase()===n.schemeIdURI){var s=n.getInitData(t);s&&o.push({ks:r[a],initData:s})}return o},getSupportedKeySystems:function(e){var t,n=[],a=i.default.parsePSSHList(e);for(t=0;t<r.length;++t)r[t].uuid in a&&n.push({ks:r[t],initData:a[r[t].uuid]});return n},getLicenseServer:function(t,r,n){if("license-release"===n||"individualization-request"==n)return null;var a=null;return r&&r.hasOwnProperty("drmtoday")?a=(0,l.default)(e).getInstance():"com.widevine.alpha"===t.systemString?a=(0,f.default)(e).getInstance():"com.microsoft.playready"===t.systemString?a=(0,d.default)(e).getInstance():"org.w3.clearkey"===t.systemString&&(a=(0,c.default)(e).getInstance()),a},processClearKeyLicenseRequest:function(e,r){try{return n.getClearKeysFromProtectionData(e,r)}catch(e){return t("Failed to retrieve clearkeys from ProtectionData"),null}},setConfig:function(e){e&&e.log&&(t=e.log)}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./../CommonEncryption.js")),o=n(e("./../drm/KeySystemClearKey.js")),s=n(e("./../drm/KeySystemWidevine.js")),u=n(e("./../drm/KeySystemPlayReady.js")),l=n(e("./../servers/DRMToday.js")),d=n(e("./../servers/PlayReady.js")),f=n(e("./../servers/Widevine.js")),c=n(e("./../servers/ClearKey.js")),g=n(e("../../../core/FactoryMaker.js"));a.__dashjs_factory_name="ProtectionKeyController",r.default=g.default.getSingletonFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"./../CommonEncryption.js":94,"./../drm/KeySystemClearKey.js":99,"./../drm/KeySystemPlayReady.js":100,"./../drm/KeySystemWidevine.js":101,"./../servers/ClearKey.js":105,"./../servers/DRMToday.js":106,"./../servers/PlayReady.js":107,"./../servers/Widevine.js":108}],99:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){return{uuid:l,schemeIdURI:f,systemString:d,getInitData:function(e){return s.default.parseInitDataFromContentProtection(e)},getRequestHeadersFromMessage:function(){return null},getLicenseRequestFromMessage:function(e){return new Uint8Array(e)},getLicenseServerURLFromInitData:function(){return null},getClearKeysFromProtectionData:function(e,t){var r=null;if(e){for(var n=JSON.parse(String.fromCharCode.apply(null,new Uint8Array(t))),a=[],s=0;s<n.kids.length;s++){var u=n.kids[s],l=e.clearkeys.hasOwnProperty(u)?e.clearkeys[u]:null;if(!l)throw new Error("DRM: ClearKey keyID ("+u+") is not known!");a.push(new i.default(u,l))}r=new o.default(a)}return r}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../vo/KeyPair.js")),o=n(e("../vo/ClearKeyKeySet.js")),s=n(e("../CommonEncryption.js")),u=n(e("../../../core/FactoryMaker.js")),l="1077efec-c0b2-4d02-ace3-3c1e52e2fb4b",d="org.w3.clearkey",f="urn:uuid:"+l;a.__dashjs_factory_name="KeySystemClearKey",r.default=u.default.getSingletonFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../CommonEncryption.js":94,"../vo/ClearKeyKeySet.js":109,"../vo/KeyPair.js":112}],100:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e="utf16";return{uuid:l,schemeIdURI:f,systemString:d,getInitData:function(e){var t,r,n,a,o,s=new Uint8Array([112,115,115,104,0,0,0,0]),l=new Uint8Array([154,4,240,121,152,64,66,134,171,146,230,91,224,136,95,149]),d=0,f=null;if("pssh"in e)return i.default.parseInitDataFromContentProtection(e);if("pro"in e)f=u.default.decodeArray(e.pro.__text);else{if(!("prheader"in e))return null;f=u.default.decodeArray(e.prheader.__text)}return t=f.length,r=4+s.length+l.length+4+t,n=new ArrayBuffer(r),a=new Uint8Array(n),(o=new DataView(n)).setUint32(d,r),d+=4,a.set(s,d),d+=s.length,a.set(l,d),d+=l.length,o.setUint32(d,t),d+=4,a.set(f,d),d+=t,a.buffer},getRequestHeadersFromMessage:function(t){var r,n,a={},i=new DOMParser,o="utf16"===e?new Uint16Array(t):new Uint8Array(t);r=String.fromCharCode.apply(null,o);for(var s=(n=i.parseFromString(r,"application/xml")).getElementsByTagName("name"),u=n.getElementsByTagName("value"),l=0;l<s.length;l++)a[s[l].childNodes[0].nodeValue]=u[l].childNodes[0].nodeValue;return a.hasOwnProperty("Content")&&(a["Content-Type"]=a.Content,delete a.Content),a},getLicenseRequestFromMessage:function(t){var r,n,a=null,i=new DOMParser,o="utf16"===e?new Uint16Array(t):new Uint8Array(t);if(r=String.fromCharCode.apply(null,o),(n=i.parseFromString(r,"application/xml")).getElementsByTagName("Challenge")[0]){var s=n.getElementsByTagName("Challenge")[0].childNodes[0].nodeValue;s&&(a=u.default.decode(s))}return a},getLicenseServerURLFromInitData:function(e){if(e)for(var t=new DataView(e),r=t.getUint16(4,!0),n=6,a=new DOMParser,i=0;r>i;i++){var o=t.getUint16(n,!0);n+=2;var s=t.getUint16(n,!0);if(n+=2,1===o){var u=e.slice(n,n+s),l=String.fromCharCode.apply(null,new Uint16Array(u)),d=a.parseFromString(l,"application/xml");if(d.getElementsByTagName("LA_URL")[0]){var f=d.getElementsByTagName("LA_URL")[0].childNodes[0].nodeValue;if(f)return f}if(d.getElementsByTagName("LUI_URL")[0]){var c=d.getElementsByTagName("LUI_URL")[0].childNodes[0].nodeValue;if(c)return c}}else n+=s}return null},setPlayReadyMessageFormat:function(t){if("utf8"!==t&&"utf16"!==t)throw new o.default("Illegal PlayReady message format! -- "+t);e=t}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../CommonEncryption.js")),o=n(e("../../vo/Error.js")),s=n(e("../../../core/FactoryMaker.js")),u=n(e("../../../../externals/base64.js")),l="9a04f079-9840-4286-ab92-e65be0885f95",d="com.microsoft.playready",f="urn:uuid:"+l;a.__dashjs_factory_name="KeySystemPlayReady",r.default=s.default.getSingletonFactory(a),t.exports=r.default},{"../../../../externals/base64.js":1,"../../../core/FactoryMaker.js":9,"../../vo/Error.js":149,"../CommonEncryption.js":94}],101:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){return{uuid:s,schemeIdURI:l,systemString:u,getInitData:function(e){return i.default.parseInitDataFromContentProtection(e)},getRequestHeadersFromMessage:function(){return null},getLicenseRequestFromMessage:function(e){return new Uint8Array(e)},getLicenseServerURLFromInitData:function(){return null}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../CommonEncryption.js")),o=n(e("../../../core/FactoryMaker.js")),s="edef8ba9-79d6-4ace-a3c8-27dcd51d21ed",u="com.widevine.alpha",l="urn:uuid:"+s;a.__dashjs_factory_name="KeySystemWidevine",r.default=o.default.getSingletonFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../CommonEncryption.js":94}],102:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e){v[m.cancelKeyRequest](y.systemString,e.sessionID)}function r(e,t){if(t&&e){for(var r=e.length,n=0;r>n;n++)if(e[n].sessionID==t)return e[n];return null}return null}function n(){v.removeEventListener(m.keyerror,R),v.removeEventListener(m.needkey,R),v.removeEventListener(m.keymessage,R),v.removeEventListener(m.keyadded,R)}var a,g=this.context,h=e.eventBus,p=e.log,m=e.api,v=void 0,y=void 0,_=void 0,E=void 0,T=void 0,M=void 0,S=void 0,R=void 0;return a={getAllInitData:function(){for(var e=[],t=0;t<T.length;t++)e.push(T[t].initData);for(t=0;t<M.length;t++)e.push(M[t].initData);return e},requestKeySystemAccess:function(e){var t=v;t||(t=document.createElement("video"));for(var r=!1,n=0;n<e.length;n++)for(var a=e[n].ks.systemString,i=e[n].configs,o=null,s=0;s<i.length;s++){var u=i[s].videoCapabilities;if(u&&0!==u.length){o=[];for(var c=0;c<u.length;c++)""!==t.canPlayType(u[c].contentType,a)&&o.push(u[c])}if(o&&(!o||0!==o.length)){r=!0;var g=new l.default(null,o),p=_.getKeySystemBySystemString(a);h.trigger(f.default.KEY_SYSTEM_ACCESS_COMPLETE,{data:new d.default(p,g)});break}}r||h.trigger(f.default.KEY_SYSTEM_ACCESS_COMPLETE,{error:"Key system access denied! -- No valid audio/video content configurations detected!"})},getKeySystem:function(){return y},selectKeySystem:function(e){y=e.keySystem,h.trigger(f.default.INTERNAL_KEY_SYSTEM_SELECTED)},setMediaElement:function(e){v!==e&&(v&&n(),(v=e)&&(v.addEventListener(m.keyerror,R),v.addEventListener(m.needkey,R),v.addEventListener(m.keymessage,R),v.addEventListener(m.keyadded,R),h.trigger(f.default.VIDEO_ELEMENT_SELECTED)))},createKeySession:function(e){if(!y)throw new Error("Can not create sessions until you have selected a key system");if(S||0===M.length){var t={sessionID:null,initData:e,getSessionID:function(){return this.sessionID},getExpirationTime:function(){return NaN},getSessionType:function(){return"temporary"}};return T.push(t),v[m.generateKeyRequest](y.systemString,new Uint8Array(e)),t}throw new Error("Multiple sessions not allowed!")},updateKeySession:function(e,t){var r=e.sessionID;if(_.isClearKey(y))for(var n=0;n<t.keyPairs.length;n++)v[m.addKey](y.systemString,t.keyPairs[n].key,t.keyPairs[n].keyID,r);else v[m.addKey](y.systemString,new Uint8Array(t),e.initData,r)},closeKeySession:t,setServerCertificate:function(){},loadKeySession:function(){},removeKeySession:function(){},reset:function(){v&&n();for(var e=0;e<M.length;e++)t(M[e]);h.trigger(f.default.TEARDOWN_COMPLETE)}},v=null,y=null,T=[],M=[],_=(0,i.default)(g).getInstance(),E=(0,c.default)(g).getInstance(),R={handleEvent:function(e){var t=null;switch(e.type){case m.needkey:var n=ArrayBuffer.isView(e.initData)?e.initData.buffer:e.initData;h.trigger(f.default.NEED_KEY,{key:new o.default(n,"cenc")});break;case m.keyerror:if((t=r(M,e.sessionId))||(t=r(T,e.sessionId)),t){var a="";switch(e.errorCode.code){case 1:a+="MEDIA_KEYERR_UNKNOWN - An unspecified error occurred. This value is used for errors that don't match any of the other codes.";break;case 2:a+="MEDIA_KEYERR_CLIENT - The Key System could not be installed or updated.";break;case 3:a+="MEDIA_KEYERR_SERVICE - The message passed into update indicated an error from the license service.";break;case 4:a+="MEDIA_KEYERR_OUTPUT - There is no available output device with the required characteristics for the content protection system.";break;case 5:a+="MEDIA_KEYERR_HARDWARECHANGE - A hardware configuration change caused a content protection error.";break;case 6:a+="MEDIA_KEYERR_DOMAIN - An error occurred in a multi-device domain licensing configuration. The most common error is a failure to join the domain."}a+="  System Code = "+e.systemCode,h.trigger(f.default.KEY_ERROR,{data:new s.default(t,a)})}else p("No session token found for key error");break;case m.keyadded:(t=r(M,e.sessionId))||(t=r(T,e.sessionId)),t?(p("DRM: Key added."),h.trigger(f.default.KEY_ADDED,{data:t})):p("No session token found for key added");break;case m.keymessage:if((S=null!==e.sessionId&&void 0!==e.sessionId)?!(t=r(M,e.sessionId))&&T.length>0&&(t=T.shift(),M.push(t),t.sessionID=e.sessionId):T.length>0&&(t=T.shift(),M.push(t),0!==T.length&&E.mediaKeyMessageError("Multiple key sessions were creates with a user-agent that does not support sessionIDs!! Unpredictable behavior ahead!")),t){var i=ArrayBuffer.isView(e.message)?e.message.buffer:e.message;t.keyMessage=i,h.trigger(f.default.INTERNAL_KEY_MESSAGE,{data:new u.default(t,i,e.defaultURL)})}else p("No session token found for key message")}}},a}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../controllers/ProtectionKeyController.js")),o=n(e("../vo/NeedKey.js")),s=n(e("../vo/KeyError.js")),u=n(e("../vo/KeyMessage.js")),l=n(e("../vo/KeySystemConfiguration.js")),d=n(e("../vo/KeySystemAccess.js")),f=n(e("../../../core/events/Events.js")),c=n(e("../../utils/ErrorHandler.js")),g=n(e("../../../core/FactoryMaker.js"));a.__dashjs_factory_name="ProtectionModel_01b",r.default=g.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../../core/events/Events.js":11,"../../utils/ErrorHandler.js":139,"../controllers/ProtectionKeyController.js":98,"../vo/KeyError.js":110,"../vo/KeyMessage.js":111,"../vo/KeySystemAccess.js":113,"../vo/KeySystemConfiguration.js":114,"../vo/NeedKey.js":116}],103:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e,r){!function(r){var n=e[r].ks,a=e[r].configs;navigator.requestMediaKeySystemAccess(n.systemString,a).then(function(e){var t="function"==typeof e.getConfiguration?e.getConfiguration():null,r=new l.default(n,t);r.mksa=e,_.trigger(d.default.KEY_SYSTEM_ACCESS_COMPLETE,{data:r})}).catch(function(){++r<e.length?t(e,r):_.trigger(d.default.KEY_SYSTEM_ACCESS_COMPLETE,{error:"Key system access denied!"})})}(r)}function r(e){var t=e.session;return t.removeEventListener("keystatuseschange",e),t.removeEventListener("message",e),t.close()}function n(e){for(var t=0;t<p.length;t++)if(p[t]===e){p.splice(t,1);break}}function a(e,t,r){var a={session:e,initData:t,handleEvent:function(e){switch(e.type){case"keystatuseschange":_.trigger(d.default.KEY_STATUSES_CHANGED,{data:this});break;case"message":var t=ArrayBuffer.isView(e.message)?e.message.buffer:e.message;_.trigger(d.default.INTERNAL_KEY_MESSAGE,{data:new u.default(this,t,void 0,e.messageType)})}},getSessionID:function(){return e.sessionId},getExpirationTime:function(){return e.expiration},getKeyStatuses:function(){return e.keyStatuses},getSessionType:function(){return r}};return e.addEventListener("keystatuseschange",a),e.addEventListener("message",a),e.closed.then(function(){n(a),E("DRM: Session closed.  SessionID = "+a.getSessionID()),_.trigger(d.default.KEY_SESSION_CLOSED,{data:a.getSessionID()})}),p.push(a),a}var f,c,g,h,p,m,v,y=this.context,_=e.eventBus,E=e.log;return f={getAllInitData:function(){for(var e=[],t=0;t<p.length;t++)e.push(p[t].initData);return e},requestKeySystemAccess:function(e){t(e,0)},getKeySystem:function(){return c},selectKeySystem:function(e){e.mksa.createMediaKeys().then(function(t){c=e.keySystem,h=t,g&&g.setMediaKeys(h),_.trigger(d.default.INTERNAL_KEY_SYSTEM_SELECTED)}).catch(function(){_.trigger(d.default.INTERNAL_KEY_SYSTEM_SELECTED,{error:"Error selecting keys system ("+e.keySystem.systemString+")! Could not create MediaKeys -- TODO"})})},setMediaElement:function(e){g!==e&&(g&&(g.removeEventListener("encrypted",m),g.setMediaKeys(null)),(g=e)&&(g.addEventListener("encrypted",m),h&&g.setMediaKeys(h)))},setServerCertificate:function(e){if(!c||!h)throw new Error("Can not set server certificate until you have selected a key system");h.setServerCertificate(e).then(function(){E("DRM: License server certificate successfully updated."),_.trigger(d.default.SERVER_CERTIFICATE_UPDATED)}).catch(function(e){_.trigger(d.default.SERVER_CERTIFICATE_UPDATED,{error:"Error updating server certificate -- "+e.name})})},createKeySession:function(e,t){if(!c||!h)throw new Error("Can not create sessions until you have selected a key system");var r=h.createSession(t),i=a(r,e,t);r.generateRequest("cenc",e).then(function(){E("DRM: Session created.  SessionID = "+i.getSessionID()),_.trigger(d.default.KEY_SESSION_CREATED,{data:i})}).catch(function(e){n(i),_.trigger(d.default.KEY_SESSION_CREATED,{data:null,error:"Error generating key request -- "+e.name})})},updateKeySession:function(e,t){var r=e.session;v.isClearKey(c)&&(t=t.toJWK()),r.update(t).catch(function(t){_.trigger(d.default.KEY_ERROR,{data:new s.default(e,"Error sending update() message! "+t.name)})})},loadKeySession:function(e){if(!c||!h)throw new Error("Can not load sessions until you have selected a key system");var t=h.createSession();t.load(e).then(function(r){if(r){var n=a(t);E("DRM: Session created.  SessionID = "+n.getSessionID()),_.trigger(d.default.KEY_SESSION_CREATED,{data:n})}else _.trigger(d.default.KEY_SESSION_CREATED,{data:null,error:"Could not load session! Invalid Session ID ("+e+")"})}).catch(function(t){_.trigger(d.default.KEY_SESSION_CREATED,{data:null,error:"Could not load session ("+e+")! "+t.name})})},removeKeySession:function(e){e.session.remove().then(function(){E("DRM: Session removed.  SessionID = "+e.getSessionID()),_.trigger(d.default.KEY_SESSION_REMOVED,{data:e.getSessionID()})},function(t){_.trigger(d.default.KEY_SESSION_REMOVED,{data:null,error:"Error removing session ("+e.getSessionID()+"). "+t.name})})},closeKeySession:function(e){r(e).catch(function(t){n(e),_.trigger(d.default.KEY_SESSION_CLOSED,{data:null,error:"Error closing session ("+e.getSessionID()+") "+t.name})})},reset:function(){var e,t=p.length;if(0!==t)for(var a=function(e){n(e),0===p.length&&(g?(g.removeEventListener("encrypted",m),g.setMediaKeys(null).then(function(){_.trigger(d.default.TEARDOWN_COMPLETE)})):_.trigger(d.default.TEARDOWN_COMPLETE))},i=0;t>i;i++)(function(t){e.session.closed.then(function(){a(t)}),r(e).catch(function(){a(t)})})(e=p[i]);else _.trigger(d.default.TEARDOWN_COMPLETE)}},c=null,g=null,h=null,p=[],v=(0,i.default)(y).getInstance(),m={handleEvent:function(e){if("encrypted"===e.type&&e.initData){var t=ArrayBuffer.isView(e.initData)?e.initData.buffer:e.initData;_.trigger(d.default.NEED_KEY,{key:new o.default(t,e.initDataType)})}}},f}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../controllers/ProtectionKeyController.js")),o=n(e("../vo/NeedKey.js")),s=n(e("../vo/KeyError.js")),u=n(e("../vo/KeyMessage.js")),l=n(e("../vo/KeySystemAccess.js")),d=n(e("../../../core/events/Events.js")),f=n(e("../../../core/FactoryMaker.js"));a.__dashjs_factory_name="ProtectionModel_21Jan2015",r.default=f.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../../core/events/Events.js":11,"../controllers/ProtectionKeyController.js":98,"../vo/KeyError.js":110,"../vo/KeyMessage.js":111,"../vo/KeySystemAccess.js":113,"../vo/NeedKey.js":116}],104:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e){var t=e.session;t.removeEventListener(h.error,e),t.removeEventListener(h.message,e),t.removeEventListener(h.ready,e),t.removeEventListener(h.close,e);for(var r=0;r<_.length;r++)if(_[r]===e){_.splice(r,1);break}t[h.release]()}function r(){var e=null,t=function(){p.removeEventListener("loadedmetadata",e),p[h.setMediaKeys](v),c.trigger(f.default.VIDEO_ELEMENT_SELECTED)};p.readyState>=1?t():(e=t.bind(this),p.addEventListener("loadedmetadata",e))}var n,a=this.context,c=e.eventBus,g=e.log,h=e.api,p=void 0,m=void 0,v=void 0,y=void 0,_=void 0,E=void 0,T=void 0;return n={getAllInitData:function(){for(var e=[],t=0;t<_.length;t++)e.push(_[t].initData);return e},requestKeySystemAccess:function(e){for(var t=!1,r=0;r<e.length;r++)for(var n=e[r].ks.systemString,a=e[r].configs,i=null,o=null,s=0;s<a.length;s++){var u=a[s].audioCapabilities,g=a[s].videoCapabilities;if(u&&0!==u.length){i=[];for(var p=0;p<u.length;p++)window[h.MediaKeys].isTypeSupported(n,u[p].contentType)&&i.push(u[p])}if(g&&0!==g.length){o=[];for(var m=0;m<g.length;m++)window[h.MediaKeys].isTypeSupported(n,g[m].contentType)&&o.push(g[m])}if(!(!i&&!o||i&&0===i.length||o&&0===o.length)){t=!0;var v=new l.default(i,o),y=T.getKeySystemBySystemString(n);c.trigger(f.default.KEY_SYSTEM_ACCESS_COMPLETE,{data:new d.default(y,v)});break}}t||c.trigger(f.default.KEY_SYSTEM_ACCESS_COMPLETE,{error:"Key system access denied! -- No valid audio/video content configurations detected!"})},getKeySystem:function(){return m},selectKeySystem:function(e){try{v=e.mediaKeys=new window[h.MediaKeys](e.keySystem.systemString),m=e.keySystem,y=e,p&&r(),c.trigger(f.default.INTERNAL_KEY_SYSTEM_SELECTED)}catch(e){c.trigger(f.default.INTERNAL_KEY_SYSTEM_SELECTED,{error:"Error selecting keys system ("+m.systemString+")! Could not create MediaKeys -- TODO"})}},setMediaElement:function(e){p!==e&&(p&&p.removeEventListener(h.needkey,E),(p=e)&&(p.addEventListener(h.needkey,E),v&&r()))},createKeySession:function(e){if(!m||!v||!y)throw new Error("Can not create sessions until you have selected a key system");var t=null;if(null!==y.ksConfiguration.videoCapabilities&&y.ksConfiguration.videoCapabilities.length>0&&(t=y.ksConfiguration.videoCapabilities[0]),null===t&&null!==y.ksConfiguration.audioCapabilities&&y.ksConfiguration.audioCapabilities.length>0&&(t=y.ksConfiguration.audioCapabilities[0]),null===t)throw new Error("Can not create sessions for unknown content types.");var r=t.contentType,n=v.createSession(r,new Uint8Array(e)),a=function(e,t){return{session:e,initData:t,getSessionID:function(){return this.session.sessionId},getExpirationTime:function(){return NaN},getSessionType:function(){return"temporary"},handleEvent:function(e){switch(e.type){case h.error:c.trigger(f.default.KEY_ERROR,{data:new s.default(this,"KeyError")});break;case h.message:var t=ArrayBuffer.isView(e.message)?e.message.buffer:e.message;c.trigger(f.default.INTERNAL_KEY_MESSAGE,{data:new u.default(this,t,e.destinationURL)});break;case h.ready:g("DRM: Key added."),c.trigger(f.default.KEY_ADDED);break;case h.close:g("DRM: Session closed.  SessionID = "+this.getSessionID()),c.trigger(f.default.KEY_SESSION_CLOSED,{data:this.getSessionID()})}}}}(n,e);n.addEventListener(h.error,a),n.addEventListener(h.message,a),n.addEventListener(h.ready,a),n.addEventListener(h.close,a),_.push(a),g("DRM: Session created.  SessionID = "+a.getSessionID()),c.trigger(f.default.KEY_SESSION_CREATED,{data:a})},updateKeySession:function(e,t){var r=e.session;T.isClearKey(m)?r.update(new Uint8Array(t.toJWK())):r.update(new Uint8Array(t))},closeKeySession:t,setServerCertificate:function(){},loadKeySession:function(){},removeKeySession:function(){},reset:function(){try{for(var e=0;e<_.length;e++)t(_[e]);p&&p.removeEventListener(h.needkey,E),c.trigger(f.default.TEARDOWN_COMPLETE)}catch(e){c.trigger(f.default.TEARDOWN_COMPLETE,{error:"Error tearing down key sessions and MediaKeys! -- "+e.message})}}},p=null,m=null,v=null,y=null,_=[],T=(0,i.default)(a).getInstance(),E={handleEvent:function(e){if(e.type===h.needkey&&e.initData){var t=ArrayBuffer.isView(e.initData)?e.initData.buffer:e.initData;c.trigger(f.default.NEED_KEY,{key:new o.default(t,"cenc")})}}},n}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../controllers/ProtectionKeyController.js")),o=n(e("../vo/NeedKey.js")),s=n(e("../vo/KeyError.js")),u=n(e("../vo/KeyMessage.js")),l=n(e("../vo/KeySystemConfiguration.js")),d=n(e("../vo/KeySystemAccess.js")),f=n(e("../../../core/events/Events.js")),c=n(e("../../../core/FactoryMaker.js"));a.__dashjs_factory_name="ProtectionModel_3Feb2014",r.default=c.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../../core/events/Events.js":11,"../controllers/ProtectionKeyController.js":98,"../vo/KeyError.js":110,"../vo/KeyMessage.js":111,"../vo/KeySystemAccess.js":113,"../vo/KeySystemConfiguration.js":114,"../vo/NeedKey.js":116}],105:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){return{getServerURLFromMessage:function(e,t){var r=JSON.parse(String.fromCharCode.apply(null,new Uint8Array(t)));e+="/?";for(var n=0;n<r.kids.length;n++)e+=r.kids[n]+"&";return e.substring(0,e.length-1)},getHTTPMethod:function(){return"GET"},getResponseType:function(){return"json"},getLicenseMessage:function(e){if(!e.hasOwnProperty("keys"))return null;for(var t=[],r=0;r<e.keys.length;r++){var n=e.keys[r],a=n.kid.replace(/=/g,""),s=n.k.replace(/=/g,"");t.push(new i.default(a,s))}return new o.default(t)},getErrorResponse:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../vo/KeyPair.js")),o=n(e("../vo/ClearKeyKeySet.js")),s=n(e("../../../core/FactoryMaker.js"));a.__dashjs_factory_name="ClearKey",r.default=s.default.getSingletonFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../vo/ClearKeyKeySet.js":109,"../vo/KeyPair.js":112}],106:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e={"com.widevine.alpha":{responseType:"json",getLicenseMessage:function(e){return o.default.decodeArray(e.license)},getErrorResponse:function(e){return e}},"com.microsoft.playready":{responseType:"arraybuffer",getLicenseMessage:function(e){return e},getErrorResponse:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}}};return{getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(t){return e[t].responseType},getLicenseMessage:function(t,r){return e[r].getLicenseMessage(t)},getErrorResponse:function(t,r){return e[r].getErrorResponse(t)}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../core/FactoryMaker.js")),o=n(e("../../../../externals/base64.js"));a.__dashjs_factory_name="DRMToday",r.default=i.default.getSingletonFactory(a),t.exports=r.default},{"../../../../externals/base64.js":1,"../../../core/FactoryMaker.js":9}],107:[function(e,t,r){"use strict";function n(){return{getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(){return"arraybuffer"},getLicenseMessage:function(e){return e},getErrorResponse:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../../core/FactoryMaker.js"));n.__dashjs_factory_name="PlayReady",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../../core/FactoryMaker.js":9}],108:[function(e,t,r){"use strict";function n(){return{getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(){return"arraybuffer"},getLicenseMessage:function(e){return e},getErrorResponse:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../../core/FactoryMaker.js"));n.__dashjs_factory_name="Widevine",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../../core/FactoryMaker.js":9}],109:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),a=function(){function e(t,r){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),r&&"persistent"!==r&&"temporary"!==r)throw new Error("Invalid ClearKey key set type!  Must be one of 'persistent' or 'temporary'");this.keyPairs=t,this.type=r}return n(e,[{key:"toJWK",value:function(){var e,t=this.keyPairs.length,r={keys:[]};for(e=0;t>e;e++){var n={kty:"oct",alg:"A128KW",kid:this.keyPairs[e].keyID,k:this.keyPairs[e].key};r.keys.push(n)}this.type&&(r.type=this.type);var a=JSON.stringify(r),i=a.length,o=new ArrayBuffer(i),s=new Uint8Array(o);for(e=0;i>e;e++)s[e]=a.charCodeAt(e);return o}}]),e}();r.default=a,t.exports=r.default},{}],110:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(t,r){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.sessionToken=t,this.error=r},t.exports=r.default},{}],111:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(t,r,n,a){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.sessionToken=t,this.message=r,this.defaultURL=n,this.messageType=a||"license-request"},t.exports=r.default},{}],112:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(t,r){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.keyID=t,this.key=r},t.exports=r.default},{}],113:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(t,r){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.keySystem=t,this.ksConfiguration=r},t.exports=r.default},{}],114:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(t,r,n,a,i){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.initDataTypes=["cenc"],this.audioCapabilities=t,this.videoCapabilities=r,this.distinctiveIdentifier=n,this.persistentState=a,this.sessionTypes=i},t.exports=r.default},{}],115:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(t,r){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.contentType=t,this.robustness=r},t.exports=r.default},{}],116:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(t,r){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.initData=t,this.initDataType=r},t.exports=r.default},{}],117:[function(e,t,r){"use strict";function n(e){var t=e.streamProcessor.getCurrentRepresentationInfo(),r=e.streamProcessor,n=e.currentValue;return{getStreamInfo:function(){return t.mediaInfo.streamInfo},getMediaInfo:function(){return t.mediaInfo},getTrackInfo:function(){return t},getCurrentValue:function(){return n},getManifestInfo:function(){return t.mediaInfo.streamInfo.manifestInfo},getStreamProcessor:function(){return r}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js"));n.__dashjs_factory_name="RulesContext",r.default=a.default.getClassFactory(n),t.exports=r.default},{"../../core/FactoryMaker.js":9}],118:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e,r){return(0,i.default)(t).create({streamProcessor:e,currentValue:r})}var t=this.context,r=void 0;return{initialize:function(){r={}},setConfig:function(e){e&&(e.abrRulesCollection&&(r[d]=e.abrRulesCollection),e.synchronizationRulesCollection&&(r[f]=e.synchronizationRulesCollection))},applyRules:function(t,r,n,a,i){var s,u={},l=t.length,d=l,f=e(r,a),c=function(e){var t,r;e.value!==o.default.NO_CHANGE&&(u[e.priority]=i(u[e.priority],e.value)),--l||(u[o.default.WEAK]!==o.default.NO_CHANGE&&(r=o.default.WEAK,t=u[o.default.WEAK]),u[o.default.DEFAULT]!==o.default.NO_CHANGE&&(r=o.default.DEFAULT,t=u[o.default.DEFAULT]),u[o.default.STRONG]!==o.default.NO_CHANGE&&(r=o.default.STRONG,t=u[o.default.STRONG]),r!=o.default.STRONG&&r!=o.default.WEAK&&(r=o.default.DEFAULT),n({value:void 0!==t?t:a,confidence:r}))};for(u[o.default.STRONG]=o.default.NO_CHANGE,u[o.default.WEAK]=o.default.NO_CHANGE,u[o.default.DEFAULT]=o.default.NO_CHANGE,s=0;d>s;s++)t[s].execute(f,c)},reset:function(){var e,t,n=r[d],a=r[f],i=(n.getRules(s.default.QUALITY_SWITCH_RULES)||[]).concat(n.getRules(s.default.ABANDON_FRAGMENT_RULES)||[]).concat(a.getRules(u.default.TIME_SYNCHRONIZED_RULES)||[]).concat(a.getRules(u.default.BEST_GUESS_RULES)||[]),o=i.length;for(t=0;o>t;t++)"function"==typeof(e=i[t]).reset&&e.reset();r={}}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./RulesContext.js")),o=n(e("./SwitchRequest.js")),s=n(e("./abr/ABRRulesCollection.js")),u=n(e("./synchronization/SynchronizationRulesCollection.js")),l=n(e("../../core/FactoryMaker.js")),d=0,f=1;a.__dashjs_factory_name="RulesController";var c=l.default.getSingletonFactory(a);c.ABR_RULE=d,c.SYNC_RULE=f,r.default=c,t.exports=r.default},{"../../core/FactoryMaker.js":9,"./RulesContext.js":117,"./SwitchRequest.js":119,"./abr/ABRRulesCollection.js":120,"./synchronization/SynchronizationRulesCollection.js":133}],119:[function(e,t,r){"use strict";function n(e,t){return{value:void 0===e?i:e,priority:void 0===t?o:t}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js")),i=999,o=.5;n.__dashjs_factory_name="SwitchRequest";var s=a.default.getClassFactory(n);s.NO_CHANGE=i,s.DEFAULT=o,s.STRONG=1,s.WEAK=0,r.default=s,t.exports=r.default},{"../../core/FactoryMaker.js":9}],120:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e=this.context,t=void 0,r=void 0;return{initialize:function(){t=[],r=[];var n=(0,c.default)(e).getInstance(),a=(0,g.default)(e).getInstance();(0,f.default)(e).getInstance().getBufferOccupancyABREnabled()?(t.push((0,l.default)(e).create({metricsModel:n,dashMetrics:(0,g.default)(e).getInstance()})),r.push((0,d.default)(e).create({metricsModel:n,dashMetrics:(0,g.default)(e).getInstance()}))):(t.push((0,i.default)(e).create({metricsModel:n,dashMetrics:a})),t.push((0,o.default)(e).create({metricsModel:n,dashMetrics:a})),t.push((0,s.default)(e).create({metricsModel:n})),r.push((0,u.default)(e).create()))},getRules:function(e){switch(e){case p:return t;case m:return r;default:return null}}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./ThroughputRule.js")),o=n(e("./BufferOccupancyRule.js")),s=n(e("./InsufficientBufferRule.js")),u=n(e("./AbandonRequestsRule.js")),l=n(e("./BolaRule.js")),d=n(e("./BolaAbandonRule.js")),f=n(e("../../models/MediaPlayerModel.js")),c=n(e("../../models/MetricsModel.js")),g=n(e("../../../dash/DashMetrics.js")),h=n(e("../../../core/FactoryMaker.js")),p="qualitySwitchRules",m="abandonFragmentRules";a.__dashjs_factory_name="ABRRulesCollection";var v=h.default.getSingletonFactory(a);v.QUALITY_SWITCH_RULES=p,v.ABANDON_FRAGMENT_RULES=m,r.default=v,t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../../dash/DashMetrics.js":15,"../../models/MediaPlayerModel.js":90,"../../models/MetricsModel.js":91,"./AbandonRequestsRule.js":121,"./BolaAbandonRule.js":122,"./BolaRule.js":123,"./BufferOccupancyRule.js":124,"./InsufficientBufferRule.js":125,"./ThroughputRule.js":126}],121:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e,t=this.context,r=(0,u.default)(t).getInstance().log,n=void 0,a=void 0,s=void 0;return e={execute:function(e,o){var u,f=(new Date).getTime(),c=e.getMediaInfo(),g=c.type,h=e.getCurrentValue(),p=e.getTrackInfo(),m=h.request,v=e.getStreamProcessor().getABRController(),y=(0,i.default)(t).create(i.default.NO_CHANGE,i.default.WEAK);if(!isNaN(m.index)){if(function(e,t){n[e]=n[e]||{},n[e][t]=n[e][t]||{}}(g,m.index),null===(u=n[g][m.index])||null===m.firstByteDate||a.hasOwnProperty(u.id))return void o(y);if(void 0===u.firstByteTime&&(u.firstByteTime=m.firstByteDate.getTime(),u.segmentDuration=m.duration,u.bytesTotal=m.bytesTotal,u.id=m.index),u.bytesLoaded=m.bytesLoaded,u.elapsedTime=f-u.firstByteTime,u.bytesLoaded<u.bytesTotal&&u.elapsedTime>=l){if(u.measuredBandwidthInKbps=Math.round(8*u.bytesLoaded/u.elapsedTime),u.estimatedTimeOfDownload=(8*u.bytesTotal*.001/u.measuredBandwidthInKbps).toFixed(2),u.estimatedTimeOfDownload<u.segmentDuration*d||0===p.quality)return void o(y);if(!a.hasOwnProperty(u.id)){var _=v.getQualityForBitrate(c,u.measuredBandwidthInKbps*s.getBandwidthSafetyFactor());y=(0,i.default)(t).create(_,i.default.STRONG),a[u.id]=u,r("AbandonRequestsRule ( ",g,"frag id",u.id,") is asking to abandon and switch to quality to ",_," measured bandwidth was",u.measuredBandwidthInKbps),delete n[g][u.id]}}else u.bytesLoaded===u.bytesTotal&&delete n[g][u.id]}o(y)},reset:function(){n={},a={}}},n={},a={},s=(0,o.default)(t).getInstance(),e}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../SwitchRequest.js")),o=n(e("../../models/MediaPlayerModel.js")),s=n(e("../../../core/FactoryMaker.js")),u=n(e("../../../core/Debug.js")),l=500,d=1.5;a.__dashjs_factory_name="AbandonRequestsRule",r.default=s.default.getClassFactory(a),t.exports=r.default},{"../../../core/Debug.js":7,"../../../core/FactoryMaker.js":9,"../../models/MediaPlayerModel.js":90,"../SwitchRequest.js":119}],122:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e,t,r){f[e]={index:t,quality:r}}var r,n=this.context,a=(0,u.default)(n).getInstance().log,s=e.dashMetrics,d=e.metricsModel,f=void 0;return r={execute:function(e,r){var o=e.getMediaInfo().type,u=d.getReadOnlyMetricsFor(o),c=e.getCurrentValue().request,g=(0,i.default)(n).create(i.default.NO_CHANGE,i.default.WEAK);if(0===u.BolaState.length||u.BolaState[0]._s.state!==l.default.BOLA_STATE_STEADY)return f={},void r(g);var h=u.BolaState[0]._s,p=c.index,m=c.quality;if(!isNaN(p)&&0!==m&&function(e,t,r){var n=f[e];return!n||t>n.index||t==n.index&&r<n.quality}(o,p,m)&&c.firstByteDate){var v=(new Date).getTime()-c.firstByteDate.getTime(),y=c.bytesLoaded,_=c.bytesTotal,E=_-y,T=c.duration,M=s.getCurrentBufferLevel(u)?s.getCurrentBufferLevel(u):0,S=8e3*h.bandwidthSafetyFactor*y/v,R=.001*(c.firstByteDate.getTime()-c.requestStartDate.getTime());.2>R&&(R=.2);var I="index="+p+" quality="+m+" bytesLoaded/bytesTotal="+y+"/"+_+" bufferLevel="+M+" timeSince1stByte="+(v/1e3).toFixed(3)+" estThroughput="+(S/1e6).toFixed(3)+" latency="+R.toFixed(3),C=_*h.bitrate[0]/h.bitrate[m],A=E-R*S/8;if(1>A&&(A=1),500>v||C>=E||M>h.bufferTarget||C>=A||T>=8*_/S)r(g);else{if(h.safetyGuarantee&&M<=h.fragmentDuration&&h.state===l.default.BOLA_STATE_STEADY)return h.lastQuality=0,d.updateBolaState(o,h),t(o,p,m),g=(0,i.default)(n).create(0,i.default.STRONG),l.default.BOLA_DEBUG&&a("BolaDebug "+o+" BolaAbandonRule to 0 for safety guarantee - "+I),void r(g);var j=8*E/S,D=m;if(j>M)for(--D;D>0&&!(M>=(j=R+8*(C=_*h.bitrate[D]/h.bitrate[m])/S));)--D;for(var b=M+h.virtualBuffer-R,O=m,P=(h.utility[m]+h.gp-b/h.Vp)/A,w=0;m>w&&!((C=_*h.bitrate[w]/h.bitrate[m])>A);++w){var N=(h.utility[w]+h.gp-b/h.Vp)/C;N>P&&(O=w,P=N)}if(O>D&&(O=D),O!==m){for(;O>0&&h.bitrate[O]>S;)--O;h.lastQuality=O,d.updateBolaState(o,h),t(o,p,m),g=(0,i.default)(n).create(O,i.default.STRONG),l.default.BOLA_DEBUG&&a("BolaDebug "+o+" BolaAbandonRule abandon to "+O+" - "+I),r(g)}else r(g)}}else r(g)},reset:function(){f={}}},f={},(0,o.default)(n).getInstance(),r}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../SwitchRequest.js")),o=n(e("../../models/MediaPlayerModel.js")),s=n(e("../../../core/FactoryMaker.js")),u=n(e("../../../core/Debug.js")),l=n(e("./BolaRule.js"));a.__dashjs_factory_name="BolaAbandonRule",r.default=s.default.getClassFactory(a),t.exports=r.default},{"../../../core/Debug.js":7,"../../../core/FactoryMaker.js":9,"../../models/MediaPlayerModel.js":90,"../SwitchRequest.js":119,"./BolaRule.js":123}],123:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(){P={},w=[],N=(0,s.default)(C).getInstance(),(0,u.default)(C).getInstance(),x=(0,d.default)(C).getInstance(),b.on(c.default.PLAYBACK_SEEKING,T,O),b.on(c.default.PERIOD_SWITCH_STARTED,M,O)}function r(e){var t={},r=e.getMediaInfo(),n=r.bitrateList.map(function(e){return e.bandwidth}),a=n.length;if(2>a||n[0]>=n[1]||n[a-2]>=n[a-1])return t.state=h,t;var i,o,s=e.getStreamProcessor(),u=e.getStreamInfo(),l=e.getTrackInfo(),d=s.isDynamic(),f=u.manifestInfo.duration,c=l.fragmentDuration;i=N.getStableBufferTime(),o=f>=N.getLongFormContentDurationThreshold()?N.getBufferTimeAtTopQualityLongForm():N.getBufferTimeAtTopQuality();var g=i;c+S>g&&(g=c+S);for(var m=[],v=0;a>v;++v)m.push(Math.log(n[v]/n[0]));var _=(g-c)/m[a-1],E=1+m[a-1]/(g/c-1),T=!d&&g===i;if(T){var M=_,C=E;for(v=1;a>v;++v){var j=M*(C-n[0]*m[v]/(n[v]-n[0])),D=c*(2-n[0]/n[v])+.2;if(D>=i){T=!1;break}D>j&&(C=D/(M*=(i-D)/(i-j))+m[v]*n[0]/(n[v]-n[0]))}T&&S>(i-c)*M/_&&(T=!1),T&&(_=M,E=C)}var b=_*(m[a-1]+E);if(t.state=p,t.bitrate=n,t.utility=m,t.Vp=_,t.gp=E,t.fragmentDuration=c,t.bandwidthSafetyFactor=N.getBandwidthSafetyFactor(),t.bufferTarget=i,t.bufferMax=o,t.bolaBufferTarget=g,t.bolaBufferMax=b,t.isDynamic=d,t.safetyGuarantee=T,t.lastQuality=0,t.virtualBuffer=0,t.throughputCount=d?R:I,y){var O="";for(v=0;v<n.length-1;++v){var P=m[v],w=m[v+1],x=n[v],L=n[v+1],F=_*((P*L-w*x)/(L-x)+E),B=_*(P+E);O+=v+":"+(n[v]/1e6).toFixed(3)+" "+F.toFixed(3)+"/"+B.toFixed(3)+" "}O+=" "+(n.length-1)+":"+(n[n.length-1]/1e6).toFixed(3)+" -/"+(_*(m[n.length-1]+E)).toFixed(3),A("BolaDebug "+r.type+" bitrates "+O)}return t}function n(e,t){for(var r=e.bitrate.length,n=r-1,a=0,i=0;r>i;++i){var o=(e.utility[i]+e.gp-t/e.Vp)/e.bitrate[i];o>a&&(a=o,n=i)}return n}function a(e,t){for(var r=j.getHttpRequests(e),n=[],a=r.length-1;a>=0;--a){var i=r[a];if(i.type===l.default.MEDIA_SEGMENT_TYPE&&i._tfinish&&i.tresponse&&i.trace&&(n.push(i),n.length===t))break}return n}function o(e,t,r){var n=a(e,t);if(0===n.length)return 0;for(var i=0,o="",s=0;s<n.length;++s){var u=.001*(n[s]._tfinish.getTime()-n[s].trequest.getTime()),l=8*n[s].trace.reduce(function(e,t){return e+t.b[0]},0);o+=" "+(l/1e6).toFixed(3)+"/"+u.toFixed(3)+"="+(l/u/1e6).toFixed(3),i+=u/l}return y&&A("BolaDebug "+r+" BolaRule last throughput = "+(n.length/i/1e6).toFixed(3)+" :"+o),n.length/i}function _(e,t){for(var r=0,n=1;n<e.bitrate.length&&!(e.bitrate[n]>t);++n)r=n;return r}function E(e,t){var r=a(e,1);if(0===r.length)return 0;var n=r[0],i=(new Date).getTime(),o=n._tfinish.getTime();o>i&&(o=i);var s,u=P[t];return P[t]=i,0>(s=u&&u>o?i-u:i-o)?0:.001*s}function T(){for(var e=0;e<w.length;++e){var t=w[e],r=D.getReadOnlyMetricsFor(t);if(0!==r.BolaState.length){var n=r.BolaState[0]._s;n.state!==h&&(n.state=p),D.updateBolaState(t,n)}}}function M(){}var S=5,R=2,I=3,C=this.context,A=(0,g.default)(C).getInstance().log,j=e.dashMetrics,D=e.metricsModel,b=(0,f.default)(C).getInstance(),O=void 0,P=void 0,w=void 0,N=void 0,x=void 0;return O={execute:function(e,t){var a=e.getStreamProcessor();a.getScheduleController().setTimeToLoadDelay(0);var s=(0,i.default)(C).create(i.default.NO_CHANGE,i.default.WEAK),u=e.getMediaInfo().type,l=D.getReadOnlyMetricsFor(u);if(0===l.BolaState.length){y&&A("BolaDebug "+u+"\nBolaDebug "+u+" BolaRule for state=- fragmentStart="+x.getIndexHandlerTime(e.getStreamProcessor()).toFixed(3));var d=r(e);D.updateBolaState(u,d);var f=0;if(d.state!==h){w.push(u);var c=o(l,d.throughputCount,u);if(0===c)return y&&A("BolaDebug "+u+" BolaRule quality unchanged for INITIALIZE"),void t(s);f=_(d,c*d.bandwidthSafetyFactor),d.lastQuality=f,s=(0,i.default)(C).create(f,i.default.DEFAULT)}return y&&A("BolaDebug "+u+" BolaRule quality "+f+" for INITIALIZE"),void t(s)}var g=l.BolaState[0]._s;if(g.state===h)return y&&A("BolaDebug "+u+" BolaRule quality 0 for ONE_BITRATE"),void t(s);y&&A("BolaDebug "+u+"\nBolaDebug "+u+" EXECUTE BolaRule for state="+g.state+" fragmentStart="+x.getIndexHandlerTime(e.getStreamProcessor()).toFixed(3));var T=j.getCurrentBufferLevel(l)?j.getCurrentBufferLevel(l):0,M=n(g,T),S=o(l,g.throughputCount,u);if(y&&A("BolaDebug "+u+" BolaRule bufferLevel="+T.toFixed(3)+"(+"+g.virtualBuffer.toFixed(3)+") lastThroughput="+(S/1e6).toFixed(3)+" tentativeQuality="+M+","+n(g,T+g.virtualBuffer)),.1>=T&&(g.virtualBuffer=0),!g.safetyGuarantee){var R=E(l,u);R>0&&(g.virtualBuffer+=R),T+g.virtualBuffer>g.bolaBufferMax&&(g.virtualBuffer=g.bolaBufferMax-T),g.virtualBuffer<0&&(g.virtualBuffer=0);var I=n(g,T+g.virtualBuffer);if(I>M){for(var b=M;I>b&&g.bitrate[b+1]*g.fragmentDuration/(S*g.bandwidthSafetyFactor)<T;)++b;if(b>M)if(b>=I)M=I;else{M=b;var O=g.Vp*(g.gp+g.utility[M]);T+g.virtualBuffer>O&&(g.virtualBuffer=O-T,g.virtualBuffer<0&&(g.virtualBuffer=0))}}}if((g.state===p||g.state===m)&&(f=_(g,S*g.bandwidthSafetyFactor),0>=S&&(g.state=v),g.state===p&&f<g.lastQuality&&(g.state=m),g.state===m&&f>g.lastQuality&&(f=g.lastQuality),M>=f&&(g.state=v),g.state!==v))return y&&A("BolaDebug "+u+" BolaRule quality "+f+">"+M+" for STARTUP"),g.lastQuality=f,D.updateBolaState(u,g),void t(s=(0,i.default)(C).create(f,i.default.DEFAULT));var P=0;M>g.lastQuality&&M>(f=_(g,S))&&(f<g.lastQuality?f=g.lastQuality:P=T-g.Vp*(g.utility[f]+g.gp),M=f),P>0&&(P>g.virtualBuffer?(P-=g.virtualBuffer,g.virtualBuffer=0):(g.virtualBuffer-=P,P=0)),P>0&&a.getScheduleController().setTimeToLoadDelay(1e3*P),g.lastQuality=M,D.updateBolaState(u,g),s=(0,i.default)(C).create(M,i.default.DEFAULT),y&&A("BolaDebug "+u+" BolaRule quality "+M+" delay="+P.toFixed(3)+" for STEADY"),t(s)},reset:function(){b.off(c.default.PLAYBACK_SEEKING,T,O),b.off(c.default.PERIOD_SWITCH_STARTED,M,O),t()}},t(),O}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../SwitchRequest.js")),o=n(e("../../../core/FactoryMaker.js")),s=n(e("../../models/MediaPlayerModel.js")),u=n(e("../../controllers/PlaybackController.js")),l=n(e("../../vo/metrics/HTTPRequest.js")),d=n(e("../../../dash/DashAdapter.js")),f=n(e("../../../core/EventBus.js")),c=n(e("../../../core/events/Events.js")),g=n(e("../../../core/Debug.js")),h=0,p=1,m=2,v=3,y=!1;a.__dashjs_factory_name="BolaRule";var _=o.default.getClassFactory(a);_.BOLA_STATE_ONE_BITRATE=h,_.BOLA_STATE_STARTUP=p,_.BOLA_STATE_STARTUP_NO_INC=m,_.BOLA_STATE_STEADY=v,_.BOLA_DEBUG=y,r.default=_,t.exports=r.default},{"../../../core/Debug.js":7,"../../../core/EventBus.js":8,"../../../core/FactoryMaker.js":9,"../../../core/events/Events.js":11,"../../../dash/DashAdapter.js":13,"../../controllers/PlaybackController.js":57,"../../models/MediaPlayerModel.js":90,"../../vo/metrics/HTTPRequest.js":166,"../SwitchRequest.js":119}],124:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t,r=this.context,n=(0,l.default)(r).getInstance().log,a=e.metricsModel,u=e.dashMetrics,d=void 0,f=void 0;return t={execute:function(e,t){var o=(new Date).getTime()/1e3,l=e.getMediaInfo(),c=e.getTrackInfo(),g=l.type,h=isNaN(c.fragmentDuration)?2:c.fragmentDuration/2,p=e.getCurrentValue(),m=e.getStreamProcessor().getABRController(),v=a.getReadOnlyMetricsFor(g),y=u.getCurrentBufferLevel(v),_=v.BufferState.length>0?v.BufferState[v.BufferState.length-1]:null,E=l.representationCount-1,T=(0,i.default)(r).create(i.default.NO_CHANGE,i.default.WEAK);return h>o-d||m.getAbandonmentStateFor(g)===s.default.ABANDON_LOAD||(null!==_&&y>_.target&&y-_.target>f.getRichBufferThreshold()&&l.representationCount>1&&(T=(0,i.default)(r).create(E,i.default.STRONG)),T.value!==i.default.NO_CHANGE&&T.value!==p&&n("BufferOccupancyRule requesting switch to index: ",T.value,"type: ",g," Priority: ",T.priority===i.default.DEFAULT?"Default":T.priority===i.default.STRONG?"Strong":"Weak")),void t(T)},reset:function(){d=0}},d=0,f=(0,o.default)(r).getInstance(),t}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../SwitchRequest.js")),o=n(e("../../models/MediaPlayerModel.js")),s=n(e("../../controllers/AbrController.js")),u=n(e("../../../core/FactoryMaker.js")),l=n(e("../../../core/Debug.js"));a.__dashjs_factory_name="BufferOccupancyRule",r.default=u.default.getClassFactory(a),t.exports=r.default},{"../../../core/Debug.js":7,"../../../core/FactoryMaker.js":9,"../../controllers/AbrController.js":49,"../../models/MediaPlayerModel.js":90,"../SwitchRequest.js":119}],125:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(){c={}}var r=this.context,n=(0,d.default)(r).getInstance().log,a=(0,s.default)(r).getInstance(),l=e.metricsModel,f=void 0,c=void 0,g=void 0,h=void 0;return f={execute:function(e,t){var a=(new Date).getTime(),s=e.getMediaInfo().type,u=e.getCurrentValue(),d=l.getReadOnlyMetricsFor(s),f=d.BufferState.length>0?d.BufferState[d.BufferState.length-1]:null,p=(0,i.default)(r).create(i.default.NO_CHANGE,i.default.WEAK);return h>a-g||null===f||(function(e,t){c[e]=c[e]||{},c[e].state=t,t!==o.default.BUFFER_LOADED||c[e].firstBufferLoadedEvent||(c[e].firstBufferLoadedEvent=!0)}(s,f.state),f.state===o.default.BUFFER_EMPTY&&void 0!==c[s].firstBufferLoadedEvent&&(p=(0,i.default)(r).create(0,i.default.STRONG)),p.value!==i.default.NO_CHANGE&&p.value!==u&&n("InsufficientBufferRule requesting switch to index: ",p.value,"type: ",s," Priority: ",p.priority===i.default.DEFAULT?"Default":p.priority===i.default.STRONG?"Strong":"Weak"),g=a),void t(p)},reset:function(){a.off(u.default.PLAYBACK_SEEKING,t,f),c={},g=0}},c={},g=0,h=1e3,a.on(u.default.PLAYBACK_SEEKING,t,f),f}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../SwitchRequest.js")),o=n(e("../../controllers/BufferController.js")),s=n(e("../../../core/EventBus.js")),u=n(e("../../../core/events/Events.js")),l=n(e("../../../core/FactoryMaker.js")),d=n(e("../../../core/Debug.js"));a.__dashjs_factory_name="InsufficientBufferRule",r.default=l.default.getClassFactory(a),t.exports=r.default},{"../../../core/Debug.js":7,"../../../core/EventBus.js":8,"../../../core/FactoryMaker.js":9,"../../../core/events/Events.js":11,"../../controllers/BufferController.js":52,"../SwitchRequest.js":119}],126:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(){v=[],y=(0,u.default)(g).getInstance()}function r(e,t){v[e]=v[e]||[],t!==1/0&&t!==v[e][v[e].length-1]&&v[e].push(t)}function n(e,t){var r=0,n=t?d:c,a=v[e],i=a.length;if(n=n>i?i:n,i>0){for(var o=0,s=i-n;i>s;s++)o+=a[s];r=o/n}return a.length>n&&a.shift(),r/1e3*y.getBandwidthSafetyFactor()}var a,d=2,c=3,g=this.context,h=(0,f.default)(g).getInstance().log,p=e.dashMetrics,m=e.metricsModel,v=void 0,y=void 0;return a={execute:function(e,t){var a,u,d,f=e.getMediaInfo(),c=f.type,v=e.getCurrentValue(),y=m.getReadOnlyMetricsFor(c),_=e.getStreamProcessor(),E=_.getABRController(),T=_.isDynamic(),M=p.getCurrentHttpRequest(y),S=y.BufferState.length>0?y.BufferState[y.BufferState.length-1]:null,R=y.BufferLevel.length>0?y.BufferLevel[y.BufferLevel.length-1]:null,I=(0,i.default)(g).create(i.default.NO_CHANGE,i.default.WEAK);if(y&&M&&M.type===l.default.MEDIA_SEGMENT_TYPE&&S&&R){if(M.trace&&M.trace.length&&(a=(M._tfinish.getTime()-M.tresponse.getTime())/1e3,u=M.trace.reduce(function(e,t){return e+t.b[0]},0),r(c,Math.round(8*u)/a)),d=Math.round(n(c,T)),E.setAverageThroughput(c,d),E.getAbandonmentStateFor(c)!==s.default.ABANDON_LOAD){if(S.state===o.default.BUFFER_LOADED||T){var C=E.getQualityForBitrate(f,d);_.getScheduleController().setTimeToLoadDelay(0),I=(0,i.default)(g).create(C,i.default.DEFAULT)}I.value!==i.default.NO_CHANGE&&I.value!==v&&h("ThroughputRule requesting switch to index: ",I.value,"type: ",c," Priority: ",I.priority===i.default.DEFAULT?"Default":I.priority===i.default.STRONG?"Strong":"Weak","Average throughput",Math.round(d),"kbps")}t(I)}else t(I)},reset:function(){t()}},t(),a}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../SwitchRequest.js")),o=n(e("../../controllers/BufferController.js")),s=n(e("../../controllers/AbrController.js")),u=n(e("../../models/MediaPlayerModel.js")),l=n(e("../../vo/metrics/HTTPRequest.js")),d=n(e("../../../core/FactoryMaker.js")),f=n(e("../../../core/Debug.js"));a.__dashjs_factory_name="ThroughputRule",r.default=d.default.getClassFactory(a),t.exports=r.default},{"../../../core/Debug.js":7,"../../../core/FactoryMaker.js":9,"../../controllers/AbrController.js":49,"../../controllers/BufferController.js":52,"../../models/MediaPlayerModel.js":90,"../../vo/metrics/HTTPRequest.js":166,"../SwitchRequest.js":119}],127:[function(e,t,r){"use strict";function n(e){var t=e.blacklistController;return{select:function(e){var r,n=0;return e&&e.some(function(e,r){return n=r,!t.contains(e.serviceLocation)})&&(r=e[n]),r}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../../core/FactoryMaker.js"));n.__dashjs_factory_name="BasicSelector",r.default=a.default.getClassFactory(n),t.exports=r.default},{"../../../core/FactoryMaker.js":9}],128:[function(e,t,r){"use strict";function n(e){var t=e.blacklistController;return{select:function(e){return e&&function(e){var t,r,n=0,a=[],i=0;return r=e.sort(function(e,t){var r=e.dvb_priority-t.dvb_priority;return isNaN(r)?0:r}).filter(function(e,t,r){return!t||r[0].dvb_priority&&e.dvb_priority&&r[0].dvb_priority===e.dvb_priority}),r.length?(r.length>1&&(r.forEach(function(e){n+=e.dvb_weight,a.push(n)}),t=Math.floor(Math.random()*(n-1)),a.every(function(e,r){return i=r,!(e>t)})),r[i]):void 0}(function(e){var r=[];return e.filter(function(e){return!t.contains(e.serviceLocation)||(e.dvb_priority&&r.push(e.dvb_priority),!1)}).filter(function(e){return!r.length||!e.dvb_priority||-1===r.indexOf(e.dvb_priority)})}(e))}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../../core/FactoryMaker.js"));n.__dashjs_factory_name="DVBSelector",r.default=a.default.getClassFactory(n),t.exports=r.default},{"../../../core/FactoryMaker.js":9}],129:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t,r=this.context,n=e.dashMetrics,a=e.metricsModel,s=e.textSourceBuffer,u=void 0;return t={execute:function(e){var t=e.getCurrentRepresentationInfo().mediaInfo.type,r=a.getReadOnlyMetricsFor(t);return n.getCurrentBufferLevel(r)<function(e,t){var r=e.getCurrentRepresentationInfo(),n=r.mediaInfo.streamInfo,a=e.getABRController(),i=n.manifestInfo.duration>=u.getLongFormContentDurationThreshold();return"fragmentedText"===t?s.getAllTracksAreDisabled()?0:r.fragmentDuration:a.isPlayingAtTopQuality(n)?i?u.getBufferTimeAtTopQualityLongForm():u.getBufferTimeAtTopQuality():u.getStableBufferTime()}(e,t)},reset:function(){}},u=(0,i.default)(r).getInstance(),(0,o.default)(r).getInstance(),t}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../models/MediaPlayerModel.js")),o=n(e("../../controllers/PlaybackController.js")),s=n(e("../../../core/FactoryMaker.js"));a.__dashjs_factory_name="BufferLevelRule",r.default=s.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../controllers/PlaybackController.js":57,"../../models/MediaPlayerModel.js":90}],130:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t=this.context,r=(0,i.default)(t).getInstance().log,n=e.adapter,a=e.sourceBufferController,o=e.virtualBuffer,s=e.textSourceBuffer;return{execute:function(e){var t=e.getCurrentRepresentationInfo(),i=t.mediaInfo,u=i.type,l=i.streamInfo.id,d=e.getScheduleController(),f=d.getSeekTarget(),c=!isNaN(f),g=!c,h=c?f:n.getIndexHandlerTime(e),p=e.getBuffer(),m=null,v=void 0,y=void 0;if(isNaN(h)||"fragmentedText"===u&&s.getAllTracksAreDisabled())return null;if(c&&d.setSeekTarget(NaN),p&&null!==(m=a.getBufferRange(e.getBuffer(),h))&&(v=o.getChunks({streamId:l,mediaType:u,appended:!0,mediaInfo:i,forRange:m}))&&v.length>0){var _=h;h=v[v.length-1].bufferedRange.end,r("Prior to making a request for time, NextFragmentRequestRule is aligning index handler's currentTime with bufferedRange.end.",_," was changed to ",h)}return(y=n.getFragmentRequestForTime(e,t,h,{keepIdx:g}))&&e.getFragmentModel().isFragmentLoaded(y)&&(y=n.getNextFragmentRequest(e,t)),y&&(n.setIndexHandlerTime(e,y.startTime+y.duration),y.delayLoadingTime=(new Date).getTime()+d.getTimeToLoadDelay(),d.setTimeToLoadDelay(0)),y}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../core/Debug.js")),o=n(e("../../../core/FactoryMaker.js"));a.__dashjs_factory_name="NextFragmentRequestRule",r.default=o.default.getClassFactory(a),t.exports=r.default},{"../../../core/Debug.js":7,"../../../core/FactoryMaker.js":9}],131:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){function t(e,r,n,a){var i;null===a?(i=c.generateFragmentRequestForTime(S,v,e),t(e,r,n,i)):(f.on(s.default.CHECK_FOR_EXISTENCE_COMPLETED,function t(a){f.off(s.default.CHECK_FOR_EXISTENCE_COMPLETED,t,this),a.exists?r(a.request,e):n(a.request,e)},this),M.checkForExistence(a))}function r(e,o){var s,u,l;return y?void a(!1,o):void((s=(l=o-h)>0?h-l:h+Math.abs(l)+m)<p.start&&s>p.end?T((0,i.default)(d).create(null,E)):(u=c.getFragmentRequestForTime(S,v,s,{ignoreIsFinished:!0}),t(s,n,r,u)))}function n(e,r){var n,o,s=e.startTime;if(!y){if(!v.fragmentDuration)return void T((0,i.default)(d).create(s,E));if(y=!0,p.end=s+2*m,r===h)return o=r+_,n=c.getFragmentRequestForTime(S,v,o,{ignoreIsFinished:!0}),void t(o,function(){a(!0,o)},function(){T((0,i.default)(d).create(o,E))},n)}a(!0,r)}function a(e,a){var o;e?p.start=a:p.end=a,Math.floor(p.end-p.start)<=_?T((0,i.default)(d).create(e?a:a-_,E)):t(o=(p.start+p.end)/2,n,r,c.getFragmentRequestForTime(S,v,o,{ignoreIsFinished:!0}))}var u,d=this.context,f=(0,o.default)(d).getInstance(),c=e.adapter,g=e.timelineConverter,h=void 0,p=void 0,m=void 0,v=void 0,y=void 0,_=void 0,E=void 0,T=void 0,M=void 0,S=void 0;return u={execute:function(e,a){var o,s;if(T=a,S=e.getStreamProcessor(),M=S.getFragmentLoader(),v=e.getTrackInfo(),_=v.fragmentDuration,s=v.DVRWindow,h=s.end,v.useCalculatedLiveEdgeTime){var u=g.getExpectedLiveEdge();return g.setExpectedLiveEdge(h),void T((0,i.default)(d).create(u,E))}p={start:Math.max(0,h-l),end:h+l},m=Math.floor((s.end-s.start)/2),o=c.getFragmentRequestForTime(S,v,h,{ignoreIsFinished:!0}),t(h,n,r,o)},reset:function(){h=NaN,p=null,m=NaN,v=null,y=!1,_=NaN,S=null,M=null}},h=NaN,p=null,m=NaN,v=null,y=!1,_=NaN,E=i.default.DEFAULT,u}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../SwitchRequest.js")),o=n(e("../../../core/EventBus.js")),s=n(e("../../../core/events/Events.js")),u=n(e("../../../core/FactoryMaker.js")),l=43200;a.__dashjs_factory_name="LiveEdgeBinarySearchRule",r.default=u.default.getClassFactory(a),t.exports=r.default},{"../../../core/EventBus.js":8,"../../../core/FactoryMaker.js":9,"../../../core/events/Events.js":11,"../SwitchRequest.js":119}],132:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(e){var t=this.context,r=e.timelineConverter;return{execute:function(e,n){var a=e.getTrackInfo(),o=a.DVRWindow.end,s=i.default.DEFAULT;if(a.useCalculatedLiveEdgeTime){var u=r.getExpectedLiveEdge();r.setExpectedLiveEdge(o),n((0,i.default)(t).create(u,s))}else n((0,i.default)(t).create(o,s))}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../SwitchRequest.js")),o=n(e("../../../core/FactoryMaker.js"));a.__dashjs_factory_name="LiveEdgeWithTimeSynchronizationRule",r.default=o.default.getClassFactory(a),t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../SwitchRequest.js":119}],133:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e=this.context,t=void 0,r=void 0;return{initialize:function(){r=[],(t=[]).push((0,u.default)(e).create({timelineConverter:(0,o.default)(e).getInstance()})),r.push((0,s.default)(e).create({timelineConverter:(0,o.default)(e).getInstance(),adapter:(0,l.default)(e).getInstance()}))},getRules:function(e){switch(e){case d:return t;case f:return r;default:return null}}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../../core/FactoryMaker.js")),o=n(e("../../../dash/utils/TimelineConverter.js")),s=n(e("./LiveEdgeBinarySearchRule.js")),u=n(e("./LiveEdgeWithTimeSynchronizationRule.js")),l=n(e("../../../dash/DashAdapter.js")),d="withAccurateTimeSourceRules",f="bestGuestRules";a.__dashjs_factory_name="SynchronizationRulesCollection";var c=i.default.getSingletonFactory(a);c.TIME_SYNCHRONIZED_RULES=d,c.BEST_GUESS_RULES=f,r.default=c,t.exports=r.default},{"../../../core/FactoryMaker.js":9,"../../../dash/DashAdapter.js":13,"../../../dash/utils/TimelineConverter.js":25,"./LiveEdgeBinarySearchRule.js":131,"./LiveEdgeWithTimeSynchronizationRule.js":132}],134:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e,t=this.context,r=(0,i.default)(t).getInstance(),n=(0,s.default)(t).getInstance(),a=void 0,f=void 0,h=void 0,p=void 0;return e={chooseSelectorFromManifest:function(e){p=n.getIsDVB(e)?h:f},select:function(e){var t=e.baseUrls,n=e.selectedIdx;if(!isNaN(n))return t[n];var a=p.select(t);return a?(e.selectedIdx=t.indexOf(a),a):void r.trigger(o.default.URL_RESOLUTION_FAILED,{error:new Error(c,g)})},reset:function(){a.reset()}},a=(0,u.default)(t).create({updateEventName:o.default.SERVICE_LOCATION_BLACKLIST_CHANGED,loadFailedEventName:o.default.FRAGMENT_LOADING_COMPLETED}),f=(0,d.default)(t).create({blacklistController:a}),h=(0,l.default)(t).create({blacklistController:a}),p=f,e}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/EventBus.js")),o=n(e("../../core/events/Events.js")),s=n(e("../../dash/models/DashManifestModel.js")),u=n(e("../controllers/BlacklistController.js")),l=n(e("../rules/baseUrlResolution/DVBSelector.js")),d=n(e("../rules/baseUrlResolution/BasicSelector.js")),f=n(e("../../core/FactoryMaker.js")),c=1,g="Failed to resolve a valid URL";a.__dashjs_factory_name="BaseURLSelector";var h=f.default.getClassFactory(a);h.URL_RESOLUTION_FAILED_GENERIC_ERROR_CODE=c,h.URL_RESOLUTION_FAILED_GENERIC_ERROR_MESSAGE=g,r.default=h,t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../../dash/models/DashManifestModel.js":19,"../controllers/BlacklistController.js":51,"../rules/baseUrlResolution/BasicSelector.js":127,"../rules/baseUrlResolution/DVBSelector.js":128}],135:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e=this.context;return{parse:function(t){if(!t)return null;void 0===t.fileStart&&(t.fileStart=0);var r=s.default.parseBuffer(t),n=(0,i.default)(e).create();return n.setData(r),n}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("./IsoFile.js")),o=n(e("../../core/FactoryMaker.js")),s=n(e("codem-isoboxer"));a.__dashjs_factory_name="BoxParser",r.default=o.default.getSingletonFactory(a),t.exports=r.default},{"../../core/FactoryMaker.js":9,"./IsoFile.js":140,"codem-isoboxer":6}],136:[function(e,t,r){"use strict";function n(){var e,t=void 0;return e={supportsMediaSource:function(){var e="WebKitMediaSource"in window,t="MediaSource"in window;return e||t},supportsEncryptedMedia:function(){return t},supportsCodec:function(e,t){var r=e.canPlayType(t);return"probably"===r||"maybe"===r},setEncryptedMediaSupported:function(e){t=e}},t=!1,e}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js"));n.__dashjs_factory_name="Capabilities",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../core/FactoryMaker.js":9}],137:[function(e,t,r){"use strict";function n(){return{customTimeRangeArray:[],length:0,add:function(e,t){var r=0;for(r=0;r<this.customTimeRangeArray.length&&e>this.customTimeRangeArray[r].start;r++);for(this.customTimeRangeArray.splice(r,0,{start:e,end:t}),r=0;r<this.customTimeRangeArray.length-1;r++)this.mergeRanges(r,r+1)&&r--;this.length=this.customTimeRangeArray.length},clear:function(){this.customTimeRangeArray=[],this.length=0},remove:function(e,t){for(var r=0;r<this.customTimeRangeArray.length;r++)if(e<=this.customTimeRangeArray[r].start&&t>=this.customTimeRangeArray[r].end)this.customTimeRangeArray.splice(r,1),r--;else{if(e>this.customTimeRangeArray[r].start&&t<this.customTimeRangeArray[r].end){this.customTimeRangeArray.splice(r+1,0,{start:t,end:this.customTimeRangeArray[r].end}),this.customTimeRangeArray[r].end=e;break}e>this.customTimeRangeArray[r].start&&e<this.customTimeRangeArray[r].end?this.customTimeRangeArray[r].end=e:t>this.customTimeRangeArray[r].start&&t<this.customTimeRangeArray[r].end&&(this.customTimeRangeArray[r].start=t)}this.length=this.customTimeRangeArray.length},mergeRanges:function(e,t){var r=this.customTimeRangeArray[e],n=this.customTimeRangeArray[t];return r.start<=n.start&&n.start<=r.end&&r.end<=n.end?(r.end=n.end,this.customTimeRangeArray.splice(t,1),!0):n.start<=r.start&&r.start<=n.end&&n.end<=r.end?(r.start=n.start,this.customTimeRangeArray.splice(t,1),!0):n.start<=r.start&&r.start<=n.end&&r.end<=n.end?(this.customTimeRangeArray.splice(e,1),!0):r.start<=n.start&&n.start<=r.end&&n.end<=r.end&&(this.customTimeRangeArray.splice(t,1),!0)},start:function(e){return this.customTimeRangeArray[e].start},end:function(e){return this.customTimeRangeArray[e].end}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js"));n.__dashjs_factory_name="CustomTimeRanges",r.default=a.default.getClassFactory(n),t.exports=r.default},{"../../core/FactoryMaker.js":9}],138:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e){if(void 0!==g)return g;var t;g=!1;try{"undefined"!=typeof window&&(t=window[e])}catch(e){return i("Warning: DOMStorage access denied: "+e.message),g}if(!t||e!==f&&e!==c)return g;try{t.setItem("1","1"),t.removeItem("1"),g=!0}catch(e){i("Warning: DOMStorage is supported, but cannot be used: "+e.message)}return g}function t(){var e=6e5;return Math.round((new Date).getTime()/e)*e}function r(t,r){return e(t)&&h["get"+r+"CachingInfo"]().enabled}var n,a=this.context,i=(0,s.default)(a).getInstance().log,g=void 0,h=void 0;return n={getSavedBitrateSettings:function(e){var t=NaN;if(r(f,"LastBitrate")){var n=l.replace(/\?/,e),a=JSON.parse(localStorage.getItem(n))||{},o=(new Date).getTime()-parseInt(a.timestamp,10)>=h.getLastBitrateCachingInfo().ttl||!1,s=parseInt(a.bitrate,10);isNaN(s)||o?o&&localStorage.removeItem(n):(t=s,i("Last saved bitrate for "+e+" was "+s))}return t},setSavedBitrateSettings:function(e,n){if(r(f,"LastBitrate")&&n){var a=l.replace(/\?/,e);try{localStorage.setItem(a,JSON.stringify({bitrate:n/1e3,timestamp:t()}))}catch(e){i(e.message)}}},getSavedMediaSettings:function(e){if(!r(f,"LastMediaSettings"))return null;var t=d.replace(/\?/,e),n=JSON.parse(localStorage.getItem(t))||{},a=(new Date).getTime()-parseInt(n.timestamp,10)>=h.getLastMediaSettingsCachingInfo().ttl||!1,i=n.settings;return a&&(localStorage.removeItem(t),i=null),i},setSavedMediaSettings:function(e,n){if(r(f,"LastMediaSettings")){var a=d.replace(/\?/,e);try{localStorage.setItem(a,JSON.stringify({settings:n,timestamp:t()}))}catch(e){i(e.message)}}},isSupported:e},h=(0,o.default)(a).getInstance(),e(f)&&u.forEach(function(e){var t=localStorage.getItem(e.oldKey);if(t){localStorage.removeItem(e.oldKey);try{localStorage.setItem(e.newKey,t)}catch(e){i(e.message)}}}),n}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/FactoryMaker.js")),o=n(e("../models/MediaPlayerModel.js")),s=n(e("../../core/Debug.js")),u=[{oldKey:"dashjs_vbitrate",newKey:"dashjs_video_bitrate"},{oldKey:"dashjs_abitrate",newKey:"dashjs_audio_bitrate"},{oldKey:"dashjs_vsettings",newKey:"dashjs_video_settings"},{oldKey:"dashjs_asettings",newKey:"dashjs_audio_settings"}],l="dashjs_?_bitrate",d="dashjs_?_settings",f="localStorage",c="sessionStorage";a.__dashjs_factory_name="DOMStorage";var g=i.default.getSingletonFactory(a);r.default=g,t.exports=r.default},{"../../core/Debug.js":7,"../../core/FactoryMaker.js":9,"../models/MediaPlayerModel.js":90}],139:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){var e=this.context,t=(0,i.default)(e).getInstance();return{capabilityError:function(e){t.trigger(o.default.ERROR,{error:"capability",event:e})},downloadError:function(e,r,n){t.trigger(o.default.ERROR,{error:"download",event:{id:e,url:r,request:n}})},manifestError:function(e,r,n){t.trigger(o.default.ERROR,{error:"manifestError",event:{message:e,id:r,manifest:n}})},timedTextError:function(e,r,n){t.trigger(o.default.ERROR,{error:"cc",event:{message:e,id:r,cc:n}})},mediaSourceError:function(e){t.trigger(o.default.ERROR,{error:"mediasource",event:e})},mediaKeySessionError:function(e){t.trigger(o.default.ERROR,{error:"key_session",event:e})},mediaKeyMessageError:function(e){t.trigger(o.default.ERROR,{error:"key_message",event:e})}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/EventBus.js")),o=n(e("../../core/events/Events.js")),s=n(e("../../core/FactoryMaker.js"));a.__dashjs_factory_name="ErrorHandler";var u=s.default.getSingletonFactory(a);u.CAPABILITY_ERROR_MEDIASOURCE="mediasource",u.CAPABILITY_ERROR_MEDIAKEYS="mediakeys",u.DOWNLOAD_ERROR_ID_MANIFEST="manifest",u.DOWNLOAD_ERROR_ID_SIDX="SIDX",u.DOWNLOAD_ERROR_ID_CONTENT="content",u.DOWNLOAD_ERROR_ID_INITIALIZATION="initialization",u.DOWNLOAD_ERROR_ID_XLINK="xlink",u.MANIFEST_ERROR_ID_CODEC="codec",u.MANIFEST_ERROR_ID_PARSE="parse",u.MANIFEST_ERROR_ID_NOSTREAMS="nostreams",u.TIMED_TEXT_ERROR_ID_PARSE="parse",r.default=u,t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11}],140:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e){for(var t,n=a.fetchAll(e),i=[],o=0,s=n.length;s>o;o++)(t=r(n[o]))&&i.push(t);return i}function t(e,t,r){for(var n in r)t[n]=e[r[n]]}function r(e){if(!e)return null;var r,n,a=new i.default;switch(t(e,a,o),e.hasOwnProperty("_incomplete")&&(a.isComplete=!e._incomplete),a.type){case"sidx":if(t(e,a,s),a.references)for(r=0,n=a.references.length;n>r;r++)t(e.references[r],a.references[r],u);break;case"emsg":t(e,a,l);break;case"mdhd":t(e,a,d);break;case"mfhd":t(e,a,f);break;case"tfhd":t(e,a,c);break;case"tfdt":t(e,a,g);break;case"trun":if(t(e,a,h),a.samples)for(r=0,n=a.samples.length;n>r;r++)t(e.samples[r],a.samples[r],p)}return a}var n,a=void 0,o=void 0,s=void 0,u=void 0,l=void 0,d=void 0,f=void 0,c=void 0,g=void 0,h=void 0,p=void 0;return n={getBox:function(e){return e&&a&&a.boxes&&0!==a.boxes.length?r(a.fetch(e)):null},getBoxes:e,setData:function(e){a=e},getLastBox:function(){if(!a||!a.boxes||!a.boxes.length)return null;var t=e(a.boxes[a.boxes.length-1].type);return t[t.length-1]},getOffset:function(){return a._cursor.offset}},o={offset:"_offset",size:"size",type:"type"},s={references:"references",timescale:"timescale",earliest_presentation_time:"earliest_presentation_time",first_offset:"first_offset"},u={reference_type:"reference_type",referenced_size:"referenced_size",subsegment_duration:"subsegment_duration"},l={id:"id",value:"value",timescale:"timescale",scheme_id_uri:"scheme_id_uri",presentation_time_delta:"presentation_time_delta",event_duration:"event_duration",message_data:"message_data"},d={timescale:"timescale"},f={sequence_number:"sequence_number"},c={base_data_offset:"base_data_offset",sample_description_index:"sample_description_index",default_sample_duration:"default_sample_duration",default_sample_size:"default_sample_size",default_sample_flags:"default_sample_flags",flags:"flags"},g={version:"version",baseMediaDecodeTime:"baseMediaDecodeTime",flags:"flags"},h={sample_count:"sample_count",first_sample_flags:"first_sample_flags",data_offset:"data_offset",flags:"flags",samples:"samples"},p={sample_size:"sample_size",sample_duration:"sample_duration",sample_composition_time_offset:"sample_composition_time_offset"},n}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../vo/IsoBox.js")),o=n(e("../../core/FactoryMaker.js"));a.__dashjs_factory_name="IsoFile",r.default=o.default.getClassFactory(a),t.exports=r.default},{"../../core/FactoryMaker.js":9,"../vo/IsoBox.js":152}],141:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(){h=!1,p=NaN}function t(e){var t=((new Date).getTime()-p)/1e3;v=e.value,a.trigger(u.default.LIVE_EDGE_SEARCH_COMPLETED,{liveEdge:v,searchTime:t,error:null===v?new o.default(f,"live edge has not been found",null):null})}function r(e){!c.isDynamic()||h||e.error||(y=d.isTimeSyncCompleted()?i.default.TIME_SYNCHRONIZED_RULES:i.default.BEST_GUESS_RULES,m=(0,i.default)(n).getInstance().getRules(y),h=!0,p=(new Date).getTime(),g.applyRules(m,c,t,null,function(e,t){return t}))}var n=this.context,a=(0,s.default)(n).getInstance(),d=void 0,c=void 0,g=void 0,h=void 0,p=void 0,m=void 0,v=void 0,y=void 0;return{initialize:function(e,t){d=e,c=t,h=!1,p=NaN,v=null,g=(0,l.default)(n).getInstance(),y=i.default.BEST_GUESS_RULES,a.on(u.default.STREAM_INITIALIZED,r,this)},abortSearch:e,getLiveEdge:function(){return v},reset:function(){a.off(u.default.STREAM_INITIALIZED,r,this),e(),v=null,d=null,c=null,h=!1,p=NaN,y=null,g=null}}}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../rules/synchronization/SynchronizationRulesCollection.js")),o=n(e("../vo/Error.js")),s=n(e("../../core/EventBus.js")),u=n(e("../../core/events/Events.js")),l=n(e("../rules/RulesController.js")),d=n(e("../../core/FactoryMaker.js")),f=1;a.__dashjs_factory_name="LiveEdgeFinder";var c=d.default.getSingletonFactory(a);c.LIVE_EDGE_NOT_FOUND_ERROR_CODE=f,r.default=c,t.exports=r.default},{"../../core/EventBus.js":8,"../../core/FactoryMaker.js":9,"../../core/events/Events.js":11,"../rules/RulesController.js":118,"../rules/synchronization/SynchronizationRulesCollection.js":133,"../vo/Error.js":149}],142:[function(e,t,r){"use strict";function n(){return{areSimpleEquivalent:function(e,t){return JSON.stringify(e)===JSON.stringify(t)}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js"));n.__dashjs_factory_name="ObjectUtils",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../core/FactoryMaker.js":9}],143:[function(e,t,r){"use strict";function n(){return{modifyRequestURL:function(e){return e},modifyRequestHeader:function(e){return e}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js"));n.__dashjs_factory_name="RequestModifier",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../core/FactoryMaker.js":9}],144:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e){var t,r,n;if(!S.test(e))return NaN;if(t=e.split(":"),r=parseFloat(t[0])*u+parseFloat(t[1])*l+parseFloat(t[2]),t[3]){if(!(n=R.tt.frameRate)||isNaN(n))return NaN;r+=parseFloat(t[3])/n}return r}function t(e,t){var r=Object.keys(e).filter(function(r){return("xmlns"===r.split(":")[0]||"xmlns"===r.split(":")[1])&&e[r]===t}).map(function(e){return e.split(":")[2]||e.split(":")[1]});return 1!=r.length?null:r[0]}function r(e,t){for(var n in e)if(e.hasOwnProperty(n)){if(("object"==typeof e[n]||e[n]instanceof Object)&&!Array.isArray(e[n]))r(e[n],t);else if(Array.isArray(e[n]))for(var a=0;a<e[n].length;a++)r(e[n][a],t);e[n.slice(n.indexOf(t)+t.length+1)]=e[n],delete e[n]}}function n(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function a(e){var t=e.slice(1).match(/.{2}/g),r=parseFloat(parseInt(parseInt(t[3],16)/255*1e3,10)/1e3),n=t.slice(0,3).map(function(e){return parseInt(e,16)});return"rgba("+n.join(",")+","+r+");"}function i(e,t){for(var r=0;r<t.length;r++)if(t[r].indexOf(e)>-1)return!0;return!1}function d(e,t){for(var r=0;r<t.length;r++)if(t[r].indexOf(e)>-1)return t[r];return null}function f(e,t){t.splice(t.indexOf(d(e,t)),1)}function c(e,t){for(var r=0;r<e.length;r++)for(var n=0;n<t.length;n++)e[r]&&e[r].split(":")[0].indexOf(t[n].split(":")[0])>-1&&e.splice(r,1);return e.concat(t)}function g(e,t){var r=[];return e.match(/\S+/g).forEach(function(e){var o=function(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(n["xml:id"]===t||n.id===t)return n}return null}(I,e);if(o){var s=function(e,t){var r,o=[];for(var s in e)if(e.hasOwnProperty(s)){var u=s.replace("ebutts:","");e[u=n(u=(u=u.replace("xml:","")).replace("tts:",""))]=e[s],delete e[s]}if("line-padding"in e){var l=parseFloat(e["line-padding"].slice(e["line-padding"].indexOf(":")+1,e["line-padding"].indexOf("c")));"id"in e&&(D[e.id]=l);var d=l*t[0]+"px;";o.push("padding-left:"+d),o.push("padding-right:"+d)}if("font-size"in e){var c=parseFloat(e["font-size"].slice(e["font-size"].indexOf(":")+1,e["font-size"].indexOf("%")));"id"in e&&(A[e.id]=c);var g=c/100*t[1]+"px;";o.push("font-size:"+g)}if("line-height"in e)if("normal"===e["line-height"])o.push("line-height: normal;");else{var h=parseFloat(e["line-height"].slice(e["line-height"].indexOf(":")+1,e["line-height"].indexOf("%")));"id"in e&&(j[e.id]=h);var p=h/100*t[1]+"px;";o.push("line-height:"+p)}return"font-family"in e&&(e["font-family"]in P?o.push(P[e["font-family"]]):o.push("font-family:"+e["font-family"]+";")),"text-align"in e&&e["text-align"]in w&&(o.push(w[e["text-align"]][0]),o.push(w[e["text-align"]][1])),"multi-row-align"in e&&(i("text-align",o)&&"auto"!=e["multi-row-align"]&&f("text-align",o),e["multi-row-align"]in N&&o.push(N[e["multi-row-align"]])),"background-color"in e&&(e["background-color"].indexOf("#")>-1&&e["background-color"].length-1==8?(r=a(e["background-color"]),o.push("background-color: "+r)):o.push("background-color:"+e["background-color"]+";")),"color"in e&&(e.color.indexOf("#")>-1&&e.color.length-1==8?(r=a(e.color),o.push("color: "+r)):o.push("color:"+e.color+";")),"wrap-option"in e&&(e["wrap-option"]in x?o.push(x[e["wrap-option"]]):o.push("white-space:"+e["wrap-option"])),"unicode-bidi"in e&&(e["unicode-bidi"]in L?o.push(L[e["unicode-bidi"]]):o.push("unicode-bidi:"+e["unicode-bidi"])),"font-style"in e&&o.push("font-style:"+e["font-style"]+";"),"font-weight"in e&&o.push("font-weight:"+e["font-weight"]+";"),"direction"in e&&o.push("direction:"+e.direction+";"),"text-decoration"in e&&o.push("text-decoration:"+e["text-decoration"]+";"),R.tt.hasOwnProperty("xml:space")&&"preserve"===R.tt["xml:space"]&&o.push("white-space: pre;"),o}(JSON.parse(JSON.stringify(o)),t);r=r.concat(s)}}),r}function h(e,t){var r=[];for(var a in e){var i=a.replace("tts:","");e[i=n(i=i.replace("xml:",""))]=e[a],i!==a&&delete e[a]}if("extent"in e){var o=e.extent.split(/\s/);r.push("width: "+o[0]+";"),r.push("height: "+o[1]+";")}if("origin"in e){var s=e.origin.split(/\s/);r.push("left: "+s[0]+";"),r.push("top: "+s[1]+";")}if("display-align"in e&&r.push(F[e["display-align"]]),"writing-mode"in e&&r.push(B[e["writing-mode"]]),"style"in e){var u=g(e.style,t);r=r.concat(u)}return"padding"in e&&r.push("padding:"+e.padding+";"),"overflow"in e&&r.push("overflow:"+e.overflow+";"),"show-background"in e&&r.push("show-background:"+e["show-background"]+";"),"id"in e&&r.push("regionID:"+e.id+";"),r}function p(e,t){var r=[];return e.match(/\S+/g).forEach(function(e){var n=function(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(n["xml:id"]===t||n.id===t)return n}return null}(C,e);if(n){var a=h(JSON.parse(JSON.stringify(n)),t);r=r.concat(a)}}),r}function m(e,t){var r=document.createElement("div");return e.forEach(function(e){if(!e.hasOwnProperty("metadata"))if(e.hasOwnProperty("span")){var n=e.span.__children,a=document.createElement("span");if(e.span.hasOwnProperty("style")){var i=g(e.span.style,t);a.className="spanPadding "+e.span.style,a.style.cssText=i.join(" ")}n.forEach(function(e){if(!n.hasOwnProperty("metadata"))if(e.hasOwnProperty("#text")){var t=document.createTextNode(e["#text"]);a.appendChild(t)}else if("br"in e){a.hasChildNodes()&&r.appendChild(a);var i=document.createElement("br");i.className="lineBreak",r.appendChild(i);var o=document.createElement("span");o.className=a.className,o.style.cssText=a.style.cssText,a=o}}),r.appendChild(a)}else if(e.hasOwnProperty("br")){var o=document.createElement("br");o.className="lineBreak",r.appendChild(o)}else if(e.hasOwnProperty("#text")){var s=document.createElement("span");s.textContent=e["#text"],r.appendChild(s)}}),r}function v(e,t,r){var n,a,i=[],o=e.region,s=t.region;return s&&(n=p(s,r)),o?(a=i.concat(p(o,r)),i=n?c(n,a):a):n&&(i=n),_(i,b),i}function y(e,t){var r,n,a,i=[],o=e.style,s=R.tt.body.style,u=R.tt.body.div.style,l="";return s&&(r=g(s,t),l="paragraph "+s),u&&(n=g(u,t),r?(n=c(r,n),l+=" "+u):l="paragraph "+u),o?(a=g(o,t),r&&n?(i=c(n,a),l+=" "+o):r?(i=c(r,a),l+=" "+o):n?(i=c(n,a),l+=" "+o):(i=a,l="paragraph "+o)):r&&!n?i=r:!r&&n&&(i=n),_(i,O),[i,l]}function _(e,t){for(var r in t)t.hasOwnProperty(r)&&(i(r,e)||e.push(r+":"+t[r]))}var E,T=this.context,M=(0,s.default)(T).getInstance().log,S=void 0,R=void 0,I=void 0,C=void 0,A=void 0,j=void 0,D=void 0,b=void 0,O=void 0,P=void 0,w=void 0,N=void 0,x=void 0,L=void 0,F=void 0,B=void 0,k=void 0;return E={parse:function(n,a,s){var u,l,c=void 0,g="",p=new o.default([],"",!1);if(!(R=p.xml_str2json(n)))throw"TTML document could not be parsed";k.getTTMLRenderingDiv()&&(c="html");var _=t(R,"http://www.w3.org/ns/ttml");if(_&&r(R,_),!(u=R.tt))throw"TTML document lacks tt element";if(!(l=u.head))throw"TTML document lacks head element";if(l.layout&&(C=l.layout.region_asArray),l.styling&&(I=l.styling.style_asArray),!u.body)throw"TTML document lacks body element";var E=R.tt.hasOwnProperty("ttp:cellResolution")?R.tt["ttp:cellResolution"].split(" ").map(parseFloat):[32,15],T=k.getElement().clientWidth,S=k.getElement().clientHeight,b=[T/E[0],S/E[1]];O["font-size"]=b[1]+"px;";var P=[];if(C)for(var w=0;w<C.length;w++)P.push(h(JSON.parse(JSON.stringify(C[w])),b));var N=t(R.tt,"http://www.w3.org/ns/ttml#parameter");R.tt.hasOwnProperty(N+":frameRate")&&(R.tt.frameRate=parseInt(R.tt[N+":frameRate"],10));var x=[];if(R.tt.body_asArray[0].__children.forEach(function(t){var r,n,o,u,l=t.div.p_asArray;l&&0!==l.length?l.forEach(function(l){if(l.hasOwnProperty("begin")&&l.hasOwnProperty("end"))r=e(l.begin),n=e(l.end);else{if(!l.span.hasOwnProperty("begin")||!l.span.hasOwnProperty("end"))throw g="TTML document has incorrect timing value";o=e(l.span.begin),u=e(l.span.end)}var h=o||r,p=u||n;if(void 0!==a&&void 0!==s){if(a>p||h>s)return void M("TTML: Cue interval "+h+"-"+p+" outside sample interval "+a+"-"+s+". Dropped");var _=!1,I=h,C=p;a>h&&(_=!0,h=a),p>s&&(_=!0,p=s),_&&M("TTML: Clipped cue "+I+"-"+C+" to "+h+"-"+p)}if(void 0!==l["smpte:backgroundImage"])for(var O=R.tt.head.metadata.image_asArray,w=0;w<O.length;w++)"#"+O[w]["xml:id"]==l["smpte:backgroundImage"]&&x.push({start:h,end:p,id:O[w]["xml:id"],data:"data:image/"+O[w].imagetype.toLowerCase()+";base64, "+O[w].__text,type:"image"});else if("html"===c){j={},D={},A={};var N="";if((l.hasOwnProperty("id")||l.hasOwnProperty("xml:id"))&&(N=l["xml:id"]||l.id),(isNaN(r)||isNaN(n))&&(isNaN(o)||isNaN(u)))throw g="TTML document has incorrect timing value";var L=v(l,t.div,b),F=y(l,b),B=F[1];F=F[0];var k=document.createElement("div");k.className=B;var U=m(l.__children,b);U.className="cueDirUniWrapper",i("unicode-bidi",F)&&(U.style.cssText+=d("unicode-bidi",F),f("unicode-bidi",F)),i("direction",F)&&(U.style.cssText+=d("direction",F),f("direction",F)),i("padding-left",F)&&i("padding-right",F)&&(U.innerHTML=function(e,t){for(var r=d("padding-left",t),n=d("padding-right",t),a=r.concat(" "+n+" "),i="",o="",s="",u=Array.prototype.slice.call(e.children),l=e.getElementsByClassName("lineBreak")[0],f=u.indexOf(l),c=[];-1!=f;)c.push(f),f=u.indexOf(l,f+1);var g="</span>",h="<br>",p='<span class="spanPadding" style="-webkit-box-decoration-break: clone; ';if(c.length)c.forEach(function(e,t){if(0===t){for(var r="",n=0;e>n;n++)i+=u[n].outerHTML,0===n&&(r=a.concat(u[n].style.cssText));i=p+r+'">'+i}for(var l="",d=e+1;d<u.length;d++)o+=u[d].outerHTML,d===u.length-1&&(l+=a.concat(u[d].style.cssText));o=p+l+'">'+o,i&&o&&t===c.length-1?s+=i+g+h+o+g:i&&o&&t!==c.length-1?s+=i+g+h+o+g+h:i&&!o?s+=i+g:!i&&o&&t===c.length-1?s+=o+g:!i&&o&&t!==c.length-1&&(s+=o+g+h)});else{for(var m="",v=0;v<u.length;v++)m+=u[v].style.cssText;s=p+a+m+'">'+e.innerHTML+g}return s}(U,F)),i("padding-left",F)&&i("padding-right",F)&&(f("padding-left",F),f("padding-right",F));var K="";if(i("regionID",L)){var H=d("regionID",L);K=H.slice(H.indexOf(":")+1,H.length-1)}F&&(k.style.cssText=F.join(" ")+"display:flex;"),L&&(L=L.join(" ")),k.appendChild(U);var q=document.createElement("div");q.appendChild(k),q.id="subtitle_"+N,q.style.cssText="position: absolute; margin: 0; display: flex; box-sizing: border-box; pointer-events: none;"+L,0===Object.keys(A).length&&(A.defaultFontSize="100"),x.push({start:h,end:p,type:"html",cueHTMLElement:q,regions:P,regionID:K,cueID:N,videoHeight:S,videoWidth:T,cellResolution:E,fontSize:A||{defaultFontSize:"100"},lineHeight:j,linePadding:D})}else{var Y="",G=l.__children;G.length&&G.forEach(function(e){if(e.hasOwnProperty("span")){var t=e.span.__children;t.forEach(function(e){t.hasOwnProperty("metadata")||(e.hasOwnProperty("#text")?Y+=e["#text"].replace(/[\r\n]+/gm," ").trim():"br"in e&&(Y+="\n"))})}else Y+=e.hasOwnProperty("br")?"\n":e["#text"].replace(/[\r\n]+/gm," ").trim()}),x.push({start:h,end:p,data:Y,type:"text"})}}):g="TTML document does not contain any cues"}),""!==g&&M(g),x.length>0)return x;throw g},setConfig:function(e){e&&e.videoModel&&(k=e.videoModel)}},S=/^([0-9][0-9]+):([0-5][0-9]):([0-5][0-9])|(60)(\.([0-9])+)?$/,A={},j={},D={},b={top:"85%;",left:"5%;",width:"90%;",height:"10%;","align-items":"flex-start;",overflow:"visible;","-ms-writing-mode":"lr-tb, horizontal-tb;","-webkit-writing-mode":"horizontal-tb;","-moz-writing-mode":"horizontal-tb;","writing-mode":"horizontal-tb;"},O={color:"rgb(255,255,255);",direction:"ltr;","font-family":"monospace, sans-serif;","font-style":"normal;","line-height":"normal;","font-weight":"normal;","text-align":"start;","justify-content":"flex-start;","text-decoration":"none;","unicode-bidi":"normal;","white-space":"normal;",width:"100%;"},P={monospace:"font-family: monospace;",sansSerif:"font-family: sans-serif;",serif:"font-family: serif;",monospaceSansSerif:"font-family: monospace, sans-serif;",monospaceSerif:"font-family: monospace, serif;",proportionalSansSerif:"font-family: Arial;",proportionalSerif:"font-family: Times New Roman;",default:"font-family: monospace, sans-serif;"},w={right:["justify-content: flex-end;","text-align: right;"],start:["justify-content: flex-start;","text-align: start;"],center:["justify-content: center;","text-align: center;"],end:["justify-content: flex-end;","text-align: end;"],left:["justify-content: flex-start;","text-align: left;"]},N={start:"text-align: start;",center:"text-align: center;",end:"text-align: end;",auto:""},x={wrap:"white-space: normal;",noWrap:"white-space: nowrap;"},L={normal:"unicode-bidi: normal;",embed:"unicode-bidi: embed;",bidiOverride:"unicode-bidi: bidi-override;"},F={before:"align-items: flex-start;",center:"align-items: center;",after:"align-items: flex-end;"},B={lrtb:"-webkit-writing-mode: horizontal-tb;writing-mode: horizontal-tb;",rltb:"-webkit-writing-mode: horizontal-tb;writing-mode: horizontal-tb;direction: rtl;unicode-bidi: bidi-override;",tbrl:"-webkit-writing-mode: vertical-rl;writing-mode: vertical-rl;-webkit-text-orientation: upright;text-orientation: upright;",tblr:"-webkit-writing-mode: vertical-lr;writing-mode: vertical-lr;-webkit-text-orientation: upright;text-orientation: upright;",lr:"-webkit-writing-mode: horizontal-tb;writing-mode: horizontal-tb;",rl:"-webkit-writing-mode: horizontal-tb;writing-mode: horizontal-tb;direction: rtl;",tb:"-webkit-writing-mode: vertical-rl;writing-mode: vertical-rl;-webkit-text-orientation: upright;text-orientation: upright;"},E}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/FactoryMaker.js")),o=n(e("../../../externals/xml2json.js")),s=n(e("../../core/Debug.js")),u=3600,l=60;a.__dashjs_factory_name="TTMLParser",r.default=i.default.getSingletonFactory(a),t.exports=r.default},{"../../../externals/xml2json.js":4,"../../core/Debug.js":7,"../../core/FactoryMaker.js":9}],145:[function(e,t,r){"use strict";function n(){var e=/^(?:(?:[a-z]+:)?\/)?\//i;return{parseBaseUrl:function(e){var t="";return-1!==e.indexOf("/")&&(-1!==e.indexOf("?")&&(e=e.substring(0,e.indexOf("?"))),t=e.substring(0,e.lastIndexOf("/")+1)),t},isRelative:function(t){return!e.test(t)}}}Object.defineProperty(r,"__esModule",{value:!0});var a=function(e){return e&&e.__esModule?e:{default:e}}(e("../../core/FactoryMaker.js"));n.__dashjs_factory_name="URLUtils",r.default=a.default.getSingletonFactory(n),t.exports=r.default},{"../../core/FactoryMaker.js":9}],146:[function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function a(){function e(e){var t=e.split(":"),r=t.length-1;return e=60*parseInt(t[r-1],10)+parseFloat(t[r]),2===r&&(e+=3600*parseInt(t[0],10)),e}function t(e){var t=e.split(l),n=t[1].split(f);return n.shift(),t[1]=n[0],n.shift(),{cuePoints:t,styles:r(n)}}function r(e){var t={};return e.forEach(function(e){if(e.split(/:/).length>1){var r=e.split(/:/)[1];r&&-1!=r.search(/%/)&&(r=parseInt(r.replace(/%/,""),10)),(e.match(/align/)||e.match(/A/))&&(t.align=r),(e.match(/line/)||e.match(/L/))&&(t.line=r),(e.match(/position/)||e.match(/P/))&&(t.position=r),(e.match(/size/)||e.match(/S/))&&(t.size=r)}}),t}function n(e,t){for(var r,n=t,a="",i="";""!==e[n]&&n<e.length;)n++;if((r=n-t)>1)for(var o=0;r>o;o++){if((i=e[t+o]).match(l)){a="";break}a+=i,o!==r-1&&(a+="\n")}else(i=e[t]).match(l)||(a=i);return decodeURI(a)}var a,i=this.context,s=(0,o.default)(i).getInstance().log,u=void 0,l=void 0,d=void 0,f=void 0;return a={parse:function(r){var a,i,o=[];a=(r=r.split(u)).length,i=-1;for(var f=0;a>f;f++){var c=r[f];if(c.length>0&&"WEBVTT"!==c&&c.match(l)){var g=t(c),h=g.cuePoints,p=g.styles,m=n(r,f+1),v=e(h[0].replace(d,"")),y=e(h[1].replace(d,""));!isNaN(v)&&!isNaN(y)&&v>=i&&y>v?""!==m?(i=v,o.push({start:v,end:y,data:m,styles:p})):s("Skipping cue due to empty/malformed cue text"):s("Skipping cue due to incorrect cue timing")}}return o}},u=/(?:\r\n|\r|\n)/gm,l=/-->/,d=/(^[\s]+|[\s]+$)/g,f=/\s\b/g,a}Object.defineProperty(r,"__esModule",{value:!0});var i=n(e("../../core/FactoryMaker.js")),o=n(e("../../core/Debug.js"));a.__dashjs_factory_name="VTTParser",r.default=i.default.getSingletonFactory(a),t.exports=r.default},{"../../core/Debug.js":7,"../../core/FactoryMaker.js":9}],147:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.mediaType=null,this.bitrate=null,this.width=null,this.height=null,this.qualityIndex=NaN},t.exports=r.default},{}],148:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.streamId=null,this.mediaInfo=null,this.segmentType=null,this.quality=NaN,this.index=NaN,this.bytes=null,this.start=NaN,this.end=NaN,this.duration=NaN},t.exports=r.default},{}],149:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(t,r,n){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.code=t||null,this.message=r||null,this.data=n||null},t.exports=r.default},{}],150:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.action=e.ACTION_DOWNLOAD,this.startTime=NaN,this.mediaType=null,this.mediaInfo=null,this.type=null,this.duration=NaN,this.timescale=NaN,this.range=null,this.url=null,this.serviceLocation=null,this.requestStartDate=null,this.firstByteDate=null,this.requestEndDate=null,this.quality=NaN,this.index=NaN,this.availabilityStartTime=null,this.availabilityEndTime=null,this.wallStartTime=null,this.bytesLoaded=NaN,this.bytesTotal=NaN,this.delayLoadingTime=NaN,this.responseType="arraybuffer"};n.ACTION_DOWNLOAD="download",n.ACTION_COMPLETE="complete",r.default=n,t.exports=r.default},{}],151:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){function t(e){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,t),function(e,t,r){for(var n=!0;n;){var a=e,i=t,o=r;n=!1,null===a&&(a=Function.prototype);var s=Object.getOwnPropertyDescriptor(a,i);if(void 0!==s){if("value"in s)return s.value;var u=s.get;if(void 0===u)return;return u.call(o)}var l=Object.getPrototypeOf(a);if(null===l)return;e=l,t=i,r=o,n=!0,s=l=void 0}}(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),this.url=e||null,this.checkForExistenceOnly=!0}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(function(e){return e&&e.__esModule?e:{default:e}}(e("./FragmentRequest.js")).default);r.default=n,t.exports=r.default},{"./FragmentRequest.js":150}],152:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.offset=NaN,this.type=null,this.size=NaN,this.isComplete=!0},t.exports=r.default},{}],153:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.DVRWindowSize=NaN,this.loadedTime=null,this.availableFrom=null,this.minBufferTime=NaN,this.duration=NaN,this.isDynamic=!1,this.maxFragmentDuration=null},t.exports=r.default},{}],154:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.id=null,this.index=null,this.type=null,this.streamInfo=null,this.representationCount=0,this.lang=null,this.viewpoint=null,this.accessibility=null,this.audioChannelConfiguration=null,this.roles=null,this.codec=null,this.mimeType=null,this.contentProtection=null,this.isText=!1,this.KID=null,this.bitrateList=null},t.exports=r.default},{}],155:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.TcpList=[],this.HttpList=[],this.RepSwitchList=[],this.BufferLevel=[],this.BufferState=[],this.PlayList=[],this.DroppedFrames=[],this.SchedulingInfo=[],this.DVRInfo=[],this.ManifestUpdate=[],this.RequestsQueue=null,this.DVBErrors=[],this.BolaState=[]},t.exports=r.default},{}],156:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.id=null,this.index=null,this.start=NaN,this.duration=NaN,this.manifestInfo=null,this.isLast=!0,this.isFirst=!0},t.exports=r.default},{}],157:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){function t(e,r){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,t),function(e,t,r){for(var n=!0;n;){var a=e,i=t,o=r;n=!1,null===a&&(a=Function.prototype);var s=Object.getOwnPropertyDescriptor(a,i);if(void 0!==s){if("value"in s)return s.value;var u=s.get;if(void 0===u)return;return u.call(o)}var l=Object.getPrototypeOf(a);if(null===l)return;e=l,t=i,r=o,n=!0,s=l=void 0}}(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),this.url=e||null,this.type=r||null,this.mediaType="stream",this.responseType="text"}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(function(e){return e&&e.__esModule?e:{default:e}}(e("./FragmentRequest.js")).default);r.default=n,t.exports=r.default},{"./FragmentRequest.js":150}],158:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.video=null,this.captionData=null,this.label=null,this.lang=null,this.defaultTrack=!1,this.kind=null,this.isFragmented=!1,this.isEmbedded=!1},t.exports=r.default},{}],159:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.id=null,this.quality=null,this.DVRWindow=null,this.fragmentDuration=null,this.mediaInfo=null,this.MSETimeOffset=null},t.exports=r.default},{}],160:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.t=null,this.xywh=null,this.track=null,this.id=null,this.s=null},t.exports=r.default},{}],161:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this._s=void 0},t.exports=r.default},{}],162:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.t=null,this.level=null},t.exports=r.default},{}],163:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){return e&&e.__esModule?e:{default:e}}(e("../../controllers/BufferController.js"));r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.target=null,this.state=n.default.BUFFER_EMPTY},t.exports=r.default},{"../../controllers/BufferController.js":52}],164:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.time=null,this.range=null,this.manifestInfo=null},t.exports=r.default},{}],165:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.time=null,this.droppedFrames=null},t.exports=r.default},{}],166:[function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(r,"__esModule",{value:!0});var a=function e(){n(this,e),this.tcpid=null,this.type=null,this.url=null,this.actualurl=null,this.range=null,this.trequest=null,this.tresponse=null,this.responsecode=null,this.interval=null,this.trace=[],this._stream=null,this._tfinish=null,this._mediaduration=null,this._responseHeaders=null,this._serviceLocation=null};a.Trace=function e(){n(this,e),this.s=null,this.d=null,this.b=[]},a.MPD_TYPE="MPD",a.XLINK_EXPANSION_TYPE="XLinkExpansion",a.INIT_SEGMENT_TYPE="InitializationSegment",a.INDEX_SEGMENT_TYPE="IndexSegment",a.MEDIA_SEGMENT_TYPE="MediaSegment",a.BITSTREAM_SWITCHING_SEGMENT_TYPE="BitstreamSwitchingSegment",a.OTHER_TYPE="other",r.default=a,t.exports=r.default},{}],167:[function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(r,"__esModule",{value:!0});var a=function e(){n(this,e),this.mediaType=null,this.type=null,this.requestTime=null,this.fetchTime=null,this.availabilityStartTime=null,this.presentationStartTime=0,this.clientTimeOffset=0,this.currentTime=null,this.buffered=null,this.latency=0,this.streamInfo=[],this.trackInfo=[]};a.StreamInfo=function e(){n(this,e),this.id=null,this.index=null,this.start=null,this.duration=null},a.TrackInfo=function e(){n(this,e),this.id=null,this.index=null,this.mediaType=null,this.streamIndex=null,this.presentationTimeOffset=null,this.startNumber=null,this.fragmentInfoType=null},r.default=a,t.exports=r.default},{}],168:[function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(r,"__esModule",{value:!0});var a=function e(){n(this,e),this.start=null,this.mstart=null,this.starttype=null,this.trace=[]};a.Trace=function e(){n(this,e),this.representationid=null,this.subreplevel=null,this.start=null,this.mstart=null,this.duration=null,this.playbackspeed=null,this.stopreason=null},a.INITIAL_PLAYOUT_START_REASON="initial_playout",a.SEEK_START_REASON="seek",a.RESUME_FROM_PAUSE_START_REASON="resume",a.METRICS_COLLECTION_START_REASON="metrics_collection_start",a.Trace.REPRESENTATION_SWITCH_STOP_REASON="representation_switch",a.Trace.REBUFFERING_REASON="rebuffering",a.Trace.USER_REQUEST_STOP_REASON="user_request",a.Trace.END_OF_PERIOD_STOP_REASON="end_of_period",a.Trace.END_OF_CONTENT_STOP_REASON="end_of_content",a.Trace.METRICS_COLLECTION_STOP_REASON="metrics_collection_end",a.Trace.FAILURE_STOP_REASON="failure",r.default=a,t.exports=r.default},{}],169:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.t=null,this.mt=null,this.to=null,this.lto=null},t.exports=r.default},{}],170:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.loadingRequests=[],this.executedRequests=[]},t.exports=r.default},{}],171:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.mediaType=null,this.t=null,this.type=null,this.startTime=null,this.availabilityStartTime=null,this.duration=null,this.quality=null,this.range=null,this.state=null},t.exports=r.default},{}],172:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.tcpid=null,this.dest=null,this.topen=null,this.tclose=null,this.tconnect=null},t.exports=r.default},{}]},{},[5]);