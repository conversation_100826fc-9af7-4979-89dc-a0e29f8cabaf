!function(t){"function"==typeof define&&define.amd?define([],t):"undefined"!=typeof module&&null!==module&&module.exports?module.exports=t:t()}(function(){var t=Object.assign||window.jQuery&&jQuery.extend,e=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t,e){return window.setTimeout(function(){t()},25)};!function(){if("function"==typeof window.CustomEvent)return!1;function t(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),n}t.prototype=window.Event.prototype,window.CustomEvent=t}();var n={textarea:!0,input:!0,select:!0,button:!0},i="mousemove",o="mouseup dragstart",a="mouseup",u="touchmove",c="touchend",r="touchend",d=/\s+/,f={bubbles:!0,cancelable:!0},m="function"==typeof Symbol?Symbol("events"):{};function s(t){return t[m]||(t[m]={})}function v(t,e,n,i,o){e=e.split(d);var a,u=s(t),c=e.length;function r(t){n(t,i)}for(;c--;)(u[a=e[c]]||(u[a]=[])).push([n,r]),t.addEventListener(a,r)}function l(t,e,n,i){e=e.split(d);var o,a,u,c=s(t),r=e.length;if(c)for(;r--;)if(a=c[o=e[r]])for(u=a.length;u--;)a[u][0]===n&&(t.removeEventListener(o,a[u][1]),a.splice(u,1))}function p(e,n,i){var o=function(t){return new CustomEvent(t,f)}(n);i&&t(o,i),e.dispatchEvent(o)}function g(t){var n=t,i=!1,o=!1;function a(t){i?(n(),e(a),o=!0,i=!1):o=!1}this.kick=function(t){i=!0,o||a()},this.end=function(t){var e=n;t&&(o?(n=i?function(){e(),t()}:t,i=!0):t())}}function h(){}function X(t){t.preventDefault()}function Y(t,e){var n,i;if(t.identifiedTouch)return t.identifiedTouch(e);for(n=-1,i=t.length;++n<i;)if(t[n].identifier===e)return t[n]}function y(t,e){var n=Y(t.changedTouches,e.identifier);if(n&&(n.pageX!==e.pageX||n.pageY!==e.pageY))return n}function w(t,e){S(t,e,t,T)}function b(t,e){T()}function T(){l(document,i,w),l(document,o,b)}function E(t){l(document,u,t.touchmove),l(document,c,t.touchend)}function S(t,e,n,i){var o=n.pageX-e.pageX,a=n.pageY-e.pageY;o*o+a*a<64||function(t,e,n,i,o,a){var u=t.targetTouches,c=t.timeStamp-e.timeStamp,r={altKey:t.altKey,ctrlKey:t.ctrlKey,shiftKey:t.shiftKey,startX:e.pageX,startY:e.pageY,distX:i,distY:o,deltaX:i,deltaY:o,pageX:n.pageX,pageY:n.pageY,velocityX:i/c,velocityY:o/c,identifier:e.identifier,targetTouches:u,finger:u?u.length:1,enableMove:function(){this.moveEnabled=!0,this.enableMove=h,t.preventDefault()}};p(e.target,"movestart",r),a(e)}(t,e,n,o,a,i)}function k(t,e){var n=e.timer;e.touch=t,e.timeStamp=t.timeStamp,n.kick()}function K(t,e){var n=e.target,o=e.event,u=e.timer;l(document,i,k),l(document,a,K),j(n,o,u,function(){setTimeout(function(){l(n,"click",X)},0)})}function j(t,e,n,i){n.end(function(){return p(t,"moveend",e),i&&i()})}if(v(document,"mousedown",function(t){(function(t){return 1===t.which&&!t.ctrlKey&&!t.altKey})(t)&&(function(t){return!!n[t.target.tagName.toLowerCase()]}(t)||(v(document,i,w,t),v(document,o,b,t)))}),v(document,"touchstart",function(t){if(!n[t.target.tagName.toLowerCase()]){var e=t.changedTouches[0],i={target:e.target,pageX:e.pageX,pageY:e.pageY,identifier:e.identifier,touchmove:function(t,e){!function(t,e){var n=y(t,e);n&&S(t,e,n,E)}(t,e)},touchend:function(t,e){!function(t,e){Y(t.changedTouches,e.identifier)&&E(e)}(t,e)}};v(document,u,i.touchmove,i),v(document,c,i.touchend,i)}}),v(document,"movestart",function(t){if(!t.defaultPrevented&&t.moveEnabled){var e={startX:t.startX,startY:t.startY,pageX:t.pageX,pageY:t.pageY,distX:t.distX,distY:t.distY,deltaX:t.deltaX,deltaY:t.deltaY,velocityX:t.velocityX,velocityY:t.velocityY,identifier:t.identifier,targetTouches:t.targetTouches,finger:t.finger},n={target:t.target,event:e,timer:new g(function(t){(function(t,e,n){var i=n-t.timeStamp;t.distX=e.pageX-t.startX,t.distY=e.pageY-t.startY,t.deltaX=e.pageX-t.pageX,t.deltaY=e.pageY-t.pageY,t.velocityX=.3*t.velocityX+.7*t.deltaX/i,t.velocityY=.3*t.velocityY+.7*t.deltaY/i,t.pageX=e.pageX,t.pageY=e.pageY})(e,n.touch,n.timeStamp),p(n.target,"move",e)}),touch:void 0,timeStamp:t.timeStamp};void 0===t.identifier?(v(t.target,"click",X),v(document,i,k,n),v(document,a,K,n)):(n.activeTouchmove=function(t,e){!function(t,e){var n=e.event,i=e.timer,o=y(t,n);o&&(t.preventDefault(),n.targetTouches=t.targetTouches,e.touch=o,e.timeStamp=t.timeStamp,i.kick())}(t,e)},n.activeTouchend=function(t,e){!function(t,e){var n=e.target,i=e.event,o=e.timer;Y(t.changedTouches,i.identifier)&&(function(t){l(document,u,t.activeTouchmove),l(document,r,t.activeTouchend)}(e),j(n,i,o))}(t,e)},v(document,u,n.activeTouchmove,n),v(document,r,n.activeTouchend,n))}}),window.jQuery){var C="startX startY pageX pageY distX distY deltaX deltaY velocityX velocityY".split(" ");jQuery.event.special.movestart={setup:function(){return v(this,"movestart",Q),!1},teardown:function(){return l(this,"movestart",Q),!1},add:F},jQuery.event.special.move={setup:function(){return v(this,"movestart",q),!1},teardown:function(){return l(this,"movestart",q),!1},add:F},jQuery.event.special.moveend={setup:function(){return v(this,"movestart",A),!1},teardown:function(){return l(this,"movestart",A),!1},add:F}}function Q(t){t.enableMove()}function q(t){t.enableMove()}function A(t){t.enableMove()}function F(t){var e=t.handler;t.handler=function(t){for(var n,i=C.length;i--;)t[n=C[i]]=t.originalEvent[n];e.apply(this,arguments)}}});