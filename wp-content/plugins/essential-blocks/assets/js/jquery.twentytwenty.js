(()=>{var t;(t=jQuery).fn.twentytwenty=function(e){return e=t.extend({default_offset_pct:.5,orientation:"horizontal",before_label:"Before",after_label:"After",no_overlay:!1,move_slider_on_hover:!1,move_with_handle_only:!0,click_to_move:!1},e),this.each(function(){var n=e.default_offset_pct,a=t(this),i=e.orientation,o="vertical"===i?"down":"left",s="vertical"===i?"up":"right";if(a.wrap("<div class='twentytwenty-wrapper twentytwenty-"+i+"'></div>"),!e.no_overlay){a.append("<div class='twentytwenty-overlay'></div>");var r=a.find(".twentytwenty-overlay");r.append("<div class='twentytwenty-before-label' data-content='"+e.before_label+"'></div>"),r.append("<div class='twentytwenty-after-label' data-content='"+e.after_label+"'></div>")}var c=a.find("img:first"),d=a.find("img:last");a.append("<div class='twentytwenty-handle'></div>");var l=a.find(".twentytwenty-handle");l.append("<span class='twentytwenty-"+o+"-arrow'></span>"),l.append("<span class='twentytwenty-"+s+"-arrow'></span>"),a.addClass("twentytwenty-container"),c.addClass("twentytwenty-before"),d.addClass("twentytwenty-after");var w=function(t){var e,n,o,s=(e=t,{w:(n=c.width())+"px",h:(o=c.height())+"px",cw:e*n+"px",ch:e*o+"px"});l.css("vertical"===i?"top":"left","vertical"===i?s.ch:s.cw),function(t){"vertical"===i?(c.css("clip","rect(0,"+t.w+","+t.ch+",0)"),d.css("clip","rect("+t.ch+","+t.w+","+t.h+",0)")):(c.css("clip","rect(0,"+t.cw+","+t.h+",0)"),d.css("clip","rect(0,"+t.w+","+t.h+","+t.cw+")")),a.css("height",t.h)}(s)},v=function(t,e){var n;return n="vertical"===i?(e-p)/h:(t-f)/y,Math.max(0,Math.min(1,n))};t(window).on("resize.twentytwenty",function(t){w(n)});var f=0,p=0,y=0,h=0,u=function(t){((t.distX>t.distY&&t.distX<-t.distY||t.distX<t.distY&&t.distX>-t.distY)&&"vertical"!==i||(t.distX<t.distY&&t.distX<-t.distY||t.distX>t.distY&&t.distX>-t.distY)&&"vertical"===i)&&t.preventDefault(),a.addClass("active"),f=a.offset().left,p=a.offset().top,y=c.width(),h=c.height()},_=function(t){a.hasClass("active")&&(n=v(t.pageX,t.pageY),w(n))},m=function(){a.removeClass("active")},g=e.move_with_handle_only?l:a;g.on("movestart",u),g.on("move",_),g.on("moveend",m),e.move_slider_on_hover&&(a.on("mouseenter",u),a.on("mousemove",_),a.on("mouseleave",m)),l.on("touchmove",function(t){t.preventDefault()}),a.find("img").on("mousedown",function(t){t.preventDefault()}),e.click_to_move&&a.on("click",function(t){f=a.offset().left,p=a.offset().top,y=c.width(),h=c.height(),n=v(t.pageX,t.pageY),w(n)}),t(window).trigger("resize.twentytwenty")})}})();