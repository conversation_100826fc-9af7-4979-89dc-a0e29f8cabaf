/*! For license information please see ScrollTrigger.min.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,function(e){"use strict";function t(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function r(){return v||"undefined"!=typeof window&&(v=window.gsap)&&v.registerPlugin&&v}function n(e,t){return~R.indexOf(e)&&R[R.indexOf(e)+1][t]}function o(e){return!!~T.indexOf(e)}function i(e,t,r,n,o){return e.addEventListener(t,r,{passive:!1!==n,capture:!!o})}function a(e,t,r,n){return e.removeEventListener(t,r,!!n)}function s(){return C&&C.isPressed||A.cache++}function l(e,t){function r(n){if(n||0===n){M&&(y.history.scrollRestoration="manual");var o=C&&C.isPressed;n=r.v=Math.round(n)||(C&&C.iOS?1:0),e(n),r.cacheID=A.cache,o&&Y("ss",n)}else(t||A.cache!==r.cacheID||Y("ref"))&&(r.cacheID=A.cache,r.v=e());return r.v+r.offset}return r.offset=0,e&&r}function c(e,t){return(t&&t._ctx&&t._ctx.selector||v.utils.toArray)(e)[0]||("string"==typeof e&&!1!==v.config().nullTargetWarn?console.warn("Element not found:",e):null)}function u(e,t){var r=t.s,a=t.sc;o(e)&&(e=x.scrollingElement||b);var c=A.indexOf(e),u=a===B.sc?1:2;~c||(c=A.push(e)-1),A[c+u]||i(e,"scroll",s);var f=A[c+u],d=f||(A[c+u]=l(n(e,r),!0)||(o(e)?a:l(function(t){return arguments.length?e[r]=t:e[r]})));return d.target=e,f||(d.smooth="smooth"===v.getProperty(e,"scrollBehavior")),d}function f(e,t,r){function n(e,t){var n=D();t||l<n-a?(i=o,o=e,s=a,a=n):r?o+=e:o=i+(e-i)/(n-s)*(a-s)}var o=e,i=e,a=D(),s=a,l=t||50,c=Math.max(500,3*l);return{update:n,reset:function(){i=o=r?0:o,s=a=0},getVelocity:function(e){var t=s,l=i,u=D();return!e&&0!==e||e===o||n(e),a===s||c<u-s?0:(o+(r?l:-l))/((r?u:a)-t)*1e3}}}function d(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e}function p(e){var t=Math.max.apply(Math,e),r=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(r)?t:r}function h(){(S=v.core.globals().ScrollTrigger)&&S.core&&function(){var e=S.core,t=e.bridge||{},r=e._scrollers,n=e._proxies;r.push.apply(r,A),n.push.apply(n,R),A=r,R=n,Y=function(e,r){return t[e](r)}}()}function g(e){return v=e||r(),!m&&v&&"undefined"!=typeof document&&document.body&&(y=window,b=(x=document).documentElement,w=x.body,T=[y,x,b,w],v.utils.clamp,P=v.core.context||function(){},k="onpointerenter"in w?"pointer":"mouse",_=N.isTouch=y.matchMedia&&y.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in y||0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints?2:0,E=N.eventTypes=("ontouchstart"in b?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in b?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return M=0},500),h(),m=1),m}var v,m,y,x,b,w,_,k,S,T,C,E,P,M=1,O=[],A=[],R=[],D=Date.now,Y=function(e,t){return t},I="scrollLeft",X="scrollTop",z={s:I,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:l(function(e){return arguments.length?y.scrollTo(e,B.sc()):y.pageXOffset||x[I]||b[I]||w[I]||0})},B={s:X,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:z,sc:l(function(e){return arguments.length?y.scrollTo(z.sc(),e):y.pageYOffset||x[X]||b[X]||w[X]||0})};z.op=B,A.cache=0;var N=(F.prototype.init=function(e){m||g(v)||console.warn("Please gsap.registerPlugin(Observer)"),S||h();var t=e.tolerance,r=e.dragMinimum,n=e.type,l=e.target,T=e.lineHeight,M=e.debounce,A=e.preventDefault,R=e.onStop,Y=e.onStopDelay,I=e.ignore,X=e.wheelSpeed,N=e.event,F=e.onDragStart,H=e.onDragEnd,L=e.onDrag,W=e.onPress,q=e.onRelease,U=e.onRight,V=e.onLeft,G=e.onUp,j=e.onDown,K=e.onChangeX,Z=e.onChangeY,$=e.onChange,J=e.onToggleX,Q=e.onToggleY,ee=e.onHover,te=e.onHoverEnd,re=e.onMove,ne=e.ignoreCheck,oe=e.isNormalizer,ie=e.onGestureStart,ae=e.onGestureEnd,se=e.onWheel,le=e.onEnable,ce=e.onDisable,ue=e.onClick,fe=e.scrollSpeed,de=e.capture,pe=e.allowClicks,he=e.lockAxis,ge=e.onLockAxis;function ve(){return Ze=D()}function me(e,t){return(ze.event=e)&&I&&~I.indexOf(e.target)||t&&Ue&&"touch"!==e.pointerType||ne&&ne(e,t)}function ye(){var e=ze.deltaX=p(je),r=ze.deltaY=p(Ke),n=Math.abs(e)>=t,o=Math.abs(r)>=t;$&&(n||o)&&$(ze,e,r,je,Ke),n&&(U&&0<ze.deltaX&&U(ze),V&&ze.deltaX<0&&V(ze),K&&K(ze),J&&ze.deltaX<0!=Be<0&&J(ze),Be=ze.deltaX,je[0]=je[1]=je[2]=0),o&&(j&&0<ze.deltaY&&j(ze),G&&ze.deltaY<0&&G(ze),Z&&Z(ze),Q&&ze.deltaY<0!=Ne<0&&Q(ze),Ne=ze.deltaY,Ke[0]=Ke[1]=Ke[2]=0),(De||Re)&&(re&&re(ze),Re&&(L(ze),Re=!1),De=!1),Ie&&!(Ie=!1)&&ge&&ge(ze),Ye&&(se(ze),Ye=!1),Oe=0}function xe(e,t,r){je[r]+=e,Ke[r]+=t,ze._vx.update(e),ze._vy.update(t),M?Oe=Oe||requestAnimationFrame(ye):ye()}function be(e,t){he&&!Xe&&(ze.axis=Xe=Math.abs(e)>Math.abs(t)?"x":"y",Ie=!0),"y"!==Xe&&(je[2]+=e,ze._vx.update(e,!0)),"x"!==Xe&&(Ke[2]+=t,ze._vy.update(t,!0)),M?Oe=Oe||requestAnimationFrame(ye):ye()}function we(e){if(!me(e,1)){var t=(e=d(e,A)).clientX,n=e.clientY,o=t-ze.x,i=n-ze.y,a=ze.isDragging;ze.x=t,ze.y=n,(a||Math.abs(ze.startX-t)>=r||Math.abs(ze.startY-n)>=r)&&(L&&(Re=!0),a||(ze.isDragging=!0),be(o,i),a||F&&F(ze))}}function _e(e){return e.touches&&1<e.touches.length&&(ze.isGesturing=!0)&&ie(e,ze.isDragging)}function ke(){return(ze.isGesturing=!1)||ae(ze)}function Se(e){if(!me(e)){var t=He(),r=Le();xe((t-We)*fe,(r-qe)*fe,1),We=t,qe=r,R&&Ae.restart(!0)}}function Te(e){if(!me(e)){e=d(e,A),se&&(Ye=!0);var t=(1===e.deltaMode?T:2===e.deltaMode?y.innerHeight:1)*X;xe(e.deltaX*t,e.deltaY*t,0),R&&!oe&&Ae.restart(!0)}}function Ce(e){if(!me(e)){var t=e.clientX,r=e.clientY,n=t-ze.x,o=r-ze.y;ze.x=t,ze.y=r,De=!0,R&&Ae.restart(!0),(n||o)&&be(n,o)}}function Ee(e){ze.event=e,ee(ze)}function Pe(e){ze.event=e,te(ze)}function Me(e){return me(e)||d(e,A)&&ue(ze)}this.target=l=c(l)||b,this.vars=e,I=I&&v.utils.toArray(I),t=t||1e-9,r=r||0,X=X||1,fe=fe||1,n=n||"wheel,touch,pointer",M=!1!==M,T=T||parseFloat(y.getComputedStyle(w).lineHeight)||22;var Oe,Ae,Re,De,Ye,Ie,Xe,ze=this,Be=0,Ne=0,Fe=e.passive||!A,He=u(l,z),Le=u(l,B),We=He(),qe=Le(),Ue=~n.indexOf("touch")&&!~n.indexOf("pointer")&&"pointerdown"===E[0],Ve=o(l),Ge=l.ownerDocument||x,je=[0,0,0],Ke=[0,0,0],Ze=0,$e=ze.onPress=function(e){me(e,1)||e&&e.button||(ze.axis=Xe=null,Ae.pause(),ze.isPressed=!0,e=d(e),Be=Ne=0,ze.startX=ze.x=e.clientX,ze.startY=ze.y=e.clientY,ze._vx.reset(),ze._vy.reset(),i(oe?l:Ge,E[1],we,Fe,!0),ze.deltaX=ze.deltaY=0,W&&W(ze))},Je=ze.onRelease=function(e){if(!me(e,1)){a(oe?l:Ge,E[1],we,!0);var t=!isNaN(ze.y-ze.startY),r=ze.isDragging,n=r&&(3<Math.abs(ze.x-ze.startX)||3<Math.abs(ze.y-ze.startY)),o=d(e);!n&&t&&(ze._vx.reset(),ze._vy.reset(),A&&pe&&v.delayedCall(.08,function(){if(300<D()-Ze&&!e.defaultPrevented)if(e.target.click)e.target.click();else if(Ge.createEvent){var t=Ge.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,y,1,o.screenX,o.screenY,o.clientX,o.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(t)}})),ze.isDragging=ze.isGesturing=ze.isPressed=!1,R&&r&&!oe&&Ae.restart(!0),H&&r&&H(ze),q&&q(ze,n)}};Ae=ze._dc=v.delayedCall(Y||.25,function(){ze._vx.reset(),ze._vy.reset(),Ae.pause(),R&&R(ze)}).pause(),ze.deltaX=ze.deltaY=0,ze._vx=f(0,50,!0),ze._vy=f(0,50,!0),ze.scrollX=He,ze.scrollY=Le,ze.isDragging=ze.isGesturing=ze.isPressed=!1,P(this),ze.enable=function(e){return ze.isEnabled||(i(Ve?Ge:l,"scroll",s),0<=n.indexOf("scroll")&&i(Ve?Ge:l,"scroll",Se,Fe,de),0<=n.indexOf("wheel")&&i(l,"wheel",Te,Fe,de),(0<=n.indexOf("touch")&&_||0<=n.indexOf("pointer"))&&(i(l,E[0],$e,Fe,de),i(Ge,E[2],Je),i(Ge,E[3],Je),pe&&i(l,"click",ve,!0,!0),ue&&i(l,"click",Me),ie&&i(Ge,"gesturestart",_e),ae&&i(Ge,"gestureend",ke),ee&&i(l,k+"enter",Ee),te&&i(l,k+"leave",Pe),re&&i(l,k+"move",Ce)),ze.isEnabled=!0,e&&e.type&&$e(e),le&&le(ze)),ze},ze.disable=function(){ze.isEnabled&&(O.filter(function(e){return e!==ze&&o(e.target)}).length||a(Ve?Ge:l,"scroll",s),ze.isPressed&&(ze._vx.reset(),ze._vy.reset(),a(oe?l:Ge,E[1],we,!0)),a(Ve?Ge:l,"scroll",Se,de),a(l,"wheel",Te,de),a(l,E[0],$e,de),a(Ge,E[2],Je),a(Ge,E[3],Je),a(l,"click",ve,!0),a(l,"click",Me),a(Ge,"gesturestart",_e),a(Ge,"gestureend",ke),a(l,k+"enter",Ee),a(l,k+"leave",Pe),a(l,k+"move",Ce),ze.isEnabled=ze.isPressed=ze.isDragging=!1,ce&&ce(ze))},ze.kill=ze.revert=function(){ze.disable();var e=O.indexOf(ze);0<=e&&O.splice(e,1),C===ze&&(C=0)},O.push(ze),oe&&o(l)&&(C=ze),ze.enable(N)},function(e,r){r&&t(e.prototype,r)}(F,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),F);function F(e){this.init(e)}function H(e,t,r){var n=_t(e)&&("clamp("===e.substr(0,6)||-1<e.indexOf("max"));return(r["_"+t+"Clamp"]=n)?e.substr(6,e.length-7):e}function L(e,t){return!t||_t(e)&&"clamp("===e.substr(0,6)?e:"clamp("+e+")"}function W(){return Ve=1}function q(){return Ve=0}function U(e){return e}function V(e){return Math.round(1e5*e)/1e5||0}function G(){return"undefined"!=typeof window}function j(){return De||G()&&(De=window.gsap)&&De.registerPlugin&&De}function K(e){return!!~Ne.indexOf(e)}function Z(e){return("Height"===e?ct:Ie["inner"+e])||ze["client"+e]||Be["client"+e]}function $(e){return n(e,"getBoundingClientRect")||(K(e)?function(){return rr.width=Ie.innerWidth,rr.height=ct,rr}:function(){return Xt(e)})}function J(e,t){var r=t.s,o=t.d2,i=t.d,a=t.a;return Math.max(0,(r="scroll"+o)&&(a=n(e,r))?a()-$(e)()[i]:K(e)?(ze[r]||Be[r])-Z(o):e[r]-e["offset"+o])}function Q(e,t){for(var r=0;r<$e.length;r+=3)t&&!~t.indexOf($e[r+1])||e($e[r],$e[r+1],$e[r+2])}function ee(e){return"function"==typeof e}function te(e){return"number"==typeof e}function re(e){return"object"==typeof e}function ne(e,t,r){return e&&e.progress(t?0:1)&&r&&e.pause()}function oe(e,t){if(e.enabled){var r=e._ctx?e._ctx.add(function(){return t(e)}):t(e);r&&r.totalTime&&(e.callbackAnimation=r)}}function ie(e){return Ie.getComputedStyle(e)}function ae(e,t){for(var r in t)r in e||(e[r]=t[r]);return e}function se(e,t){var r=t.d2;return e["offset"+r]||e["client"+r]||0}function le(e){var t,r=[],n=e.labels,o=e.duration();for(t in n)r.push(n[t]/o);return r}function ce(e){var t=De.utils.snap(e),r=Array.isArray(e)&&e.slice(0).sort(function(e,t){return e-t});return r?function(e,n,o){var i;if(void 0===o&&(o=.001),!n)return t(e);if(0<n){for(e-=o,i=0;i<r.length;i++)if(r[i]>=e)return r[i];return r[i-1]}for(i=r.length,e+=o;i--;)if(r[i]<=e)return r[i];return r[0]}:function(r,n,o){void 0===o&&(o=.001);var i=t(r);return!n||Math.abs(i-r)<o||i-r<0==n<0?i:t(n<0?r-e:r+e)}}function ue(e,t,r,n){return r.split(",").forEach(function(r){return e(t,r,n)})}function fe(e,t,r,n,o){return e.addEventListener(t,r,{passive:!n,capture:!!o})}function de(e,t,r,n){return e.removeEventListener(t,r,!!n)}function pe(e,t,r){(r=r&&r.wheelHandler)&&(e(t,"wheel",r),e(t,"touchmove",r))}function he(e,t){if(_t(e)){var r=e.indexOf("="),n=~r?(e.charAt(r-1)+1)*parseFloat(e.substr(r+1)):0;~r&&(e.indexOf("%")>r&&(n*=t/100),e=e.substr(0,r-1)),e=n+(e in Nt?Nt[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e}function ge(e,t,r,o,i,a,s,l){var c=i.startColor,u=i.endColor,f=i.fontSize,d=i.indent,p=i.fontWeight,h=Xe.createElement("div"),g=K(r)||"fixed"===n(r,"pinType"),v=-1!==e.indexOf("scroller"),m=g?Be:r,y=-1!==e.indexOf("start"),x=y?c:u,b="border-color:"+x+";font-size:"+f+";color:"+x+";font-weight:"+p+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return b+="position:"+((v||l)&&g?"fixed;":"absolute;"),!v&&!l&&g||(b+=(o===B?St:Tt)+":"+(a+parseFloat(d))+"px;"),s&&(b+="box-sizing:border-box;text-align:left;width:"+s.offsetWidth+"px;"),h._isStart=y,h.setAttribute("class","gsap-marker-"+e+(t?" marker-"+t:"")),h.style.cssText=b,h.innerText=t||0===t?e+"-"+t:e,m.children[0]?m.insertBefore(h,m.children[0]):m.appendChild(h),h._offset=h["offset"+o.op.d2],Ft(h,0,o,y),h}function ve(){return 34<yt()-bt&&(pt=pt||requestAnimationFrame($t))}function me(){tt&&tt.isPressed&&!(tt.startX>Be.clientWidth)||(A.cache++,tt?pt=pt||requestAnimationFrame($t):$t(),bt||Ut("scrollStart"),bt=yt())}function ye(){ot=Ie.innerWidth,nt=Ie.innerHeight}function xe(){A.cache++,Ue||et||Xe.fullscreenElement||Xe.webkitFullscreenElement||rt&&ot===Ie.innerWidth&&!(Math.abs(Ie.innerHeight-nt)>.25*Ie.innerHeight)||Fe.restart(!0)}function be(){return de(or,"scrollEnd",be)||jt(!0)}function we(e){for(var t=0;t<Vt.length;t+=5)(!e||Vt[t+4]&&Vt[t+4].query===e)&&(Vt[t].style.cssText=Vt[t+1],Vt[t].getBBox&&Vt[t].setAttribute("transform",Vt[t+2]||""),Vt[t+3].uncache=1)}function _e(e,t){var r;for(je=0;je<Ht.length;je++)!(r=Ht[je])||t&&r._ctx!==t||(e?r.kill(1):r.revert(!0,!0));ut=!0,t&&we(t),t||Ut("revert")}function ke(e,t){A.cache++,!t&&ht||A.forEach(function(e){return ee(e)&&e.cacheID++&&(e.rec=0)}),_t(e)&&(Ie.history.scrollRestoration=st=e)}function Se(){Be.appendChild(lt),ct=!tt&&lt.offsetHeight||Ie.innerHeight,Be.removeChild(lt)}function Te(e){return He(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(t){return t.style.display=e?"none":"block"})}function Ce(e,t,r,n){if(!e._gsap.swappedIn){for(var o,i=Jt.length,a=t.style,s=e.style;i--;)a[o=Jt[i]]=r[o];a.position="absolute"===r.position?"absolute":"relative","inline"===r.display&&(a.display="inline-block"),s[Tt]=s[St]="auto",a.flexBasis=r.flexBasis||"auto",a.overflow="visible",a.boxSizing="border-box",a[Ct]=se(e,z)+It,a[Et]=se(e,B)+It,a[Rt]=s[Dt]=s.top=s.left="0",tr(n),s[Ct]=s.maxWidth=r[Ct],s[Et]=s.maxHeight=r[Et],s[Rt]=r[Rt],e.parentNode!==t&&(e.parentNode.insertBefore(t,e),t.appendChild(e)),e._gsap.swappedIn=!0}}function Ee(e){for(var t=Qt.length,r=e.style,n=[],o=0;o<t;o++)n.push(Qt[o],r[Qt[o]]);return n.t=e,n}function Pe(e,t,r,n,o,i,a,s,l,u,f,d,p,h){ee(e)&&(e=e(s)),_t(e)&&"max"===e.substr(0,3)&&(e=d+("="===e.charAt(4)?he("0"+e.substr(3),r):0));var g,v,m,y=p?p.time():0;if(p&&p.seek(0),isNaN(e)||(e=+e),te(e))p&&(e=De.utils.mapRange(p.scrollTrigger.start,p.scrollTrigger.end,0,d,e)),a&&Ft(a,r,n,!0);else{ee(t)&&(t=t(s));var x,b,w,_,k=(e||"0").split(" ");m=c(t,s)||Be,(x=Xt(m)||{})&&(x.left||x.top)||"none"!==ie(m).display||(_=m.style.display,m.style.display="block",x=Xt(m),_?m.style.display=_:m.style.removeProperty("display")),b=he(k[0],x[n.d]),w=he(k[1]||"0",r),e=x[n.p]-l[n.p]-u+b+o-w,a&&Ft(a,w,n,r-w<20||a._isStart&&20<w),r-=r-w}if(h&&(s[h]=e||-.001,e<0&&(e=0)),i){var S=e+r,T=i._isStart;g="scroll"+n.d2,Ft(i,S,n,T&&20<S||!T&&(f?Math.max(Be[g],ze[g]):i.parentNode[g])<=S+1),f&&(l=Xt(a),f&&(i.style[n.op.p]=l[n.op.p]-n.op.m-i._offset+It))}return p&&m&&(g=Xt(m),p.seek(d),v=Xt(m),p._caScrollDist=g[n.p]-v[n.p],e=e/p._caScrollDist*d),p&&p.seek(y),p?e:Math.round(e)}function Me(e,t,r,n){if(e.parentNode!==t){var o,i,a=e.style;if(t===Be){for(o in e._stOrig=a.cssText,i=ie(e))+o||nr.test(o)||!i[o]||"string"!=typeof a[o]||"0"===o||(a[o]=i[o]);a.top=r,a.left=n}else a.cssText=e._stOrig;De.core.getCache(e).uncache=1,t.appendChild(e)}}function Oe(e,t,r){var n=t,o=n;return function(t){var i=Math.round(e());return i!==n&&i!==o&&3<Math.abs(i-n)&&3<Math.abs(i-o)&&(t=i,r&&r()),o=n,n=t}}function Ae(e,t,r){var n={};n[t.p]="+="+r,De.set(e,n)}function Re(e,t){function r(t,i,a,s,l){var c=r.tween,u=i.onComplete;a=a||n();var f=Oe(n,a,function(){c.kill(),r.tween=0});return l=s&&l||0,s=s||t-a,c&&c.kill(),i[o]=t,i.inherit=!1,(i.modifiers={})[o]=function(){return f(a+s*c.ratio+l*c.ratio*c.ratio)},i.onUpdate=function(){A.cache++,r.tween&&$t()},i.onComplete=function(){r.tween=0,u&&u.call(c)},c=r.tween=De.to(e,i)}var n=u(e,t),o="_scroll"+t.p2;return(e[o]=n).wheelHandler=function(){return r.tween&&r.tween.kill()&&(r.tween=0)},fe(e,"wheel",n.wheelHandler),or.isTouch&&fe(e,"touchmove",n.wheelHandler),r}N.version="3.12.5",N.create=function(e){return new N(e)},N.register=g,N.getAll=function(){return O.slice()},N.getById=function(e){return O.filter(function(t){return t.vars.id===e})[0]},r()&&v.registerPlugin(N);var De,Ye,Ie,Xe,ze,Be,Ne,Fe,He,Le,We,qe,Ue,Ve,Ge,je,Ke,Ze,$e,Je,Qe,et,tt,rt,nt,ot,it,at,st,lt,ct,ut,ft,dt,pt,ht,gt,vt,mt=1,yt=Date.now,xt=yt(),bt=0,wt=0,_t=function(e){return"string"==typeof e},kt=Math.abs,St="right",Tt="bottom",Ct="width",Et="height",Pt="Right",Mt="Left",Ot="Top",At="Bottom",Rt="padding",Dt="margin",Yt="Width",It="px",Xt=function(e,t){var r=t&&"matrix(1, 0, 0, 1, 0, 0)"!==ie(e)[Ge]&&De.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=e.getBoundingClientRect();return r&&r.progress(0).kill(),n},zt={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},Bt={toggleActions:"play",anticipatePin:0},Nt={top:0,left:0,center:.5,bottom:1,right:1},Ft=function(e,t,r,n){var o={display:"block"},i=r[n?"os2":"p2"],a=r[n?"p2":"os2"];e._isFlipped=n,o[r.a+"Percent"]=n?-100:0,o[r.a]=n?"1px":0,o["border"+i+Yt]=1,o["border"+a+Yt]=0,o[r.p]=t+"px",De.set(e,o)},Ht=[],Lt={},Wt={},qt=[],Ut=function(e){return Wt[e]&&Wt[e].map(function(e){return e()})||qt},Vt=[],Gt=0,jt=function(e,t){if(!bt||e||ut){Se(),ht=or.isRefreshing=!0,A.forEach(function(e){return ee(e)&&++e.cacheID&&(e.rec=e())});var r=Ut("refreshInit");Je&&or.sort(),t||_e(),A.forEach(function(e){ee(e)&&(e.smooth&&(e.target.style.scrollBehavior="auto"),e(0))}),Ht.slice(0).forEach(function(e){return e.refresh()}),ut=!1,Ht.forEach(function(e){if(e._subPinOffset&&e.pin){var t=e.vars.horizontal?"offsetWidth":"offsetHeight",r=e.pin[t];e.revert(!0,1),e.adjustPinSpacing(e.pin[t]-r),e.refresh()}}),ft=1,Te(!0),Ht.forEach(function(e){var t=J(e.scroller,e._dir),r="max"===e.vars.end||e._endClamp&&e.end>t,n=e._startClamp&&e.start>=t;(r||n)&&e.setPositions(n?t-1:e.start,r?Math.max(n?t:e.start+1,t):e.end,!0)}),Te(!1),ft=0,r.forEach(function(e){return e&&e.render&&e.render(-1)}),A.forEach(function(e){ee(e)&&(e.smooth&&requestAnimationFrame(function(){return e.target.style.scrollBehavior="smooth"}),e.rec&&e(e.rec))}),ke(st,1),Fe.pause(),Gt++,$t(ht=2),Ht.forEach(function(e){return ee(e.vars.onRefresh)&&e.vars.onRefresh(e)}),ht=or.isRefreshing=!1,Ut("refresh")}else fe(or,"scrollEnd",be)},Kt=0,Zt=1,$t=function(e){if(2===e||!ht&&!ut){or.isUpdating=!0,vt&&vt.update(0);var t=Ht.length,r=yt(),n=50<=r-xt,o=t&&Ht[0].scroll();if(Zt=o<Kt?-1:1,ht||(Kt=o),n&&(bt&&!Ve&&200<r-bt&&(bt=0,Ut("scrollEnd")),We=xt,xt=r),Zt<0){for(je=t;0<je--;)Ht[je]&&Ht[je].update(0,n);Zt=1}else for(je=0;je<t;je++)Ht[je]&&Ht[je].update(0,n);or.isUpdating=!1}pt=0},Jt=["left","top",Tt,St,Dt+At,Dt+Pt,Dt+Ot,Dt+Mt,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],Qt=Jt.concat([Ct,Et,"boxSizing","max"+Yt,"maxHeight","position",Dt,Rt,Rt+Ot,Rt+Pt,Rt+At,Rt+Mt]),er=/([A-Z])/g,tr=function(e){if(e){var t,r,n=e.t.style,o=e.length,i=0;for((e.t._gsap||De.core.getCache(e.t)).uncache=1;i<o;i+=2)r=e[i+1],t=e[i],r?n[t]=r:n[t]&&n.removeProperty(t.replace(er,"-$1").toLowerCase())}},rr={left:0,top:0},nr=/(webkit|moz|length|cssText|inset)/i,or=(ir.prototype.init=function(e,t){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),wt){var r,o,i,a,s,l,f,d,p,h,g,v,m,y,x,b,w,_,k,S,T,C,E,P,M,O,D,Y,I,X,N,F,W,q,G,j,Q,ue,pe,ve=(e=ae(_t(e)||te(e)||e.nodeType?{trigger:e}:e,Bt)).onUpdate,ye=e.toggleClass,we=e.id,_e=e.onToggle,ke=e.onRefresh,Se=e.scrub,Te=e.trigger,Oe=e.pin,Ye=e.pinSpacing,Ne=e.invalidateOnRefresh,Fe=e.anticipatePin,qe=e.onScrubComplete,Ge=e.onSnapComplete,Ke=e.once,Ze=e.snap,$e=e.pinReparent,et=e.pinSpacer,tt=e.containerAnimation,rt=e.fastScrollEnd,nt=e.preventOverlaps,ot=e.horizontal||e.containerAnimation&&!1!==e.horizontal?z:B,it=!Se&&0!==Se,at=c(e.scroller||Ie),st=De.core.getCache(at),lt=K(at),ct="fixed"===("pinType"in e?e.pinType:n(at,"pinType")||lt&&"fixed"),ut=[e.onEnter,e.onLeave,e.onEnterBack,e.onLeaveBack],pt=it&&e.toggleActions.split(" "),xt="markers"in e?e.markers:Bt.markers,St=lt?0:parseFloat(ie(at)["border"+ot.p2+Yt])||0,Tt=this,Nt=e.onRefreshInit&&function(){return e.onRefreshInit(Tt)},Ft=function(e,t,r){var o=r.d,i=r.d2,a=r.a;return(a=n(e,"getBoundingClientRect"))?function(){return a()[o]}:function(){return(t?Z(i):e["client"+i])||0}}(at,lt,ot),Wt=function(e,t){return!t||~R.indexOf(e)?$(e):function(){return rr}}(at,lt),qt=0,Ut=0,Vt=0,Kt=u(at,ot);if(Tt._startClamp=Tt._endClamp=!1,Tt._dir=ot,Fe*=45,Tt.scroller=at,Tt.scroll=tt?tt.time.bind(tt):Kt,a=Kt(),Tt.vars=e,t=t||e.animation,"refreshPriority"in e&&(Je=1,-9999===e.refreshPriority&&(vt=Tt)),st.tweenScroll=st.tweenScroll||{top:Re(at,B),left:Re(at,z)},Tt.tweenTo=r=st.tweenScroll[ot.p],Tt.scrubDuration=function(e){(W=te(e)&&e)?F?F.duration(e):F=De.to(t,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:W,paused:!0,onComplete:function(){return qe&&qe(Tt)}}):(F&&F.progress(1).kill(),F=0)},t&&(t.vars.lazy=!1,t._initted&&!Tt.isReverted||!1!==t.vars.immediateRender&&!1!==e.immediateRender&&t.duration()&&t.render(0,!0,!0),Tt.animation=t.pause(),(t.scrollTrigger=Tt).scrubDuration(Se),X=0,we=we||t.vars.id),Ze&&(re(Ze)&&!Ze.push||(Ze={snapTo:Ze}),"scrollBehavior"in Be.style&&De.set(lt?[Be,ze]:at,{scrollBehavior:"auto"}),A.forEach(function(e){return ee(e)&&e.target===(lt?Xe.scrollingElement||ze:at)&&(e.smooth=!1)}),i=ee(Ze.snapTo)?Ze.snapTo:"labels"===Ze.snapTo?function(e){return function(t){return De.utils.snap(le(e),t)}}(t):"labelsDirectional"===Ze.snapTo?function(e){return function(t,r){return ce(le(e))(t,r.direction)}}(t):!1!==Ze.directional?function(e,t){return ce(Ze.snapTo)(e,yt()-Ut<500?0:t.direction)}:De.utils.snap(Ze.snapTo),q=re(q=Ze.duration||{min:.1,max:2})?Le(q.min,q.max):Le(q,q),G=De.delayedCall(Ze.delay||W/2||.1,function(){var e=Kt(),n=yt()-Ut<500,o=r.tween;if(!(n||Math.abs(Tt.getVelocity())<10)||o||Ve||qt===e)Tt.isActive&&qt!==e&&G.restart(!0);else{var a,s,c=(e-l)/y,u=t&&!it?t.totalProgress():c,d=n?0:(u-N)/(yt()-We)*1e3||0,p=De.utils.clamp(-c,1-c,kt(d/2)*d/.185),h=c+(!1===Ze.inertia?0:p),g=Ze.onStart,v=Ze.onInterrupt,m=Ze.onComplete;if(te(a=i(h,Tt))||(a=h),s=Math.round(l+a*y),e<=f&&l<=e&&s!==e){if(o&&!o._initted&&o.data<=kt(s-e))return;!1===Ze.inertia&&(p=a-c),r(s,{duration:q(kt(.185*Math.max(kt(h-u),kt(a-u))/d/.05||0)),ease:Ze.ease||"power3",data:kt(s-e),onInterrupt:function(){return G.restart(!0)&&v&&v(Tt)},onComplete:function(){Tt.update(),qt=Kt(),t&&(F?F.resetTo("totalProgress",a,t._tTime/t._tDur):t.progress(a)),X=N=t&&!it?t.totalProgress():Tt.progress,Ge&&Ge(Tt),m&&m(Tt)}},e,p*y,s-e-p*y),g&&g(Tt,r.tween)}}}).pause()),we&&(Lt[we]=Tt),pe=(pe=(Te=Tt.trigger=c(Te||!0!==Oe&&Oe))&&Te._gsap&&Te._gsap.stRevert)&&pe(Tt),Oe=!0===Oe?Te:c(Oe),_t(ye)&&(ye={targets:Te,className:ye}),Oe&&(!1===Ye||Ye===Dt||(Ye=!(!Ye&&Oe.parentNode&&Oe.parentNode.style&&"flex"===ie(Oe.parentNode).display)&&Rt),Tt.pin=Oe,(o=De.core.getCache(Oe)).spacer?x=o.pinState:(et&&((et=c(et))&&!et.nodeType&&(et=et.current||et.nativeElement),o.spacerIsNative=!!et,et&&(o.spacerState=Ee(et))),o.spacer=_=et||Xe.createElement("div"),_.classList.add("pin-spacer"),we&&_.classList.add("pin-spacer-"+we),o.pinState=x=Ee(Oe)),!1!==e.force3D&&De.set(Oe,{force3D:!0}),Tt.spacer=_=o.spacer,I=ie(Oe),P=I[Ye+ot.os2],S=De.getProperty(Oe),T=De.quickSetter(Oe,ot.a,It),Ce(Oe,_,I),w=Ee(Oe)),xt){v=re(xt)?ae(xt,zt):zt,h=ge("scroller-start",we,at,ot,v,0),g=ge("scroller-end",we,at,ot,v,0,h),k=h["offset"+ot.op.d2];var $t=c(n(at,"content")||at);d=this.markerStart=ge("start",we,$t,ot,v,k,0,tt),p=this.markerEnd=ge("end",we,$t,ot,v,k,0,tt),tt&&(ue=De.quickSetter([d,p],ot.a,It)),ct||R.length&&!0===n(at,"fixedMarkers")||(function(e){var t=ie(e).position;e.style.position="absolute"===t||"fixed"===t?t:"relative"}(lt?Be:at),De.set([h,g],{force3D:!0}),O=De.quickSetter(h,ot.a,It),Y=De.quickSetter(g,ot.a,It))}if(tt){var Jt=tt.vars.onUpdate,Qt=tt.vars.onUpdateParams;tt.eventCallback("onUpdate",function(){Tt.update(0,0,1),Jt&&Jt.apply(tt,Qt||[])})}if(Tt.previous=function(){return Ht[Ht.indexOf(Tt)-1]},Tt.next=function(){return Ht[Ht.indexOf(Tt)+1]},Tt.revert=function(e,r){if(!r)return Tt.kill(!0);var n=!1!==e||!Tt.enabled,o=Ue;n!==Tt.isReverted&&(n&&(j=Math.max(Kt(),Tt.scroll.rec||0),Vt=Tt.progress,Q=t&&t.progress()),d&&[d,p,h,g].forEach(function(e){return e.style.display=n?"none":"block"}),n&&(Ue=Tt).update(n),!Oe||$e&&Tt.isActive||(n?function(e,t,r){tr(r);var n=e._gsap;if(n.spacerIsNative)tr(n.spacerState);else if(e._gsap.swappedIn){var o=t.parentNode;o&&(o.insertBefore(e,t),o.removeChild(t))}e._gsap.swappedIn=!1}(Oe,_,x):Ce(Oe,_,ie(Oe),M)),n||Tt.update(n),Ue=o,Tt.isReverted=n)},Tt.refresh=function(n,o,i,v){if(!Ue&&Tt.enabled||o)if(Oe&&n&&bt)fe(ir,"scrollEnd",be);else{!ht&&Nt&&Nt(Tt),Ue=Tt,r.tween&&!i&&(r.tween.kill(),r.tween=0),F&&F.pause(),Ne&&t&&t.revert({kill:!1}).invalidate(),Tt.isReverted||Tt.revert(!0,!0),Tt._subPinOffset=!1;var k,T,P,O,A,R,Y,I,X,N,L,W,q,U=Ft(),V=Wt(),K=tt?tt.duration():J(at,ot),Z=y<=.01,$=0,te=v||0,ne=re(i)?i.end:e.end,oe=e.endTrigger||Te,ae=re(i)?i.start:e.start||(0!==e.start&&Te?Oe?"0 0":"0 100%":0),le=Tt.pinnedContainer=e.pinnedContainer&&c(e.pinnedContainer,Tt),ce=Te&&Math.max(0,Ht.indexOf(Tt))||0,ue=ce;for(xt&&re(i)&&(W=De.getProperty(h,ot.p),q=De.getProperty(g,ot.p));ue--;)(R=Ht[ue]).end||R.refresh(0,1)||(Ue=Tt),!(Y=R.pin)||Y!==Te&&Y!==Oe&&Y!==le||R.isReverted||((N=N||[]).unshift(R),R.revert(!0,!0)),R!==Ht[ue]&&(ce--,ue--);for(ee(ae)&&(ae=ae(Tt)),ae=H(ae,"start",Tt),l=Pe(ae,Te,U,ot,Kt(),d,h,Tt,V,St,ct,K,tt,Tt._startClamp&&"_startClamp")||(Oe?-.001:0),ee(ne)&&(ne=ne(Tt)),_t(ne)&&!ne.indexOf("+=")&&(~ne.indexOf(" ")?ne=(_t(ae)?ae.split(" ")[0]:"")+ne:($=he(ne.substr(2),U),ne=_t(ae)?ae:(tt?De.utils.mapRange(0,tt.duration(),tt.scrollTrigger.start,tt.scrollTrigger.end,l):l)+$,oe=Te)),ne=H(ne,"end",Tt),f=Math.max(l,Pe(ne||(oe?"100% 0":K),oe,U,ot,Kt()+$,p,g,Tt,V,St,ct,K,tt,Tt._endClamp&&"_endClamp"))||-.001,$=0,ue=ce;ue--;)(Y=(R=Ht[ue]).pin)&&R.start-R._pinPush<=l&&!tt&&0<R.end&&(k=R.end-(Tt._startClamp?Math.max(0,R.start):R.start),(Y===Te&&R.start-R._pinPush<l||Y===le)&&isNaN(ae)&&($+=k*(1-R.progress)),Y===Oe&&(te+=k));if(l+=$,f+=$,Tt._startClamp&&(Tt._startClamp+=$),Tt._endClamp&&!ht&&(Tt._endClamp=f||-.001,f=Math.min(f,J(at,ot))),y=f-l||(l-=.01)&&.001,Z&&(Vt=De.utils.clamp(0,1,De.utils.normalize(l,f,j))),Tt._pinPush=te,d&&$&&((k={})[ot.a]="+="+$,le&&(k[ot.p]="-="+Kt()),De.set([d,p],k)),!Oe||ft&&Tt.end>=J(at,ot)){if(Te&&Kt()&&!tt)for(T=Te.parentNode;T&&T!==Be;)T._pinOffset&&(l-=T._pinOffset,f-=T._pinOffset),T=T.parentNode}else k=ie(Oe),O=ot===B,P=Kt(),C=parseFloat(S(ot.a))+te,!K&&1<f&&(L={style:L=(lt?Xe.scrollingElement||ze:at).style,value:L["overflow"+ot.a.toUpperCase()]},lt&&"scroll"!==ie(Be)["overflow"+ot.a.toUpperCase()]&&(L.style["overflow"+ot.a.toUpperCase()]="scroll")),Ce(Oe,_,k),w=Ee(Oe),T=Xt(Oe,!0),I=ct&&u(at,O?z:B)(),Ye?((M=[Ye+ot.os2,y+te+It]).t=_,(ue=Ye===Rt?se(Oe,ot)+y+te:0)&&(M.push(ot.d,ue+It),"auto"!==_.style.flexBasis&&(_.style.flexBasis=ue+It)),tr(M),le&&Ht.forEach(function(e){e.pin===le&&!1!==e.vars.pinSpacing&&(e._subPinOffset=!0)}),ct&&Kt(j)):(ue=se(Oe,ot))&&"auto"!==_.style.flexBasis&&(_.style.flexBasis=ue+It),ct&&((A={top:T.top+(O?P-l:I)+It,left:T.left+(O?I:P-l)+It,boxSizing:"border-box",position:"fixed"})[Ct]=A.maxWidth=Math.ceil(T.width)+It,A[Et]=A.maxHeight=Math.ceil(T.height)+It,A[Dt]=A[Dt+Ot]=A[Dt+Pt]=A[Dt+At]=A[Dt+Mt]="0",A[Rt]=k[Rt],A[Rt+Ot]=k[Rt+Ot],A[Rt+Pt]=k[Rt+Pt],A[Rt+At]=k[Rt+At],A[Rt+Mt]=k[Rt+Mt],b=function(e,t,r){for(var n,o=[],i=e.length,a=r?8:0;a<i;a+=2)n=e[a],o.push(n,n in t?t[n]:e[a+1]);return o.t=e.t,o}(x,A,$e),ht&&Kt(0)),t?(X=t._initted,Qe(1),t.render(t.duration(),!0,!0),E=S(ot.a)-C+y+te,D=1<Math.abs(y-E),ct&&D&&b.splice(b.length-2,2),t.render(0,!0,!0),X||t.invalidate(!0),t.parent||t.totalTime(t.totalTime()),Qe(0)):E=y,L&&(L.value?L.style["overflow"+ot.a.toUpperCase()]=L.value:L.style.removeProperty("overflow-"+ot.a));N&&N.forEach(function(e){return e.revert(!1,!0)}),Tt.start=l,Tt.end=f,a=s=ht?j:Kt(),tt||ht||(a<j&&Kt(j),Tt.scroll.rec=0),Tt.revert(!1,!0),Ut=yt(),G&&(qt=-1,G.restart(!0)),Ue=0,t&&it&&(t._initted||Q)&&t.progress()!==Q&&t.progress(Q||0,!0).render(t.time(),!0,!0),(Z||Vt!==Tt.progress||tt||Ne)&&(t&&!it&&t.totalProgress(tt&&l<-.001&&!Vt?De.utils.normalize(l,f,0):Vt,!0),Tt.progress=Z||(a-l)/y===Vt?0:Vt),Oe&&Ye&&(_._pinOffset=Math.round(Tt.progress*E)),F&&F.invalidate(),isNaN(W)||(W-=De.getProperty(h,ot.p),q-=De.getProperty(g,ot.p),Ae(h,ot,W),Ae(d,ot,W-(v||0)),Ae(g,ot,q),Ae(p,ot,q-(v||0))),Z&&!ht&&Tt.update(),!ke||ht||m||(m=!0,ke(Tt),m=!1)}},Tt.getVelocity=function(){return(Kt()-s)/(yt()-We)*1e3||0},Tt.endAnimation=function(){ne(Tt.callbackAnimation),t&&(F?F.progress(1):t.paused()?it||ne(t,Tt.direction<0,1):ne(t,t.reversed()))},Tt.labelToScroll=function(e){return t&&t.labels&&(l||Tt.refresh()||l)+t.labels[e]/t.duration()*y||0},Tt.getTrailing=function(e){var t=Ht.indexOf(Tt),r=0<Tt.direction?Ht.slice(0,t).reverse():Ht.slice(t+1);return(_t(e)?r.filter(function(t){return t.vars.preventOverlaps===e}):r).filter(function(e){return 0<Tt.direction?e.end<=l:e.start>=f})},Tt.update=function(e,n,o){if(!tt||o||e){var i,c,u,d,p,g,v,m=!0===ht?j:Tt.scroll(),x=e?0:(m-l)/y,k=x<0?0:1<x?1:x||0,S=Tt.progress;if(n&&(s=a,a=tt?Kt():m,Ze&&(N=X,X=t&&!it?t.totalProgress():k)),Fe&&Oe&&!Ue&&!mt&&bt&&(!k&&l<m+(m-s)/(yt()-We)*Fe?k=1e-4:1===k&&f>m+(m-s)/(yt()-We)*Fe&&(k=.9999)),k!==S&&Tt.enabled){if(d=(p=(i=Tt.isActive=!!k&&k<1)!=(!!S&&S<1))||!!k!=!!S,Tt.direction=S<k?1:-1,Tt.progress=k,d&&!Ue&&(c=k&&!S?0:1===k?1:1===S?2:3,it&&(u=!p&&"none"!==pt[c+1]&&pt[c+1]||pt[c],v=t&&("complete"===u||"reset"===u||u in t))),nt&&(p||v)&&(v||Se||!t)&&(ee(nt)?nt(Tt):Tt.getTrailing(nt).forEach(function(e){return e.endAnimation()})),it||(!F||Ue||mt?t&&t.totalProgress(k,!(!Ue||!Ut&&!e)):(F._dp._time-F._start!==F._time&&F.render(F._dp._time-F._start),F.resetTo?F.resetTo("totalProgress",k,t._tTime/t._tDur):(F.vars.totalProgress=k,F.invalidate().restart()))),Oe)if(e&&Ye&&(_.style[Ye+ot.os2]=P),ct){if(d){if(g=!e&&S<k&&m<f+1&&m+1>=J(at,ot),$e)if(e||!i&&!g)Me(Oe,_);else{var M=Xt(Oe,!0),A=m-l;Me(Oe,Be,M.top+(ot===B?A:0)+It,M.left+(ot===B?0:A)+It)}tr(i||g?b:w),D&&k<1&&i||T(C+(1!==k||g?0:E))}}else T(V(C+E*k));!Ze||r.tween||Ue||mt||G.restart(!0),ye&&(p||Ke&&k&&(k<1||!dt))&&He(ye.targets).forEach(function(e){return e.classList[i||Ke?"add":"remove"](ye.className)}),!ve||it||e||ve(Tt),d&&!Ue?(it&&(v&&("complete"===u?t.pause().totalProgress(1):"reset"===u?t.restart(!0).pause():"restart"===u?t.restart(!0):t[u]()),ve&&ve(Tt)),!p&&dt||(_e&&p&&oe(Tt,_e),ut[c]&&oe(Tt,ut[c]),Ke&&(1===k?Tt.kill(!1,1):ut[c]=0),p||ut[c=1===k?1:3]&&oe(Tt,ut[c])),rt&&!i&&Math.abs(Tt.getVelocity())>(te(rt)?rt:2500)&&(ne(Tt.callbackAnimation),F?F.progress(1):ne(t,"reverse"===u?1:!k,1))):it&&ve&&!Ue&&ve(Tt)}if(Y){var R=tt?m/tt.duration()*(tt._caScrollDist||0):m;O(R+(h._isFlipped?1:0)),Y(R)}ue&&ue(-m/tt.duration()*(tt._caScrollDist||0))}},Tt.enable=function(e,t){Tt.enabled||(Tt.enabled=!0,fe(at,"resize",xe),lt||fe(at,"scroll",me),Nt&&fe(ir,"refreshInit",Nt),!1!==e&&(Tt.progress=Vt=0,a=s=qt=Kt()),!1!==t&&Tt.refresh())},Tt.getTween=function(e){return e&&r?r.tween:F},Tt.setPositions=function(e,t,r,n){if(tt){var o=tt.scrollTrigger,i=tt.duration(),a=o.end-o.start;e=o.start+a*e/i,t=o.start+a*t/i}Tt.refresh(!1,!1,{start:L(e,r&&!!Tt._startClamp),end:L(t,r&&!!Tt._endClamp)},n),Tt.update()},Tt.adjustPinSpacing=function(e){if(M&&e){var t=M.indexOf(ot.d)+1;M[t]=parseFloat(M[t])+e+It,M[1]=parseFloat(M[1])+e+It,tr(M)}},Tt.disable=function(e,t){if(Tt.enabled&&(!1!==e&&Tt.revert(!0,!0),Tt.enabled=Tt.isActive=!1,t||F&&F.pause(),j=0,o&&(o.uncache=1),Nt&&de(ir,"refreshInit",Nt),G&&(G.pause(),r.tween&&r.tween.kill()&&(r.tween=0)),!lt)){for(var n=Ht.length;n--;)if(Ht[n].scroller===at&&Ht[n]!==Tt)return;de(at,"resize",xe),lt||de(at,"scroll",me)}},Tt.kill=function(r,n){Tt.disable(r,n),F&&!n&&F.kill(),we&&delete Lt[we];var i=Ht.indexOf(Tt);0<=i&&Ht.splice(i,1),i===je&&0<Zt&&je--,i=0,Ht.forEach(function(e){return e.scroller===Tt.scroller&&(i=1)}),i||ht||(Tt.scroll.rec=0),t&&(t.scrollTrigger=null,r&&t.revert({kill:!1}),n||t.kill()),d&&[d,p,h,g].forEach(function(e){return e.parentNode&&e.parentNode.removeChild(e)}),vt===Tt&&(vt=0),Oe&&(o&&(o.uncache=1),i=0,Ht.forEach(function(e){return e.pin===Oe&&i++}),i||(o.spacer=0)),e.onKill&&e.onKill(Tt)},Ht.push(Tt),Tt.enable(!1,!1),pe&&pe(Tt),t&&t.add&&!y){var er=Tt.update;Tt.update=function(){Tt.update=er,l||f||Tt.refresh()},De.delayedCall(.01,Tt.update),y=.01,l=f=0}else Tt.refresh();Oe&&function(){if(gt!==Gt){var e=gt=Gt;requestAnimationFrame(function(){return e===Gt&&jt(!0)})}}()}else this.update=this.refresh=this.kill=U},ir.register=function(e){return Ye||(De=e||j(),G()&&window.document&&ir.enable(),Ye=wt),Ye},ir.defaults=function(e){if(e)for(var t in e)Bt[t]=e[t];return Bt},ir.disable=function(e,t){wt=0,Ht.forEach(function(r){return r[t?"kill":"disable"](e)}),de(Ie,"wheel",me),de(Xe,"scroll",me),clearInterval(qe),de(Xe,"touchcancel",U),de(Be,"touchstart",U),ue(de,Xe,"pointerdown,touchstart,mousedown",W),ue(de,Xe,"pointerup,touchend,mouseup",q),Fe.kill(),Q(de);for(var r=0;r<A.length;r+=3)pe(de,A[r],A[r+1]),pe(de,A[r],A[r+2])},ir.enable=function(){if(Ie=window,Xe=document,ze=Xe.documentElement,Be=Xe.body,De&&(He=De.utils.toArray,Le=De.utils.clamp,at=De.core.context||U,Qe=De.core.suppressOverwrites||U,st=Ie.history.scrollRestoration||"auto",Kt=Ie.pageYOffset,De.core.globals("ScrollTrigger",ir),Be)){wt=1,(lt=document.createElement("div")).style.height="100vh",lt.style.position="absolute",Se(),function e(){return wt&&requestAnimationFrame(e)}(),N.register(De),ir.isTouch=N.isTouch,it=N.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),rt=1===N.isTouch,fe(Ie,"wheel",me),Ne=[Ie,Xe,ze,Be],De.matchMedia?(ir.matchMedia=function(e){var t,r=De.matchMedia();for(t in e)r.add(t,e[t]);return r},De.addEventListener("matchMediaInit",function(){return _e()}),De.addEventListener("matchMediaRevert",function(){return we()}),De.addEventListener("matchMedia",function(){jt(0,1),Ut("matchMedia")}),De.matchMedia("(orientation: portrait)",function(){return ye(),ye})):console.warn("Requires GSAP 3.11.0 or later"),ye(),fe(Xe,"scroll",me);var e,t,r=Be.style,n=r.borderTopStyle,o=De.core.Animation.prototype;for(o.revert||Object.defineProperty(o,"revert",{value:function(){return this.time(-.01,!0)}}),r.borderTopStyle="solid",e=Xt(Be),B.m=Math.round(e.top+B.sc())||0,z.m=Math.round(e.left+z.sc())||0,n?r.borderTopStyle=n:r.removeProperty("border-top-style"),qe=setInterval(ve,250),De.delayedCall(.5,function(){return mt=0}),fe(Xe,"touchcancel",U),fe(Be,"touchstart",U),ue(fe,Xe,"pointerdown,touchstart,mousedown",W),ue(fe,Xe,"pointerup,touchend,mouseup",q),Ge=De.utils.checkPrefix("transform"),Qt.push(Ge),Ye=yt(),Fe=De.delayedCall(.2,jt).pause(),$e=[Xe,"visibilitychange",function(){var e=Ie.innerWidth,t=Ie.innerHeight;Xe.hidden?(Ke=e,Ze=t):Ke===e&&Ze===t||xe()},Xe,"DOMContentLoaded",jt,Ie,"load",jt,Ie,"resize",xe],Q(fe),Ht.forEach(function(e){return e.enable(0,1)}),t=0;t<A.length;t+=3)pe(de,A[t],A[t+1]),pe(de,A[t],A[t+2])}},ir.config=function(e){"limitCallbacks"in e&&(dt=!!e.limitCallbacks);var t=e.syncInterval;t&&clearInterval(qe)||(qe=t)&&setInterval(ve,t),"ignoreMobileResize"in e&&(rt=1===ir.isTouch&&e.ignoreMobileResize),"autoRefreshEvents"in e&&(Q(de)||Q(fe,e.autoRefreshEvents||"none"),et=-1===(e.autoRefreshEvents+"").indexOf("resize"))},ir.scrollerProxy=function(e,t){var r=c(e),n=A.indexOf(r),o=K(r);~n&&A.splice(n,o?6:2),t&&(o?R.unshift(Ie,t,Be,t,ze,t):R.unshift(r,t))},ir.clearMatchMedia=function(e){Ht.forEach(function(t){return t._ctx&&t._ctx.query===e&&t._ctx.kill(!0,!0)})},ir.isInViewport=function(e,t,r){var n=(_t(e)?c(e):e).getBoundingClientRect(),o=n[r?Ct:Et]*t||0;return r?0<n.right-o&&n.left+o<Ie.innerWidth:0<n.bottom-o&&n.top+o<Ie.innerHeight},ir.positionInViewport=function(e,t,r){_t(e)&&(e=c(e));var n=e.getBoundingClientRect(),o=n[r?Ct:Et],i=null==t?o/2:t in Nt?Nt[t]*o:~t.indexOf("%")?parseFloat(t)*o/100:parseFloat(t)||0;return r?(n.left+i)/Ie.innerWidth:(n.top+i)/Ie.innerHeight},ir.killAll=function(e){if(Ht.slice(0).forEach(function(e){return"ScrollSmoother"!==e.vars.id&&e.kill()}),!0!==e){var t=Wt.killAll||[];Wt={},t.forEach(function(e){return e()})}},ir);function ir(e,t){Ye||ir.register(De)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),at(this),this.init(e,t)}function ar(e,t,r,n){return n<t?e(n):t<0&&e(0),n<r?(n-t)/(r-t):r<0?t/(t-r):1}function sr(e,t){!0===t?e.style.removeProperty("touch-action"):e.style.touchAction=!0===t?"auto":t?"pan-"+t+(N.isTouch?" pinch-zoom":""):"none",e===ze&&sr(Be,t)}function lr(e){var t,r=e.event,n=e.target,o=e.axis,i=(r.changedTouches?r.changedTouches[0]:r).target,a=i._gsap||De.core.getCache(i),s=yt();if(!a._isScrollT||2e3<s-a._isScrollT){for(;i&&i!==Be&&(i.scrollHeight<=i.clientHeight&&i.scrollWidth<=i.clientWidth||!fr[(t=ie(i)).overflowY]&&!fr[t.overflowX]);)i=i.parentNode;a._isScroll=i&&i!==n&&!K(i)&&(fr[(t=ie(i)).overflowY]||fr[t.overflowX]),a._isScrollT=s}!a._isScroll&&"x"!==o||(r.stopPropagation(),r._gsapAllow=!0)}function cr(e,t,r,n){return N.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:t,onWheel:n=n&&lr,onPress:n,onDrag:n,onScroll:n,onEnable:function(){return r&&fe(Xe,N.eventTypes[0],pr,!1,!0)},onDisable:function(){return de(Xe,N.eventTypes[0],pr,!0)}})}or.version="3.12.5",or.saveStyles=function(e){return e?He(e).forEach(function(e){if(e&&e.style){var t=Vt.indexOf(e);0<=t&&Vt.splice(t,5),Vt.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),De.core.getCache(e),at())}}):Vt},or.revert=function(e,t){return _e(!e,t)},or.create=function(e,t){return new or(e,t)},or.refresh=function(e){return e?xe():(Ye||or.register())&&jt(!0)},or.update=function(e){return++A.cache&&$t(!0===e?2:0)},or.clearScrollMemory=ke,or.maxScroll=function(e,t){return J(e,t?z:B)},or.getScrollFunc=function(e,t){return u(c(e),t?z:B)},or.getById=function(e){return Lt[e]},or.getAll=function(){return Ht.filter(function(e){return"ScrollSmoother"!==e.vars.id})},or.isScrolling=function(){return!!bt},or.snapDirectional=ce,or.addEventListener=function(e,t){var r=Wt[e]||(Wt[e]=[]);~r.indexOf(t)||r.push(t)},or.removeEventListener=function(e,t){var r=Wt[e],n=r&&r.indexOf(t);0<=n&&r.splice(n,1)},or.batch=function(e,t){function r(e,t){var r=[],n=[],o=De.delayedCall(a,function(){t(r,n),r=[],n=[]}).pause();return function(e){r.length||o.restart(!0),r.push(e.trigger),n.push(e),s<=r.length&&o.progress(1)}}var n,o=[],i={},a=t.interval||.016,s=t.batchMax||1e9;for(n in t)i[n]="on"===n.substr(0,2)&&ee(t[n])&&"onRefreshInit"!==n?r(0,t[n]):t[n];return ee(s)&&(s=s(),fe(or,"refresh",function(){return s=t.batchMax()})),He(e).forEach(function(e){var t={};for(n in i)t[n]=i[n];t.trigger=e,o.push(or.create(t))}),o};var ur,fr={auto:1,scroll:1},dr=/(input|label|select|textarea)/i,pr=function(e){var t=dr.test(e.target.tagName);(t||ur)&&(e._gsapAllow=!0,ur=t)};or.sort=function(e){return Ht.sort(e||function(e,t){return-1e6*(e.vars.refreshPriority||0)+e.start-(t.start+-1e6*(t.vars.refreshPriority||0))})},or.observe=function(e){return new N(e)},or.normalizeScroll=function(e){if(void 0===e)return tt;if(!0===e&&tt)return tt.enable();if(!1===e)return tt&&tt.kill(),void(tt=e);var t=e instanceof N?e:function(e){function t(){return l=!1}function r(){a=J(x,B),R=Le(it?1:0,a),g&&(O=Le(0,J(x,z))),s=Gt}function n(){_._gsap.y=V(parseFloat(_._gsap.y)+k.offset)+"px",_.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(_._gsap.y)+", 0, 1)",k.offset=k.cacheID=0}function o(){r(),f.isActive()&&f.vars.scrollY>a&&(k()>a?f.progress(1)&&k(a):f.resetTo("scrollY",a))}re(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var i,a,s,l,f,d,p,h,g=e.normalizeScrollX,v=e.momentum,m=e.allowNestedScroll,y=e.onRelease,x=c(e.target)||ze,b=De.core.globals().ScrollSmoother,w=b&&b.get(),_=it&&(e.content&&c(e.content)||w&&!1!==e.content&&!w.smooth()&&w.content()),k=u(x,B),S=u(x,z),T=1,C=(N.isTouch&&Ie.visualViewport?Ie.visualViewport.scale*Ie.visualViewport.width:Ie.outerWidth)/Ie.innerWidth,E=0,P=ee(v)?function(){return v(i)}:function(){return v||2.8},M=cr(x,e.type,!0,m),O=U,R=U;return _&&De.set(_,{y:"+=0"}),e.ignoreCheck=function(e){return it&&"touchmove"===e.type&&function(){if(l){requestAnimationFrame(t);var e=V(i.deltaY/2),r=R(k.v-e);if(_&&r!==k.v+k.offset){k.offset=r-k.v;var o=V((parseFloat(_&&_._gsap.y)||0)-k.offset);_.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+o+", 0, 1)",_._gsap.y=o+"px",k.cacheID=A.cache,$t()}return!0}k.offset&&n(),l=!0}()||1.05<T&&"touchstart"!==e.type||i.isGesturing||e.touches&&1<e.touches.length},e.onPress=function(){l=!1;var e=T;T=V((Ie.visualViewport&&Ie.visualViewport.scale||1)/C),f.pause(),e!==T&&sr(x,1.01<T||!g&&"x"),d=S(),p=k(),r(),s=Gt},e.onRelease=e.onGestureStart=function(e,t){if(k.offset&&n(),t){A.cache++;var r,i,s=P();g&&(i=(r=S())+.05*s*-e.velocityX/.227,s*=ar(S,r,i,J(x,z)),f.vars.scrollX=O(i)),i=(r=k())+.05*s*-e.velocityY/.227,s*=ar(k,r,i,J(x,B)),f.vars.scrollY=R(i),f.invalidate().duration(s).play(.01),(it&&f.vars.scrollY>=a||a-1<=r)&&De.to({},{onUpdate:o,duration:s})}else h.restart(!0);y&&y(e)},e.onWheel=function(){f._ts&&f.pause(),1e3<yt()-E&&(s=0,E=yt())},e.onChange=function(e,t,o,i,a){if(Gt!==s&&r(),t&&g&&S(O(i[2]===t?d+(e.startX-e.x):S()+t-i[1])),o){k.offset&&n();var l=a[2]===o,c=l?p+e.startY-e.y:k()+o-a[1],u=R(c);l&&c!==u&&(p+=u-c),k(u)}(o||t)&&$t()},e.onEnable=function(){sr(x,!g&&"x"),or.addEventListener("refresh",o),fe(Ie,"resize",o),k.smooth&&(k.target.style.scrollBehavior="auto",k.smooth=S.smooth=!1),M.enable()},e.onDisable=function(){sr(x,!0),de(Ie,"resize",o),or.removeEventListener("refresh",o),M.kill()},e.lockAxis=!1!==e.lockAxis,((i=new N(e)).iOS=it)&&!k()&&k(1),it&&De.ticker.add(U),h=i._dc,f=De.to(i,{ease:"power4",paused:!0,inherit:!1,scrollX:g?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:Oe(k,k(),function(){return f.pause()})},onUpdate:$t,onComplete:h.vars.onComplete}),i}(e);return tt&&tt.target===t.target&&tt.kill(),K(t.target)&&(tt=t),t},or.core={_getVelocityProp:f,_inputObserver:cr,_scrollers:A,_proxies:R,bridge:{ss:function(){bt||Ut("scrollStart"),bt=yt()},ref:function(){return Ue}}},j()&&De.registerPlugin(or),e.ScrollTrigger=or,e.default=or,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete e.default});