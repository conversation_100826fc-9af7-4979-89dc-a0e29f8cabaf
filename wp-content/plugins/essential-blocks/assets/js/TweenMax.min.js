/*! For license information please see TweenMax.min.js.LICENSE.txt */
var _gsScope="undefined"!=typeof module&&module.exports&&"undefined"!=typeof global?global:this||window;(_gsScope._gsQueue||(_gsScope._gsQueue=[])).push(function(){"use strict";var t,e,i,s,r,n,a,o,l,h,_,u,f,c,p,d;_gsScope._gsDefine("TweenMax",["core.Animation","core.SimpleTimeline","TweenLite"],function(t,e,i){var s=function(t){var e,i=[],s=t.length;for(e=0;e!==s;i.push(t[e++]));return i},r=function(t,e,i){var s,r,n=t.cycle;for(s in n)r=n[s],t[s]="function"==typeof r?r(i,e[i]):r[i%r.length];delete t.cycle},n=function(t,e,s){i.call(this,t,e,s),this._cycle=0,this._yoyo=!0===this.vars.yoyo||!!this.vars.yoyoEase,this._repeat=this.vars.repeat||0,this._repeatDelay=this.vars.repeatDelay||0,this._repeat&&this._uncache(!0),this.render=n.prototype.render},a=1e-10,o=i._internals,l=o.isSelector,h=o.isArray,_=n.prototype=i.to({},.1,{}),u=[];n.version="1.20.3",_.constructor=n,_.kill()._gc=!1,n.killTweensOf=n.killDelayedCallsTo=i.killTweensOf,n.getTweensOf=i.getTweensOf,n.lagSmoothing=i.lagSmoothing,n.ticker=i.ticker,n.render=i.render,_.invalidate=function(){return this._yoyo=!0===this.vars.yoyo||!!this.vars.yoyoEase,this._repeat=this.vars.repeat||0,this._repeatDelay=this.vars.repeatDelay||0,this._yoyoEase=null,this._uncache(!0),i.prototype.invalidate.call(this)},_.updateTo=function(t,e){var s,r=this.ratio,n=this.vars.immediateRender||t.immediateRender;for(s in e&&this._startTime<this._timeline._time&&(this._startTime=this._timeline._time,this._uncache(!1),this._gc?this._enabled(!0,!1):this._timeline.insert(this,this._startTime-this._delay)),t)this.vars[s]=t[s];if(this._initted||n)if(e)this._initted=!1,n&&this.render(0,!0,!0);else if(this._gc&&this._enabled(!0,!1),this._notifyPluginsOfEnabled&&this._firstPT&&i._onPluginEvent("_onDisable",this),this._time/this._duration>.998){var a=this._totalTime;this.render(0,!0,!1),this._initted=!1,this.render(a,!0,!1)}else if(this._initted=!1,this._init(),this._time>0||n)for(var o,l=1/(1-r),h=this._firstPT;h;)o=h.s+h.c,h.c*=l,h.s=o-h.c,h=h._next;return this},_.render=function(t,e,s){this._initted||0===this._duration&&this.vars.repeat&&this.invalidate();var r,n,l,h,_,u,f,c,p,d=this._dirty?this.totalDuration():this._totalDuration,m=this._time,g=this._totalTime,y=this._cycle,v=this._duration,T=this._rawPrevTime;if(t>=d-1e-7&&t>=0?(this._totalTime=d,this._cycle=this._repeat,this._yoyo&&1&this._cycle?(this._time=0,this.ratio=this._ease._calcEnd?this._ease.getRatio(0):0):(this._time=v,this.ratio=this._ease._calcEnd?this._ease.getRatio(1):1),this._reversed||(r=!0,n="onComplete",s=s||this._timeline.autoRemoveChildren),0===v&&(this._initted||!this.vars.lazy||s)&&(this._startTime===this._timeline._duration&&(t=0),(0>T||0>=t&&t>=-1e-7||T===a&&"isPause"!==this.data)&&T!==t&&(s=!0,T>a&&(n="onReverseComplete")),this._rawPrevTime=c=!e||t||T===t?t:a)):1e-7>t?(this._totalTime=this._time=this._cycle=0,this.ratio=this._ease._calcEnd?this._ease.getRatio(0):0,(0!==g||0===v&&T>0)&&(n="onReverseComplete",r=this._reversed),0>t&&(this._active=!1,0===v&&(this._initted||!this.vars.lazy||s)&&(T>=0&&(s=!0),this._rawPrevTime=c=!e||t||T===t?t:a)),this._initted||(s=!0)):(this._totalTime=this._time=t,0!==this._repeat&&(h=v+this._repeatDelay,this._cycle=this._totalTime/h|0,0!==this._cycle&&this._cycle===this._totalTime/h&&t>=g&&this._cycle--,this._time=this._totalTime-this._cycle*h,this._yoyo&&!!(1&this._cycle)&&(this._time=v-this._time,(p=this._yoyoEase||this.vars.yoyoEase)&&(this._yoyoEase||(!0!==p||this._initted?this._yoyoEase=p=!0===p?this._ease:p instanceof Ease?p:Ease.map[p]:(p=this.vars.ease,this._yoyoEase=p=p?p instanceof Ease?p:"function"==typeof p?new Ease(p,this.vars.easeParams):Ease.map[p]||i.defaultEase:i.defaultEase)),this.ratio=p?1-p.getRatio((v-this._time)/v):0)),this._time>v?this._time=v:this._time<0&&(this._time=0)),this._easeType&&!p?(_=this._time/v,(1===(u=this._easeType)||3===u&&_>=.5)&&(_=1-_),3===u&&(_*=2),1===(f=this._easePower)?_*=_:2===f?_*=_*_:3===f?_*=_*_*_:4===f&&(_*=_*_*_*_),1===u?this.ratio=1-_:2===u?this.ratio=_:this._time/v<.5?this.ratio=_/2:this.ratio=1-_/2):p||(this.ratio=this._ease.getRatio(this._time/v))),m!==this._time||s||y!==this._cycle){if(!this._initted){if(this._init(),!this._initted||this._gc)return;if(!s&&this._firstPT&&(!1!==this.vars.lazy&&this._duration||this.vars.lazy&&!this._duration))return this._time=m,this._totalTime=g,this._rawPrevTime=T,this._cycle=y,o.lazyTweens.push(this),void(this._lazy=[t,e]);!this._time||r||p?r&&this._ease._calcEnd&&!p&&(this.ratio=this._ease.getRatio(0===this._time?0:1)):this.ratio=this._ease.getRatio(this._time/v)}for(!1!==this._lazy&&(this._lazy=!1),this._active||!this._paused&&this._time!==m&&t>=0&&(this._active=!0),0===g&&(2===this._initted&&t>0&&this._init(),this._startAt&&(t>=0?this._startAt.render(t,!0,s):n||(n="_dummyGS")),this.vars.onStart&&(0!==this._totalTime||0===v)&&(e||this._callback("onStart"))),l=this._firstPT;l;)l.f?l.t[l.p](l.c*this.ratio+l.s):l.t[l.p]=l.c*this.ratio+l.s,l=l._next;this._onUpdate&&(0>t&&this._startAt&&this._startTime&&this._startAt.render(t,!0,s),e||(this._totalTime!==g||n)&&this._callback("onUpdate")),this._cycle!==y&&(e||this._gc||this.vars.onRepeat&&this._callback("onRepeat")),n&&(!this._gc||s)&&(0>t&&this._startAt&&!this._onUpdate&&this._startTime&&this._startAt.render(t,!0,s),r&&(this._timeline.autoRemoveChildren&&this._enabled(!1,!1),this._active=!1),!e&&this.vars[n]&&this._callback(n),0===v&&this._rawPrevTime===a&&c!==a&&(this._rawPrevTime=0))}else g!==this._totalTime&&this._onUpdate&&(e||this._callback("onUpdate"))},n.to=function(t,e,i){return new n(t,e,i)},n.from=function(t,e,i){return i.runBackwards=!0,i.immediateRender=0!=i.immediateRender,new n(t,e,i)},n.fromTo=function(t,e,i,s){return s.startAt=i,s.immediateRender=0!=s.immediateRender&&0!=i.immediateRender,new n(t,e,s)},n.staggerTo=n.allTo=function(t,e,a,o,_,f,c){o=o||0;var p,d,m,g,y=0,v=[],T=function(){a.onComplete&&a.onComplete.apply(a.onCompleteScope||this,arguments),_.apply(c||a.callbackScope||this,f||u)},x=a.cycle,b=a.startAt&&a.startAt.cycle;for(h(t)||("string"==typeof t&&(t=i.selector(t)||t),l(t)&&(t=s(t))),t=t||[],0>o&&((t=s(t)).reverse(),o*=-1),p=t.length-1,m=0;p>=m;m++){for(g in d={},a)d[g]=a[g];if(x&&(r(d,t,m),null!=d.duration&&(e=d.duration,delete d.duration)),b){for(g in b=d.startAt={},a.startAt)b[g]=a.startAt[g];r(d.startAt,t,m)}d.delay=y+(d.delay||0),m===p&&_&&(d.onComplete=T),v[m]=new n(t[m],e,d),y+=o}return v},n.staggerFrom=n.allFrom=function(t,e,i,s,r,a,o){return i.runBackwards=!0,i.immediateRender=0!=i.immediateRender,n.staggerTo(t,e,i,s,r,a,o)},n.staggerFromTo=n.allFromTo=function(t,e,i,s,r,a,o,l){return s.startAt=i,s.immediateRender=0!=s.immediateRender&&0!=i.immediateRender,n.staggerTo(t,e,s,r,a,o,l)},n.delayedCall=function(t,e,i,s,r){return new n(e,0,{delay:t,onComplete:e,onCompleteParams:i,callbackScope:s,onReverseComplete:e,onReverseCompleteParams:i,immediateRender:!1,useFrames:r,overwrite:0})},n.set=function(t,e){return new n(t,0,e)},n.isTweening=function(t){return i.getTweensOf(t,!0).length>0};var f=function(t,e){for(var s=[],r=0,n=t._first;n;)n instanceof i?s[r++]=n:(e&&(s[r++]=n),r=(s=s.concat(f(n,e))).length),n=n._next;return s},c=n.getAllTweens=function(e){return f(t._rootTimeline,e).concat(f(t._rootFramesTimeline,e))};n.killAll=function(t,i,s,r){null==i&&(i=!0),null==s&&(s=!0);var n,a,o,l=c(0!=r),h=l.length,_=i&&s&&r;for(o=0;h>o;o++)a=l[o],(_||a instanceof e||(n=a.target===a.vars.onComplete)&&s||i&&!n)&&(t?a.totalTime(a._reversed?0:a.totalDuration()):a._enabled(!1,!1))},n.killChildTweensOf=function(t,e){if(null!=t){var r,a,_,u,f,c=o.tweenLookup;if("string"==typeof t&&(t=i.selector(t)||t),l(t)&&(t=s(t)),h(t))for(u=t.length;--u>-1;)n.killChildTweensOf(t[u],e);else{for(_ in r=[],c)for(a=c[_].target.parentNode;a;)a===t&&(r=r.concat(c[_].tweens)),a=a.parentNode;for(f=r.length,u=0;f>u;u++)e&&r[u].totalTime(r[u].totalDuration()),r[u]._enabled(!1,!1)}}};var p=function(t,i,s,r){i=!1!==i,s=!1!==s;for(var n,a,o=c(r=!1!==r),l=i&&s&&r,h=o.length;--h>-1;)a=o[h],(l||a instanceof e||(n=a.target===a.vars.onComplete)&&s||i&&!n)&&a.paused(t)};return n.pauseAll=function(t,e,i){p(!0,t,e,i)},n.resumeAll=function(t,e,i){p(!1,t,e,i)},n.globalTimeScale=function(e){var s=t._rootTimeline,r=i.ticker.time;return arguments.length?(e=e||a,s._startTime=r-(r-s._startTime)*s._timeScale/e,s=t._rootFramesTimeline,r=i.ticker.frame,s._startTime=r-(r-s._startTime)*s._timeScale/e,s._timeScale=t._rootTimeline._timeScale=e,e):s._timeScale},_.progress=function(t,e){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&1&this._cycle?1-t:t)+this._cycle*(this._duration+this._repeatDelay),e):this._time/this.duration()},_.totalProgress=function(t,e){return arguments.length?this.totalTime(this.totalDuration()*t,e):this._totalTime/this.totalDuration()},_.time=function(t,e){return arguments.length?(this._dirty&&this.totalDuration(),t>this._duration&&(t=this._duration),this._yoyo&&1&this._cycle?t=this._duration-t+this._cycle*(this._duration+this._repeatDelay):0!==this._repeat&&(t+=this._cycle*(this._duration+this._repeatDelay)),this.totalTime(t,e)):this._time},_.duration=function(e){return arguments.length?t.prototype.duration.call(this,e):this._duration},_.totalDuration=function(t){return arguments.length?-1===this._repeat?this:this.duration((t-this._repeat*this._repeatDelay)/(this._repeat+1)):(this._dirty&&(this._totalDuration=-1===this._repeat?999999999999:this._duration*(this._repeat+1)+this._repeatDelay*this._repeat,this._dirty=!1),this._totalDuration)},_.repeat=function(t){return arguments.length?(this._repeat=t,this._uncache(!0)):this._repeat},_.repeatDelay=function(t){return arguments.length?(this._repeatDelay=t,this._uncache(!0)):this._repeatDelay},_.yoyo=function(t){return arguments.length?(this._yoyo=t,this):this._yoyo},n},!0),_gsScope._gsDefine("TimelineLite",["core.Animation","core.SimpleTimeline","TweenLite"],function(t,e,i){var s=function(t){e.call(this,t),this._labels={},this.autoRemoveChildren=!0===this.vars.autoRemoveChildren,this.smoothChildTiming=!0===this.vars.smoothChildTiming,this._sortChildren=!0,this._onUpdate=this.vars.onUpdate;var i,s,r=this.vars;for(s in r)i=r[s],l(i)&&-1!==i.join("").indexOf("{self}")&&(r[s]=this._swapSelfInParams(i));l(r.tweens)&&this.add(r.tweens,0,r.align,r.stagger)},r=1e-10,n=i._internals,a=s._internals={},o=n.isSelector,l=n.isArray,h=n.lazyTweens,_=n.lazyRender,u=_gsScope._gsDefine.globals,f=function(t){var e,i={};for(e in t)i[e]=t[e];return i},c=function(t,e,i){var s,r,n=t.cycle;for(s in n)r=n[s],t[s]="function"==typeof r?r(i,e[i]):r[i%r.length];delete t.cycle},p=a.pauseCallback=function(){},d=function(t){var e,i=[],s=t.length;for(e=0;e!==s;i.push(t[e++]));return i},m=s.prototype=new e;return s.version="1.20.3",m.constructor=s,m.kill()._gc=m._forcingPlayhead=m._hasPause=!1,m.to=function(t,e,s,r){var n=s.repeat&&u.TweenMax||i;return e?this.add(new n(t,e,s),r):this.set(t,s,r)},m.from=function(t,e,s,r){return this.add((s.repeat&&u.TweenMax||i).from(t,e,s),r)},m.fromTo=function(t,e,s,r,n){var a=r.repeat&&u.TweenMax||i;return e?this.add(a.fromTo(t,e,s,r),n):this.set(t,r,n)},m.staggerTo=function(t,e,r,n,a,l,h,_){var u,p,m=new s({onComplete:l,onCompleteParams:h,callbackScope:_,smoothChildTiming:this.smoothChildTiming}),g=r.cycle;for("string"==typeof t&&(t=i.selector(t)||t),o(t=t||[])&&(t=d(t)),0>(n=n||0)&&((t=d(t)).reverse(),n*=-1),p=0;p<t.length;p++)(u=f(r)).startAt&&(u.startAt=f(u.startAt),u.startAt.cycle&&c(u.startAt,t,p)),g&&(c(u,t,p),null!=u.duration&&(e=u.duration,delete u.duration)),m.to(t[p],e,u,p*n);return this.add(m,a)},m.staggerFrom=function(t,e,i,s,r,n,a,o){return i.immediateRender=0!=i.immediateRender,i.runBackwards=!0,this.staggerTo(t,e,i,s,r,n,a,o)},m.staggerFromTo=function(t,e,i,s,r,n,a,o,l){return s.startAt=i,s.immediateRender=0!=s.immediateRender&&0!=i.immediateRender,this.staggerTo(t,e,s,r,n,a,o,l)},m.call=function(t,e,s,r){return this.add(i.delayedCall(0,t,e,s),r)},m.set=function(t,e,s){return s=this._parseTimeOrLabel(s,0,!0),null==e.immediateRender&&(e.immediateRender=s===this._time&&!this._paused),this.add(new i(t,0,e),s)},s.exportRoot=function(t,e){null==(t=t||{}).smoothChildTiming&&(t.smoothChildTiming=!0);var r,n,a,o,l=new s(t),h=l._timeline;for(null==e&&(e=!0),h._remove(l,!0),l._startTime=0,l._rawPrevTime=l._time=l._totalTime=h._time,a=h._first;a;)o=a._next,e&&a instanceof i&&a.target===a.vars.onComplete||(0>(n=a._startTime-a._delay)&&(r=1),l.add(a,n)),a=o;return h.add(l,0),r&&l.totalDuration(),l},m.add=function(r,n,a,o){var h,_,u,f,c,p;if("number"!=typeof n&&(n=this._parseTimeOrLabel(n,0,!0,r)),!(r instanceof t)){if(r instanceof Array||r&&r.push&&l(r)){for(a=a||"normal",o=o||0,h=n,_=r.length,u=0;_>u;u++)l(f=r[u])&&(f=new s({tweens:f})),this.add(f,h),"string"!=typeof f&&"function"!=typeof f&&("sequence"===a?h=f._startTime+f.totalDuration()/f._timeScale:"start"===a&&(f._startTime-=f.delay())),h+=o;return this._uncache(!0)}if("string"==typeof r)return this.addLabel(r,n);if("function"!=typeof r)throw"Cannot add "+r+" into the timeline; it is not a tween, timeline, function, or string.";r=i.delayedCall(0,r)}if(e.prototype.add.call(this,r,n),r._time&&r.render((this.rawTime()-r._startTime)*r._timeScale,!1,!1),(this._gc||this._time===this._duration)&&!this._paused&&this._duration<this.duration())for(p=(c=this).rawTime()>r._startTime;c._timeline;)p&&c._timeline.smoothChildTiming?c.totalTime(c._totalTime,!0):c._gc&&c._enabled(!0,!1),c=c._timeline;return this},m.remove=function(e){if(e instanceof t){this._remove(e,!1);var i=e._timeline=e.vars.useFrames?t._rootFramesTimeline:t._rootTimeline;return e._startTime=(e._paused?e._pauseTime:i._time)-(e._reversed?e.totalDuration()-e._totalTime:e._totalTime)/e._timeScale,this}if(e instanceof Array||e&&e.push&&l(e)){for(var s=e.length;--s>-1;)this.remove(e[s]);return this}return"string"==typeof e?this.removeLabel(e):this.kill(null,e)},m._remove=function(t,i){return e.prototype._remove.call(this,t,i),this._last?this._time>this.duration()&&(this._time=this._duration,this._totalTime=this._totalDuration):this._time=this._totalTime=this._duration=this._totalDuration=0,this},m.append=function(t,e){return this.add(t,this._parseTimeOrLabel(null,e,!0,t))},m.insert=m.insertMultiple=function(t,e,i,s){return this.add(t,e||0,i,s)},m.appendMultiple=function(t,e,i,s){return this.add(t,this._parseTimeOrLabel(null,e,!0,t),i,s)},m.addLabel=function(t,e){return this._labels[t]=this._parseTimeOrLabel(e),this},m.addPause=function(t,e,s,r){var n=i.delayedCall(0,p,s,r||this);return n.vars.onComplete=n.vars.onReverseComplete=e,n.data="isPause",this._hasPause=!0,this.add(n,t)},m.removeLabel=function(t){return delete this._labels[t],this},m.getLabelTime=function(t){return null!=this._labels[t]?this._labels[t]:-1},m._parseTimeOrLabel=function(e,i,s,r){var n,a;if(r instanceof t&&r.timeline===this)this.remove(r);else if(r&&(r instanceof Array||r.push&&l(r)))for(a=r.length;--a>-1;)r[a]instanceof t&&r[a].timeline===this&&this.remove(r[a]);if(n="number"!=typeof e||i?this.duration()>99999999999?this.recent().endTime(!1):this._duration:0,"string"==typeof i)return this._parseTimeOrLabel(i,s&&"number"==typeof e&&null==this._labels[i]?e-n:0,s);if(i=i||0,"string"!=typeof e||!isNaN(e)&&null==this._labels[e])null==e&&(e=n);else{if(-1===(a=e.indexOf("=")))return null==this._labels[e]?s?this._labels[e]=n+i:i:this._labels[e]+i;i=parseInt(e.charAt(a-1)+"1",10)*Number(e.substr(a+1)),e=a>1?this._parseTimeOrLabel(e.substr(0,a-1),0,s):n}return Number(e)+i},m.seek=function(t,e){return this.totalTime("number"==typeof t?t:this._parseTimeOrLabel(t),!1!==e)},m.stop=function(){return this.paused(!0)},m.gotoAndPlay=function(t,e){return this.play(t,e)},m.gotoAndStop=function(t,e){return this.pause(t,e)},m.render=function(t,e,i){this._gc&&this._enabled(!0,!1);var s,n,a,o,l,u,f,c=this._time,p=this._dirty?this.totalDuration():this._totalDuration,d=this._startTime,m=this._timeScale,g=this._paused;if(c!==this._time&&(t+=this._time-c),t>=p-1e-7&&t>=0)this._totalTime=this._time=p,this._reversed||this._hasPausedChild()||(n=!0,o="onComplete",l=!!this._timeline.autoRemoveChildren,0===this._duration&&(0>=t&&t>=-1e-7||this._rawPrevTime<0||this._rawPrevTime===r)&&this._rawPrevTime!==t&&this._first&&(l=!0,this._rawPrevTime>r&&(o="onReverseComplete"))),this._rawPrevTime=this._duration||!e||t||this._rawPrevTime===t?t:r,t=p+1e-4;else if(1e-7>t)if(this._totalTime=this._time=0,(0!==c||0===this._duration&&this._rawPrevTime!==r&&(this._rawPrevTime>0||0>t&&this._rawPrevTime>=0))&&(o="onReverseComplete",n=this._reversed),0>t)this._active=!1,this._timeline.autoRemoveChildren&&this._reversed?(l=n=!0,o="onReverseComplete"):this._rawPrevTime>=0&&this._first&&(l=!0),this._rawPrevTime=t;else{if(this._rawPrevTime=this._duration||!e||t||this._rawPrevTime===t?t:r,0===t&&n)for(s=this._first;s&&0===s._startTime;)s._duration||(n=!1),s=s._next;t=0,this._initted||(l=!0)}else{if(this._hasPause&&!this._forcingPlayhead&&!e){if(t>=c)for(s=this._first;s&&s._startTime<=t&&!u;)s._duration||"isPause"!==s.data||s.ratio||0===s._startTime&&0===this._rawPrevTime||(u=s),s=s._next;else for(s=this._last;s&&s._startTime>=t&&!u;)s._duration||"isPause"===s.data&&s._rawPrevTime>0&&(u=s),s=s._prev;u&&(this._time=t=u._startTime,this._totalTime=t+this._cycle*(this._totalDuration+this._repeatDelay))}this._totalTime=this._time=this._rawPrevTime=t}if(this._time!==c&&this._first||i||l||u){if(this._initted||(this._initted=!0),this._active||!this._paused&&this._time!==c&&t>0&&(this._active=!0),0===c&&this.vars.onStart&&(0===this._time&&this._duration||e||this._callback("onStart")),(f=this._time)>=c)for(s=this._first;s&&(a=s._next,f===this._time&&(!this._paused||g));)(s._active||s._startTime<=f&&!s._paused&&!s._gc)&&(u===s&&this.pause(),s._reversed?s.render((s._dirty?s.totalDuration():s._totalDuration)-(t-s._startTime)*s._timeScale,e,i):s.render((t-s._startTime)*s._timeScale,e,i)),s=a;else for(s=this._last;s&&(a=s._prev,f===this._time&&(!this._paused||g));){if(s._active||s._startTime<=c&&!s._paused&&!s._gc){if(u===s){for(u=s._prev;u&&u.endTime()>this._time;)u.render(u._reversed?u.totalDuration()-(t-u._startTime)*u._timeScale:(t-u._startTime)*u._timeScale,e,i),u=u._prev;u=null,this.pause()}s._reversed?s.render((s._dirty?s.totalDuration():s._totalDuration)-(t-s._startTime)*s._timeScale,e,i):s.render((t-s._startTime)*s._timeScale,e,i)}s=a}this._onUpdate&&(e||(h.length&&_(),this._callback("onUpdate"))),o&&(this._gc||(d===this._startTime||m!==this._timeScale)&&(0===this._time||p>=this.totalDuration())&&(n&&(h.length&&_(),this._timeline.autoRemoveChildren&&this._enabled(!1,!1),this._active=!1),!e&&this.vars[o]&&this._callback(o)))}},m._hasPausedChild=function(){for(var t=this._first;t;){if(t._paused||t instanceof s&&t._hasPausedChild())return!0;t=t._next}return!1},m.getChildren=function(t,e,s,r){r=r||-9999999999;for(var n=[],a=this._first,o=0;a;)a._startTime<r||(a instanceof i?!1!==e&&(n[o++]=a):(!1!==s&&(n[o++]=a),!1!==t&&(o=(n=n.concat(a.getChildren(!0,e,s))).length))),a=a._next;return n},m.getTweensOf=function(t,e){var s,r,n=this._gc,a=[],o=0;for(n&&this._enabled(!0,!0),r=(s=i.getTweensOf(t)).length;--r>-1;)(s[r].timeline===this||e&&this._contains(s[r]))&&(a[o++]=s[r]);return n&&this._enabled(!1,!0),a},m.recent=function(){return this._recent},m._contains=function(t){for(var e=t.timeline;e;){if(e===this)return!0;e=e.timeline}return!1},m.shiftChildren=function(t,e,i){i=i||0;for(var s,r=this._first,n=this._labels;r;)r._startTime>=i&&(r._startTime+=t),r=r._next;if(e)for(s in n)n[s]>=i&&(n[s]+=t);return this._uncache(!0)},m._kill=function(t,e){if(!t&&!e)return this._enabled(!1,!1);for(var i=e?this.getTweensOf(e):this.getChildren(!0,!0,!1),s=i.length,r=!1;--s>-1;)i[s]._kill(t,e)&&(r=!0);return r},m.clear=function(t){var e=this.getChildren(!1,!0,!0),i=e.length;for(this._time=this._totalTime=0;--i>-1;)e[i]._enabled(!1,!1);return!1!==t&&(this._labels={}),this._uncache(!0)},m.invalidate=function(){for(var e=this._first;e;)e.invalidate(),e=e._next;return t.prototype.invalidate.call(this)},m._enabled=function(t,i){if(t===this._gc)for(var s=this._first;s;)s._enabled(t,!0),s=s._next;return e.prototype._enabled.call(this,t,i)},m.totalTime=function(e,i,s){this._forcingPlayhead=!0;var r=t.prototype.totalTime.apply(this,arguments);return this._forcingPlayhead=!1,r},m.duration=function(t){return arguments.length?(0!==this.duration()&&0!==t&&this.timeScale(this._duration/t),this):(this._dirty&&this.totalDuration(),this._duration)},m.totalDuration=function(t){if(!arguments.length){if(this._dirty){for(var e,i,s=0,r=this._last,n=999999999999;r;)e=r._prev,r._dirty&&r.totalDuration(),r._startTime>n&&this._sortChildren&&!r._paused&&!this._calculatingDuration?(this._calculatingDuration=1,this.add(r,r._startTime-r._delay),this._calculatingDuration=0):n=r._startTime,r._startTime<0&&!r._paused&&(s-=r._startTime,this._timeline.smoothChildTiming&&(this._startTime+=r._startTime/this._timeScale,this._time-=r._startTime,this._totalTime-=r._startTime,this._rawPrevTime-=r._startTime),this.shiftChildren(-r._startTime,!1,-9999999999),n=0),(i=r._startTime+r._totalDuration/r._timeScale)>s&&(s=i),r=e;this._duration=this._totalDuration=s,this._dirty=!1}return this._totalDuration}return t&&this.totalDuration()?this.timeScale(this._totalDuration/t):this},m.paused=function(e){if(!e)for(var i=this._first,s=this._time;i;)i._startTime===s&&"isPause"===i.data&&(i._rawPrevTime=0),i=i._next;return t.prototype.paused.apply(this,arguments)},m.usesFrames=function(){for(var e=this._timeline;e._timeline;)e=e._timeline;return e===t._rootFramesTimeline},m.rawTime=function(t){return t&&(this._paused||this._repeat&&this.time()>0&&this.totalProgress()<1)?this._totalTime%(this._duration+this._repeatDelay):this._paused?this._totalTime:(this._timeline.rawTime(t)-this._startTime)*this._timeScale},s},!0),_gsScope._gsDefine("TimelineMax",["TimelineLite","TweenLite","easing.Ease"],function(t,e,i){var s=function(e){t.call(this,e),this._repeat=this.vars.repeat||0,this._repeatDelay=this.vars.repeatDelay||0,this._cycle=0,this._yoyo=!0===this.vars.yoyo,this._dirty=!0},r=1e-10,n=e._internals,a=n.lazyTweens,o=n.lazyRender,l=_gsScope._gsDefine.globals,h=new i(null,null,1,0),_=s.prototype=new t;return _.constructor=s,_.kill()._gc=!1,s.version="1.20.3",_.invalidate=function(){return this._yoyo=!0===this.vars.yoyo,this._repeat=this.vars.repeat||0,this._repeatDelay=this.vars.repeatDelay||0,this._uncache(!0),t.prototype.invalidate.call(this)},_.addCallback=function(t,i,s,r){return this.add(e.delayedCall(0,t,s,r),i)},_.removeCallback=function(t,e){if(t)if(null==e)this._kill(null,t);else for(var i=this.getTweensOf(t,!1),s=i.length,r=this._parseTimeOrLabel(e);--s>-1;)i[s]._startTime===r&&i[s]._enabled(!1,!1);return this},_.removePause=function(e){return this.removeCallback(t._internals.pauseCallback,e)},_.tweenTo=function(t,i){i=i||{};var s,r,n,a={ease:h,useFrames:this.usesFrames(),immediateRender:!1},o=i.repeat&&l.TweenMax||e;for(r in i)a[r]=i[r];return a.time=this._parseTimeOrLabel(t),s=Math.abs(Number(a.time)-this._time)/this._timeScale||.001,n=new o(this,s,a),a.onStart=function(){n.target.paused(!0),n.vars.time!==n.target.time()&&s===n.duration()&&n.duration(Math.abs(n.vars.time-n.target.time())/n.target._timeScale),i.onStart&&i.onStart.apply(i.onStartScope||i.callbackScope||n,i.onStartParams||[])},n},_.tweenFromTo=function(t,e,i){i=i||{},t=this._parseTimeOrLabel(t),i.startAt={onComplete:this.seek,onCompleteParams:[t],callbackScope:this},i.immediateRender=!1!==i.immediateRender;var s=this.tweenTo(e,i);return s.duration(Math.abs(s.vars.time-t)/this._timeScale||.001)},_.render=function(t,e,i){this._gc&&this._enabled(!0,!1);var s,n,l,h,_,u,f,c,p=this._time,d=this._dirty?this.totalDuration():this._totalDuration,m=this._duration,g=this._totalTime,y=this._startTime,v=this._timeScale,T=this._rawPrevTime,x=this._paused,b=this._cycle;if(p!==this._time&&(t+=this._time-p),t>=d-1e-7&&t>=0)this._locked||(this._totalTime=d,this._cycle=this._repeat),this._reversed||this._hasPausedChild()||(n=!0,h="onComplete",_=!!this._timeline.autoRemoveChildren,0===this._duration&&(0>=t&&t>=-1e-7||0>T||T===r)&&T!==t&&this._first&&(_=!0,T>r&&(h="onReverseComplete"))),this._rawPrevTime=this._duration||!e||t||this._rawPrevTime===t?t:r,this._yoyo&&1&this._cycle?this._time=t=0:(this._time=m,t=m+1e-4);else if(1e-7>t)if(this._locked||(this._totalTime=this._cycle=0),this._time=0,(0!==p||0===m&&T!==r&&(T>0||0>t&&T>=0)&&!this._locked)&&(h="onReverseComplete",n=this._reversed),0>t)this._active=!1,this._timeline.autoRemoveChildren&&this._reversed?(_=n=!0,h="onReverseComplete"):T>=0&&this._first&&(_=!0),this._rawPrevTime=t;else{if(this._rawPrevTime=m||!e||t||this._rawPrevTime===t?t:r,0===t&&n)for(s=this._first;s&&0===s._startTime;)s._duration||(n=!1),s=s._next;t=0,this._initted||(_=!0)}else if(0===m&&0>T&&(_=!0),this._time=this._rawPrevTime=t,this._locked||(this._totalTime=t,0!==this._repeat&&(u=m+this._repeatDelay,this._cycle=this._totalTime/u|0,0!==this._cycle&&this._cycle===this._totalTime/u&&t>=g&&this._cycle--,this._time=this._totalTime-this._cycle*u,this._yoyo&&!!(1&this._cycle)&&(this._time=m-this._time),this._time>m?(this._time=m,t=m+1e-4):this._time<0?this._time=t=0:t=this._time)),this._hasPause&&!this._forcingPlayhead&&!e){if((t=this._time)>=p||this._repeat&&b!==this._cycle)for(s=this._first;s&&s._startTime<=t&&!f;)s._duration||"isPause"!==s.data||s.ratio||0===s._startTime&&0===this._rawPrevTime||(f=s),s=s._next;else for(s=this._last;s&&s._startTime>=t&&!f;)s._duration||"isPause"===s.data&&s._rawPrevTime>0&&(f=s),s=s._prev;f&&f._startTime<m&&(this._time=t=f._startTime,this._totalTime=t+this._cycle*(this._totalDuration+this._repeatDelay))}if(this._cycle!==b&&!this._locked){var w=this._yoyo&&!!(1&b),P=w===(this._yoyo&&!!(1&this._cycle)),O=this._totalTime,S=this._cycle,k=this._rawPrevTime,R=this._time;if(this._totalTime=b*m,this._cycle<b?w=!w:this._totalTime+=m,this._time=p,this._rawPrevTime=0===m?T-1e-4:T,this._cycle=b,this._locked=!0,p=w?0:m,this.render(p,e,0===m),e||this._gc||this.vars.onRepeat&&(this._cycle=S,this._locked=!1,this._callback("onRepeat")),p!==this._time)return;if(P&&(this._cycle=b,this._locked=!0,p=w?m+1e-4:-1e-4,this.render(p,!0,!1)),this._locked=!1,this._paused&&!x)return;this._time=R,this._totalTime=O,this._cycle=S,this._rawPrevTime=k}if(this._time!==p&&this._first||i||_||f){if(this._initted||(this._initted=!0),this._active||!this._paused&&this._totalTime!==g&&t>0&&(this._active=!0),0===g&&this.vars.onStart&&(0===this._totalTime&&this._totalDuration||e||this._callback("onStart")),(c=this._time)>=p)for(s=this._first;s&&(l=s._next,c===this._time&&(!this._paused||x));)(s._active||s._startTime<=this._time&&!s._paused&&!s._gc)&&(f===s&&this.pause(),s._reversed?s.render((s._dirty?s.totalDuration():s._totalDuration)-(t-s._startTime)*s._timeScale,e,i):s.render((t-s._startTime)*s._timeScale,e,i)),s=l;else for(s=this._last;s&&(l=s._prev,c===this._time&&(!this._paused||x));){if(s._active||s._startTime<=p&&!s._paused&&!s._gc){if(f===s){for(f=s._prev;f&&f.endTime()>this._time;)f.render(f._reversed?f.totalDuration()-(t-f._startTime)*f._timeScale:(t-f._startTime)*f._timeScale,e,i),f=f._prev;f=null,this.pause()}s._reversed?s.render((s._dirty?s.totalDuration():s._totalDuration)-(t-s._startTime)*s._timeScale,e,i):s.render((t-s._startTime)*s._timeScale,e,i)}s=l}this._onUpdate&&(e||(a.length&&o(),this._callback("onUpdate"))),h&&(this._locked||this._gc||(y===this._startTime||v!==this._timeScale)&&(0===this._time||d>=this.totalDuration())&&(n&&(a.length&&o(),this._timeline.autoRemoveChildren&&this._enabled(!1,!1),this._active=!1),!e&&this.vars[h]&&this._callback(h)))}else g!==this._totalTime&&this._onUpdate&&(e||this._callback("onUpdate"))},_.getActive=function(t,e,i){null==t&&(t=!0),null==e&&(e=!0),null==i&&(i=!1);var s,r,n=[],a=this.getChildren(t,e,i),o=0,l=a.length;for(s=0;l>s;s++)(r=a[s]).isActive()&&(n[o++]=r);return n},_.getLabelAfter=function(t){t||0!==t&&(t=this._time);var e,i=this.getLabelsArray(),s=i.length;for(e=0;s>e;e++)if(i[e].time>t)return i[e].name;return null},_.getLabelBefore=function(t){null==t&&(t=this._time);for(var e=this.getLabelsArray(),i=e.length;--i>-1;)if(e[i].time<t)return e[i].name;return null},_.getLabelsArray=function(){var t,e=[],i=0;for(t in this._labels)e[i++]={time:this._labels[t],name:t};return e.sort(function(t,e){return t.time-e.time}),e},_.invalidate=function(){return this._locked=!1,t.prototype.invalidate.call(this)},_.progress=function(t,e){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&1&this._cycle?1-t:t)+this._cycle*(this._duration+this._repeatDelay),e):this._time/this.duration()||0},_.totalProgress=function(t,e){return arguments.length?this.totalTime(this.totalDuration()*t,e):this._totalTime/this.totalDuration()||0},_.totalDuration=function(e){return arguments.length?-1!==this._repeat&&e?this.timeScale(this.totalDuration()/e):this:(this._dirty&&(t.prototype.totalDuration.call(this),this._totalDuration=-1===this._repeat?999999999999:this._duration*(this._repeat+1)+this._repeatDelay*this._repeat),this._totalDuration)},_.time=function(t,e){return arguments.length?(this._dirty&&this.totalDuration(),t>this._duration&&(t=this._duration),this._yoyo&&1&this._cycle?t=this._duration-t+this._cycle*(this._duration+this._repeatDelay):0!==this._repeat&&(t+=this._cycle*(this._duration+this._repeatDelay)),this.totalTime(t,e)):this._time},_.repeat=function(t){return arguments.length?(this._repeat=t,this._uncache(!0)):this._repeat},_.repeatDelay=function(t){return arguments.length?(this._repeatDelay=t,this._uncache(!0)):this._repeatDelay},_.yoyo=function(t){return arguments.length?(this._yoyo=t,this):this._yoyo},_.currentLabel=function(t){return arguments.length?this.seek(t,!0):this.getLabelBefore(this._time+1e-8)},s},!0),t=180/Math.PI,e=[],i=[],s=[],r={},n=_gsScope._gsDefine.globals,a=function(t,e,i,s){i===s&&(i=s-(s-e)/1e6),t===e&&(e=t+(i-t)/1e6),this.a=t,this.b=e,this.c=i,this.d=s,this.da=s-t,this.ca=i-t,this.ba=e-t},o=function(t,e,i,s){var r={a:t},n={},a={},o={c:s},l=(t+e)/2,h=(e+i)/2,_=(i+s)/2,u=(l+h)/2,f=(h+_)/2,c=(f-u)/8;return r.b=l+(t-l)/4,n.b=u+c,r.c=n.a=(r.b+n.b)/2,n.c=a.a=(u+f)/2,a.b=f-c,o.b=_+(s-_)/4,a.c=o.a=(a.b+o.b)/2,[r,n,a,o]},l=function(t,r,n,a,l){var h,_,u,f,c,p,d,m,g,y,v,T,x,b=t.length-1,w=0,P=t[0].a;for(h=0;b>h;h++)_=(c=t[w]).a,u=c.d,f=t[w+1].d,l?(v=e[h],x=((T=i[h])+v)*r*.25/(a?.5:s[h]||.5),m=u-((p=u-(u-_)*(a?.5*r:0!==v?x/v:0))+(((d=u+(f-u)*(a?.5*r:0!==T?x/T:0))-p)*(3*v/(v+T)+.5)/4||0))):m=u-((p=u-(u-_)*r*.5)+(d=u+(f-u)*r*.5))/2,p+=m,d+=m,c.c=g=p,c.b=0!==h?P:P=c.a+.6*(c.c-c.a),c.da=u-_,c.ca=g-_,c.ba=P-_,n?(y=o(_,P,g,u),t.splice(w,1,y[0],y[1],y[2],y[3]),w+=4):w++,P=d;(c=t[w]).b=P,c.c=P+.4*(c.d-P),c.da=c.d-c.a,c.ca=c.c-c.a,c.ba=P-c.a,n&&(y=o(c.a,P,c.c,c.d),t.splice(w,1,y[0],y[1],y[2],y[3]))},h=function(t,s,r,n){var o,l,h,_,u,f,c=[];if(n)for(l=(t=[n].concat(t)).length;--l>-1;)"string"==typeof(f=t[l][s])&&"="===f.charAt(1)&&(t[l][s]=n[s]+Number(f.charAt(0)+f.substr(2)));if(0>(o=t.length-2))return c[0]=new a(t[0][s],0,0,t[0][s]),c;for(l=0;o>l;l++)h=t[l][s],_=t[l+1][s],c[l]=new a(h,0,0,_),r&&(u=t[l+2][s],e[l]=(e[l]||0)+(_-h)*(_-h),i[l]=(i[l]||0)+(u-_)*(u-_));return c[l]=new a(t[l][s],0,0,t[l+1][s]),c},_=function(t,n,a,o,_,u){var f,c,p,d,m,g,y,v,T={},x=[],b=u||t[0];for(c in _="string"==typeof _?","+_+",":",x,y,z,left,top,right,bottom,marginTop,marginLeft,marginRight,marginBottom,paddingLeft,paddingTop,paddingRight,paddingBottom,backgroundPosition,backgroundPosition_y,",null==n&&(n=1),t[0])x.push(c);if(t.length>1){for(v=t[t.length-1],y=!0,f=x.length;--f>-1;)if(c=x[f],Math.abs(b[c]-v[c])>.05){y=!1;break}y&&(t=t.concat(),u&&t.unshift(u),t.push(t[1]),u=t[t.length-3])}for(e.length=i.length=s.length=0,f=x.length;--f>-1;)c=x[f],r[c]=-1!==_.indexOf(","+c+","),T[c]=h(t,c,r[c],u);for(f=e.length;--f>-1;)e[f]=Math.sqrt(e[f]),i[f]=Math.sqrt(i[f]);if(!o){for(f=x.length;--f>-1;)if(r[c])for(g=(p=T[x[f]]).length-1,d=0;g>d;d++)m=p[d+1].da/i[d]+p[d].da/e[d]||0,s[d]=(s[d]||0)+m*m;for(f=s.length;--f>-1;)s[f]=Math.sqrt(s[f])}for(f=x.length,d=a?4:1;--f>-1;)p=T[c=x[f]],l(p,n,a,o,r[c]),y&&(p.splice(0,d),p.splice(p.length-d,d));return T},u=function(t,e,i){var s,r,n,o,l,h,_,u,f,c,p,d={},m="cubic"===(e=e||"soft")?3:2,g="soft"===e,y=[];if(g&&i&&(t=[i].concat(t)),null==t||t.length<m+1)throw"invalid Bezier data";for(f in t[0])y.push(f);for(h=y.length;--h>-1;){for(d[f=y[h]]=l=[],c=0,u=t.length,_=0;u>_;_++)s=null==i?t[_][f]:"string"==typeof(p=t[_][f])&&"="===p.charAt(1)?i[f]+Number(p.charAt(0)+p.substr(2)):Number(p),g&&_>1&&u-1>_&&(l[c++]=(s+l[c-2])/2),l[c++]=s;for(u=c-m+1,c=0,_=0;u>_;_+=m)s=l[_],r=l[_+1],n=l[_+2],o=2===m?0:l[_+3],l[c++]=p=3===m?new a(s,r,n,o):new a(s,(2*r+s)/3,(2*r+n)/3,n);l.length=c}return d},f=function(t,e,i){for(var s,r,n,a,o,l,h,_,u,f,c,p=1/i,d=t.length;--d>-1;)for(n=(f=t[d]).a,a=f.d-n,o=f.c-n,l=f.b-n,s=r=0,_=1;i>=_;_++)s=r-(r=((h=p*_)*h*a+3*(u=1-h)*(h*o+u*l))*h),e[c=d*i+_-1]=(e[c]||0)+s*s},c=function(t,e){var i,s,r,n,a=[],o=[],l=0,h=0,_=(e=0|e||6)-1,u=[],c=[];for(i in t)f(t[i],a,e);for(r=a.length,s=0;r>s;s++)l+=Math.sqrt(a[s]),c[n=s%e]=l,n===_&&(h+=l,u[n=s/e|0]=c,o[n]=h,l=0,c=[]);return{length:h,lengths:o,segments:u}},p=_gsScope._gsDefine.plugin({propName:"bezier",priority:-1,version:"1.3.8",API:2,global:!0,init:function(t,e,i){this._target=t,e instanceof Array&&(e={values:e}),this._func={},this._mod={},this._props=[],this._timeRes=null==e.timeResolution?6:parseInt(e.timeResolution,10);var s,r,n,a,o,l=e.values||[],h={},f=l[0],p=e.autoRotate||i.vars.orientToBezier;for(s in this._autoRotate=p?p instanceof Array?p:[["x","y","rotation",!0===p?0:Number(p)||0]]:null,f)this._props.push(s);for(n=this._props.length;--n>-1;)s=this._props[n],this._overwriteProps.push(s),r=this._func[s]="function"==typeof t[s],h[s]=r?t[s.indexOf("set")||"function"!=typeof t["get"+s.substr(3)]?s:"get"+s.substr(3)]():parseFloat(t[s]),o||h[s]!==l[0][s]&&(o=h);if(this._beziers="cubic"!==e.type&&"quadratic"!==e.type&&"soft"!==e.type?_(l,isNaN(e.curviness)?1:e.curviness,!1,"thruBasic"===e.type,e.correlate,o):u(l,e.type,h),this._segCount=this._beziers[s].length,this._timeRes){var d=c(this._beziers,this._timeRes);this._length=d.length,this._lengths=d.lengths,this._segments=d.segments,this._l1=this._li=this._s1=this._si=0,this._l2=this._lengths[0],this._curSeg=this._segments[0],this._s2=this._curSeg[0],this._prec=1/this._curSeg.length}if(p=this._autoRotate)for(this._initialRotations=[],p[0]instanceof Array||(this._autoRotate=p=[p]),n=p.length;--n>-1;){for(a=0;3>a;a++)s=p[n][a],this._func[s]="function"==typeof t[s]&&t[s.indexOf("set")||"function"!=typeof t["get"+s.substr(3)]?s:"get"+s.substr(3)];s=p[n][2],this._initialRotations[n]=(this._func[s]?this._func[s].call(this._target):this._target[s])||0,this._overwriteProps.push(s)}return this._startRatio=i.vars.runBackwards?1:0,!0},set:function(e){var i,s,r,n,a,o,l,h,_,u,f=this._segCount,c=this._func,p=this._target,d=e!==this._startRatio;if(this._timeRes){if(_=this._lengths,u=this._curSeg,e*=this._length,r=this._li,e>this._l2&&f-1>r){for(h=f-1;h>r&&(this._l2=_[++r])<=e;);this._l1=_[r-1],this._li=r,this._curSeg=u=this._segments[r],this._s2=u[this._s1=this._si=0]}else if(e<this._l1&&r>0){for(;r>0&&(this._l1=_[--r])>=e;);0===r&&e<this._l1?this._l1=0:r++,this._l2=_[r],this._li=r,this._curSeg=u=this._segments[r],this._s1=u[(this._si=u.length-1)-1]||0,this._s2=u[this._si]}if(i=r,e-=this._l1,r=this._si,e>this._s2&&r<u.length-1){for(h=u.length-1;h>r&&(this._s2=u[++r])<=e;);this._s1=u[r-1],this._si=r}else if(e<this._s1&&r>0){for(;r>0&&(this._s1=u[--r])>=e;);0===r&&e<this._s1?this._s1=0:r++,this._s2=u[r],this._si=r}o=(r+(e-this._s1)/(this._s2-this._s1))*this._prec||0}else o=(e-(i=0>e?0:e>=1?f-1:f*e|0)*(1/f))*f;for(s=1-o,r=this._props.length;--r>-1;)n=this._props[r],l=(o*o*(a=this._beziers[n][i]).da+3*s*(o*a.ca+s*a.ba))*o+a.a,this._mod[n]&&(l=this._mod[n](l,p)),c[n]?p[n](l):p[n]=l;if(this._autoRotate){var m,g,y,v,T,x,b,w=this._autoRotate;for(r=w.length;--r>-1;)n=w[r][2],x=w[r][3]||0,b=!0===w[r][4]?1:t,a=this._beziers[w[r][0]],m=this._beziers[w[r][1]],a&&m&&(a=a[i],m=m[i],g=a.a+(a.b-a.a)*o,g+=((v=a.b+(a.c-a.b)*o)-g)*o,v+=(a.c+(a.d-a.c)*o-v)*o,y=m.a+(m.b-m.a)*o,y+=((T=m.b+(m.c-m.b)*o)-y)*o,T+=(m.c+(m.d-m.c)*o-T)*o,l=d?Math.atan2(T-y,v-g)*b+x:this._initialRotations[r],this._mod[n]&&(l=this._mod[n](l,p)),c[n]?p[n](l):p[n]=l)}}}),d=p.prototype,p.bezierThrough=_,p.cubicToQuadratic=o,p._autoCSS=!0,p.quadraticToCubic=function(t,e,i){return new a(t,(2*e+t)/3,(2*e+i)/3,i)},p._cssRegister=function(){var t=n.CSSPlugin;if(t){var e=t._internals,i=e._parseToProxy,s=e._setPluginRatio,r=e.CSSPropTween;e._registerComplexSpecialProp("bezier",{parser:function(t,e,n,a,o,l){e instanceof Array&&(e={values:e}),l=new p;var h,_,u,f=e.values,c=f.length-1,d=[],m={};if(0>c)return o;for(h=0;c>=h;h++)u=i(t,f[h],a,o,l,c!==h),d[h]=u.end;for(_ in e)m[_]=e[_];return m.values=d,(o=new r(t,"bezier",0,0,u.pt,2)).data=u,o.plugin=l,o.setRatio=s,0===m.autoRotate&&(m.autoRotate=!0),!m.autoRotate||m.autoRotate instanceof Array||(h=!0===m.autoRotate?0:Number(m.autoRotate),m.autoRotate=null!=u.end.left?[["left","top","rotation",h,!1]]:null!=u.end.x&&[["x","y","rotation",h,!1]]),m.autoRotate&&(a._transform||a._enableTransforms(!1),u.autoRotate=a._target._gsTransform,u.proxy.rotation=u.autoRotate.rotation||0,a._overwriteProps.push("rotation")),l._onInitTween(u.proxy,m,a._tween),o}})}},d._mod=function(t){for(var e,i=this._overwriteProps,s=i.length;--s>-1;)(e=t[i[s]])&&"function"==typeof e&&(this._mod[i[s]]=e)},d._kill=function(t){var e,i,s=this._props;for(e in this._beziers)if(e in t)for(delete this._beziers[e],delete this._func[e],i=s.length;--i>-1;)s[i]===e&&s.splice(i,1);if(s=this._autoRotate)for(i=s.length;--i>-1;)t[s[i][2]]&&s.splice(i,1);return this._super._kill.call(this,t)},_gsScope._gsDefine("plugins.CSSPlugin",["plugins.TweenPlugin","TweenLite"],function(t,e){var i,s,r,n,a=function(){t.call(this,"css"),this._overwriteProps.length=0,this.setRatio=a.prototype.setRatio},o=_gsScope._gsDefine.globals,l={},h=a.prototype=new t("css");h.constructor=a,a.version="1.20.3",a.API=2,a.defaultTransformPerspective=0,a.defaultSkewType="compensated",a.defaultSmoothOrigin=!0,h="px",a.suffixMap={top:h,right:h,bottom:h,left:h,width:h,height:h,fontSize:h,padding:h,margin:h,perspective:h,lineHeight:""};var _,u,f,c,p,d,m,g,y=/(?:\-|\.|\b)(\d|\.|e\-)+/g,v=/(?:\d|\-\d|\.\d|\-\.\d|\+=\d|\-=\d|\+=.\d|\-=\.\d)+/g,T=/(?:\+=|\-=|\-|\b)[\d\-\.]+[a-zA-Z0-9]*(?:%|\b)/gi,x=/(?![+-]?\d*\.?\d+|[+-]|e[+-]\d+)[^0-9]/g,b=/(?:\d|\-|\+|=|#|\.)*/g,w=/opacity *= *([^)]*)/i,P=/opacity:([^;]*)/i,O=/alpha\(opacity *=.+?\)/i,S=/^(rgb|hsl)/,k=/([A-Z])/g,R=/-([a-z])/gi,A=/(^(?:url\(\"|url\())|(?:(\"\))$|\)$)/gi,C=function(t,e){return e.toUpperCase()},D=/(?:Left|Right|Width)/i,M=/(M11|M12|M21|M22)=[\d\-\.e]+/gi,F=/progid\:DXImageTransform\.Microsoft\.Matrix\(.+?\)/i,z=/,(?=[^\)]*(?:\(|$))/gi,E=/[\s,\(]/i,I=Math.PI/180,X=180/Math.PI,N={},L={style:{}},B=_gsScope.document||{createElement:function(){return L}},Y=function(t,e){return B.createElementNS?B.createElementNS(e||"http://www.w3.org/1999/xhtml",t):B.createElement(t)},j=Y("div"),U=Y("img"),V=a._internals={_specialProps:l},q=(_gsScope.navigator||{}).userAgent||"",W=function(){var t=q.indexOf("Android"),e=Y("a");return f=-1!==q.indexOf("Safari")&&-1===q.indexOf("Chrome")&&(-1===t||parseFloat(q.substr(t+8,2))>3),p=f&&parseFloat(q.substr(q.indexOf("Version/")+8,2))<6,c=-1!==q.indexOf("Firefox"),(/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(q)||/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(q))&&(d=parseFloat(RegExp.$1)),!!e&&(e.style.cssText="top:1px;opacity:.55;",/^0.55/.test(e.style.opacity))}(),G=function(t){return w.test("string"==typeof t?t:(t.currentStyle?t.currentStyle.filter:t.style.filter)||"")?parseFloat(RegExp.$1)/100:1},Z=function(t){_gsScope.console&&console.log(t)},H="",$="",Q=function(t,e){var i,s,r=(e=e||j).style;if(void 0!==r[t])return t;for(t=t.charAt(0).toUpperCase()+t.substr(1),i=["O","Moz","ms","Ms","Webkit"],s=5;--s>-1&&void 0===r[i[s]+t];);return s>=0?(H="-"+($=3===s?"ms":i[s]).toLowerCase()+"-",$+t):null},K=B.defaultView?B.defaultView.getComputedStyle:function(){},J=a.getStyle=function(t,e,i,s,r){var n;return W||"opacity"!==e?(!s&&t.style[e]?n=t.style[e]:(i=i||K(t))?n=i[e]||i.getPropertyValue(e)||i.getPropertyValue(e.replace(k,"-$1").toLowerCase()):t.currentStyle&&(n=t.currentStyle[e]),null==r||n&&"none"!==n&&"auto"!==n&&"auto auto"!==n?n:r):G(t)},tt=V.convertToPixels=function(t,i,s,r,n){if("px"===r||!r&&"lineHeight"!==i)return s;if("auto"===r||!s)return 0;var o,l,h,_=D.test(i),u=t,f=j.style,c=0>s,p=1===s;if(c&&(s=-s),p&&(s*=100),"lineHeight"!==i||r)if("%"===r&&-1!==i.indexOf("border"))o=s/100*(_?t.clientWidth:t.clientHeight);else{if(f.cssText="border:0 solid red;position:"+J(t,"position")+";line-height:0;","%"!==r&&u.appendChild&&"v"!==r.charAt(0)&&"rem"!==r)f[_?"borderLeftWidth":"borderTopWidth"]=s+r;else{if(u=t.parentNode||B.body,-1!==J(u,"display").indexOf("flex")&&(f.position="absolute"),l=u._gsCache,h=e.ticker.frame,l&&_&&l.time===h)return l.width*s/100;f[_?"width":"height"]=s+r}u.appendChild(j),o=parseFloat(j[_?"offsetWidth":"offsetHeight"]),u.removeChild(j),_&&"%"===r&&!1!==a.cacheWidths&&((l=u._gsCache=u._gsCache||{}).time=h,l.width=o/s*100),0!==o||n||(o=tt(t,i,s,r,!0))}else l=K(t).lineHeight,t.style.lineHeight=s,o=parseFloat(K(t).lineHeight),t.style.lineHeight=l;return p&&(o/=100),c?-o:o},et=V.calculateOffset=function(t,e,i){if("absolute"!==J(t,"position",i))return 0;var s="left"===e?"Left":"Top",r=J(t,"margin"+s,i);return t["offset"+s]-(tt(t,e,parseFloat(r),r.replace(b,""))||0)},it=function(t,e){var i,s,r,n={};if(e=e||K(t,null))if(i=e.length)for(;--i>-1;)(-1===(r=e[i]).indexOf("-transform")||At===r)&&(n[r.replace(R,C)]=e.getPropertyValue(r));else for(i in e)(-1===i.indexOf("Transform")||Rt===i)&&(n[i]=e[i]);else if(e=t.currentStyle||t.style)for(i in e)"string"==typeof i&&void 0===n[i]&&(n[i.replace(R,C)]=e[i]);return W||(n.opacity=G(t)),s=jt(t,e,!1),n.rotation=s.rotation,n.skewX=s.skewX,n.scaleX=s.scaleX,n.scaleY=s.scaleY,n.x=s.x,n.y=s.y,Dt&&(n.z=s.z,n.rotationX=s.rotationX,n.rotationY=s.rotationY,n.scaleZ=s.scaleZ),n.filters&&delete n.filters,n},st=function(t,e,i,s,r){var n,a,o,l={},h=t.style;for(a in i)"cssText"!==a&&"length"!==a&&isNaN(a)&&(e[a]!==(n=i[a])||r&&r[a])&&-1===a.indexOf("Origin")&&("number"==typeof n||"string"==typeof n)&&(l[a]="auto"!==n||"left"!==a&&"top"!==a?""!==n&&"auto"!==n&&"none"!==n||"string"!=typeof e[a]||""===e[a].replace(x,"")?n:0:et(t,a),void 0!==h[a]&&(o=new yt(h,a,h[a],o)));if(s)for(a in s)"className"!==a&&(l[a]=s[a]);return{difs:l,firstMPT:o}},rt={width:["Left","Right"],height:["Top","Bottom"]},nt=["marginLeft","marginRight","marginTop","marginBottom"],at=function(t,e,i){if("svg"===(t.nodeName+"").toLowerCase())return(i||K(t))[e]||0;if(t.getCTM&&Lt(t))return t.getBBox()[e]||0;var s=parseFloat("width"===e?t.offsetWidth:t.offsetHeight),r=rt[e],n=r.length;for(i=i||K(t,null);--n>-1;)s-=parseFloat(J(t,"padding"+r[n],i,!0))||0,s-=parseFloat(J(t,"border"+r[n]+"Width",i,!0))||0;return s},ot=function(t,e){if("contain"===t||"auto"===t||"auto auto"===t)return t+" ";(null==t||""===t)&&(t="0 0");var i,s=t.split(" "),r=-1!==t.indexOf("left")?"0%":-1!==t.indexOf("right")?"100%":s[0],n=-1!==t.indexOf("top")?"0%":-1!==t.indexOf("bottom")?"100%":s[1];if(s.length>3&&!e){for(s=t.split(", ").join(",").split(","),t=[],i=0;i<s.length;i++)t.push(ot(s[i]));return t.join(",")}return null==n?n="center"===r?"50%":"0":"center"===n&&(n="50%"),("center"===r||isNaN(parseFloat(r))&&-1===(r+"").indexOf("="))&&(r="50%"),t=r+" "+n+(s.length>2?" "+s[2]:""),e&&(e.oxp=-1!==r.indexOf("%"),e.oyp=-1!==n.indexOf("%"),e.oxr="="===r.charAt(1),e.oyr="="===n.charAt(1),e.ox=parseFloat(r.replace(x,"")),e.oy=parseFloat(n.replace(x,"")),e.v=t),e||t},lt=function(t,e){return"function"==typeof t&&(t=t(g,m)),"string"==typeof t&&"="===t.charAt(1)?parseInt(t.charAt(0)+"1",10)*parseFloat(t.substr(2)):parseFloat(t)-parseFloat(e)||0},ht=function(t,e){return"function"==typeof t&&(t=t(g,m)),null==t?e:"string"==typeof t&&"="===t.charAt(1)?parseInt(t.charAt(0)+"1",10)*parseFloat(t.substr(2))+e:parseFloat(t)||0},_t=function(t,e,i,s){var r,n,a,o,l,h=1e-6;return"function"==typeof t&&(t=t(g,m)),null==t?o=e:"number"==typeof t?o=t:(r=360,n=t.split("_"),a=((l="="===t.charAt(1))?parseInt(t.charAt(0)+"1",10)*parseFloat(n[0].substr(2)):parseFloat(n[0]))*(-1===t.indexOf("rad")?1:X)-(l?0:e),n.length&&(s&&(s[i]=e+a),-1!==t.indexOf("short")&&(a%=r)!==a%180&&(a=0>a?a+r:a-r),-1!==t.indexOf("_cw")&&0>a?a=(a+9999999999*r)%r-(a/r|0)*r:-1!==t.indexOf("ccw")&&a>0&&(a=(a-9999999999*r)%r-(a/r|0)*r)),o=e+a),h>o&&o>-h&&(o=0),o},ut={aqua:[0,255,255],lime:[0,255,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,255],navy:[0,0,128],white:[255,255,255],fuchsia:[255,0,255],olive:[128,128,0],yellow:[255,255,0],orange:[255,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[255,0,0],pink:[255,192,203],cyan:[0,255,255],transparent:[255,255,255,0]},ft=function(t,e,i){return 255*(1>6*(t=0>t?t+1:t>1?t-1:t)?e+(i-e)*t*6:.5>t?i:2>3*t?e+(i-e)*(2/3-t)*6:e)+.5|0},ct=a.parseColor=function(t,e){var i,s,r,n,a,o,l,h,_,u,f;if(t)if("number"==typeof t)i=[t>>16,t>>8&255,255&t];else{if(","===t.charAt(t.length-1)&&(t=t.substr(0,t.length-1)),ut[t])i=ut[t];else if("#"===t.charAt(0))4===t.length&&(s=t.charAt(1),r=t.charAt(2),n=t.charAt(3),t="#"+s+s+r+r+n+n),i=[(t=parseInt(t.substr(1),16))>>16,t>>8&255,255&t];else if("hsl"===t.substr(0,3))if(i=f=t.match(y),e){if(-1!==t.indexOf("="))return t.match(v)}else a=Number(i[0])%360/360,o=Number(i[1])/100,s=2*(l=Number(i[2])/100)-(r=.5>=l?l*(o+1):l+o-l*o),i.length>3&&(i[3]=Number(i[3])),i[0]=ft(a+1/3,s,r),i[1]=ft(a,s,r),i[2]=ft(a-1/3,s,r);else i=t.match(y)||ut.transparent;i[0]=Number(i[0]),i[1]=Number(i[1]),i[2]=Number(i[2]),i.length>3&&(i[3]=Number(i[3]))}else i=ut.black;return e&&!f&&(s=i[0]/255,r=i[1]/255,n=i[2]/255,l=((h=Math.max(s,r,n))+(_=Math.min(s,r,n)))/2,h===_?a=o=0:(u=h-_,o=l>.5?u/(2-h-_):u/(h+_),a=h===s?(r-n)/u+(n>r?6:0):h===r?(n-s)/u+2:(s-r)/u+4,a*=60),i[0]=a+.5|0,i[1]=100*o+.5|0,i[2]=100*l+.5|0),i},pt=function(t,e){var i,s,r,n=t.match(dt)||[],a=0,o="";if(!n.length)return t;for(i=0;i<n.length;i++)s=n[i],a+=(r=t.substr(a,t.indexOf(s,a)-a)).length+s.length,3===(s=ct(s,e)).length&&s.push(1),o+=r+(e?"hsla("+s[0]+","+s[1]+"%,"+s[2]+"%,"+s[3]:"rgba("+s.join(","))+")";return o+t.substr(a)},dt="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3}){1,2}\\b";for(h in ut)dt+="|"+h+"\\b";dt=new RegExp(dt+")","gi"),a.colorStringFilter=function(t){var e,i=t[0]+" "+t[1];dt.test(i)&&(e=-1!==i.indexOf("hsl(")||-1!==i.indexOf("hsla("),t[0]=pt(t[0],e),t[1]=pt(t[1],e)),dt.lastIndex=0},e.defaultStringFilter||(e.defaultStringFilter=a.colorStringFilter);var mt=function(t,e,i,s){if(null==t)return function(t){return t};var r,n=e?(t.match(dt)||[""])[0]:"",a=t.split(n).join("").match(T)||[],o=t.substr(0,t.indexOf(a[0])),l=")"===t.charAt(t.length-1)?")":"",h=-1!==t.indexOf(" ")?" ":",",_=a.length,u=_>0?a[0].replace(y,""):"";return _?r=e?function(t){var e,f,c,p;if("number"==typeof t)t+=u;else if(s&&z.test(t)){for(p=t.replace(z,"|").split("|"),c=0;c<p.length;c++)p[c]=r(p[c]);return p.join(",")}if(e=(t.match(dt)||[n])[0],c=(f=t.split(e).join("").match(T)||[]).length,_>c--)for(;++c<_;)f[c]=i?f[(c-1)/2|0]:a[c];return o+f.join(h)+h+e+l+(-1!==t.indexOf("inset")?" inset":"")}:function(t){var e,n,f;if("number"==typeof t)t+=u;else if(s&&z.test(t)){for(n=t.replace(z,"|").split("|"),f=0;f<n.length;f++)n[f]=r(n[f]);return n.join(",")}if(f=(e=t.match(T)||[]).length,_>f--)for(;++f<_;)e[f]=i?e[(f-1)/2|0]:a[f];return o+e.join(h)+l}:function(t){return t}},gt=function(t){return t=t.split(","),function(e,i,s,r,n,a,o){var l,h=(i+"").split(" ");for(o={},l=0;4>l;l++)o[t[l]]=h[l]=h[l]||h[(l-1)/2|0];return r.parse(e,o,n,a)}},yt=(V._setPluginRatio=function(t){this.plugin.setRatio(t);for(var e,i,s,r,n,a=this.data,o=a.proxy,l=a.firstMPT,h=1e-6;l;)e=o[l.v],l.r?e=Math.round(e):h>e&&e>-h&&(e=0),l.t[l.p]=e,l=l._next;if(a.autoRotate&&(a.autoRotate.rotation=a.mod?a.mod(o.rotation,this.t):o.rotation),1===t||0===t)for(l=a.firstMPT,n=1===t?"e":"b";l;){if((i=l.t).type){if(1===i.type){for(r=i.xs0+i.s+i.xs1,s=1;s<i.l;s++)r+=i["xn"+s]+i["xs"+(s+1)];i[n]=r}}else i[n]=i.s+i.xs0;l=l._next}},function(t,e,i,s,r){this.t=t,this.p=e,this.v=i,this.r=r,s&&(s._prev=this,this._next=s)}),vt=(V._parseToProxy=function(t,e,i,s,r,n){var a,o,l,h,_,u=s,f={},c={},p=i._transform,d=N;for(i._transform=null,N=e,s=_=i.parse(t,e,s,r),N=d,n&&(i._transform=p,u&&(u._prev=null,u._prev&&(u._prev._next=null)));s&&s!==u;){if(s.type<=1&&(c[o=s.p]=s.s+s.c,f[o]=s.s,n||(h=new yt(s,"s",o,h,s.r),s.c=0),1===s.type))for(a=s.l;--a>0;)l="xn"+a,c[o=s.p+"_"+l]=s.data[l],f[o]=s[l],n||(h=new yt(s,l,o,h,s.rxp[l]));s=s._next}return{proxy:f,end:c,firstMPT:h,pt:_}},V.CSSPropTween=function(t,e,s,r,a,o,l,h,_,u,f){this.t=t,this.p=e,this.s=s,this.c=r,this.n=l||e,t instanceof vt||n.push(this.n),this.r=h,this.type=o||0,_&&(this.pr=_,i=!0),this.b=void 0===u?s:u,this.e=void 0===f?s+r:f,a&&(this._next=a,a._prev=this)}),Tt=function(t,e,i,s,r,n){var a=new vt(t,e,i,s-i,r,-1,n);return a.b=i,a.e=a.xs0=s,a},xt=a.parseComplex=function(t,e,i,s,r,n,o,l,h,u){i=i||n||"","function"==typeof s&&(s=s(g,m)),o=new vt(t,e,0,0,o,u?2:1,null,!1,l,i,s),s+="",r&&dt.test(s+i)&&(s=[i,s],a.colorStringFilter(s),i=s[0],s=s[1]);var f,c,p,d,T,x,b,w,P,O,S,k,R,A=i.split(", ").join(",").split(" "),C=s.split(", ").join(",").split(" "),D=A.length,M=!1!==_;for((-1!==s.indexOf(",")||-1!==i.indexOf(","))&&(-1!==(s+i).indexOf("rgb")||-1!==(s+i).indexOf("hsl")?(A=A.join(" ").replace(z,", ").split(" "),C=C.join(" ").replace(z,", ").split(" ")):(A=A.join(" ").split(",").join(", ").split(" "),C=C.join(" ").split(",").join(", ").split(" ")),D=A.length),D!==C.length&&(D=(A=(n||"").split(" ")).length),o.plugin=h,o.setRatio=u,dt.lastIndex=0,f=0;D>f;f++)if(d=A[f],T=C[f],(w=parseFloat(d))||0===w)o.appendXtra("",w,lt(T,w),T.replace(v,""),M&&-1!==T.indexOf("px"),!0);else if(r&&dt.test(d))k=")"+((k=T.indexOf(")")+1)?T.substr(k):""),R=-1!==T.indexOf("hsl")&&W,O=T,d=ct(d,R),T=ct(T,R),(P=d.length+T.length>6)&&!W&&0===T[3]?(o["xs"+o.l]+=o.l?" transparent":"transparent",o.e=o.e.split(C[f]).join("transparent")):(W||(P=!1),R?o.appendXtra(O.substr(0,O.indexOf("hsl"))+(P?"hsla(":"hsl("),d[0],lt(T[0],d[0]),",",!1,!0).appendXtra("",d[1],lt(T[1],d[1]),"%,",!1).appendXtra("",d[2],lt(T[2],d[2]),P?"%,":"%"+k,!1):o.appendXtra(O.substr(0,O.indexOf("rgb"))+(P?"rgba(":"rgb("),d[0],T[0]-d[0],",",!0,!0).appendXtra("",d[1],T[1]-d[1],",",!0).appendXtra("",d[2],T[2]-d[2],P?",":k,!0),P&&(d=d.length<4?1:d[3],o.appendXtra("",d,(T.length<4?1:T[3])-d,k,!1))),dt.lastIndex=0;else if(x=d.match(y)){if(!(b=T.match(v))||b.length!==x.length)return o;for(p=0,c=0;c<x.length;c++)S=x[c],O=d.indexOf(S,p),o.appendXtra(d.substr(p,O-p),Number(S),lt(b[c],S),"",M&&"px"===d.substr(O+S.length,2),0===c),p=O+S.length;o["xs"+o.l]+=d.substr(p)}else o["xs"+o.l]+=o.l||o["xs"+o.l]?" "+T:T;if(-1!==s.indexOf("=")&&o.data){for(k=o.xs0+o.data.s,f=1;f<o.l;f++)k+=o["xs"+f]+o.data["xn"+f];o.e=k+o["xs"+f]}return o.l||(o.type=-1,o.xs0=o.e),o.xfirst||o},bt=9;for((h=vt.prototype).l=h.pr=0;--bt>0;)h["xn"+bt]=0,h["xs"+bt]="";h.xs0="",h._next=h._prev=h.xfirst=h.data=h.plugin=h.setRatio=h.rxp=null,h.appendXtra=function(t,e,i,s,r,n){var a=this,o=a.l;return a["xs"+o]+=n&&(o||a["xs"+o])?" "+t:t||"",i||0===o||a.plugin?(a.l++,a.type=a.setRatio?2:1,a["xs"+a.l]=s||"",o>0?(a.data["xn"+o]=e+i,a.rxp["xn"+o]=r,a["xn"+o]=e,a.plugin||(a.xfirst=new vt(a,"xn"+o,e,i,a.xfirst||a,0,a.n,r,a.pr),a.xfirst.xs0=0),a):(a.data={s:e+i},a.rxp={},a.s=e,a.c=i,a.r=r,a)):(a["xs"+o]+=e+(s||""),a)};var wt=function(t,e){e=e||{},this.p=e.prefix&&Q(t)||t,l[t]=l[this.p]=this,this.format=e.formatter||mt(e.defaultValue,e.color,e.collapsible,e.multi),e.parser&&(this.parse=e.parser),this.clrs=e.color,this.multi=e.multi,this.keyword=e.keyword,this.dflt=e.defaultValue,this.pr=e.priority||0},Pt=V._registerComplexSpecialProp=function(t,e,i){"object"!=typeof e&&(e={parser:i});var s,r=t.split(","),n=e.defaultValue;for(i=i||[n],s=0;s<r.length;s++)e.prefix=0===s&&e.prefix,e.defaultValue=i[s]||n,new wt(r[s],e)},Ot=V._registerPluginProp=function(t){if(!l[t]){var e=t.charAt(0).toUpperCase()+t.substr(1)+"Plugin";Pt(t,{parser:function(t,i,s,r,n,a,h){var _=o.com.greensock.plugins[e];return _?(_._cssRegister(),l[s].parse(t,i,s,r,n,a,h)):(Z("Error: "+e+" js file not loaded."),n)}})}};(h=wt.prototype).parseComplex=function(t,e,i,s,r,n){var a,o,l,h,_,u,f=this.keyword;if(this.multi&&(z.test(i)||z.test(e)?(o=e.replace(z,"|").split("|"),l=i.replace(z,"|").split("|")):f&&(o=[e],l=[i])),l){for(h=l.length>o.length?l.length:o.length,a=0;h>a;a++)e=o[a]=o[a]||this.dflt,i=l[a]=l[a]||this.dflt,f&&(_=e.indexOf(f))!==(u=i.indexOf(f))&&(-1===u?o[a]=o[a].split(f).join(""):-1===_&&(o[a]+=" "+f));e=o.join(", "),i=l.join(", ")}return xt(t,this.p,e,i,this.clrs,this.dflt,s,this.pr,r,n)},h.parse=function(t,e,i,s,n,a,o){return this.parseComplex(t.style,this.format(J(t,this.p,r,!1,this.dflt)),this.format(e),n,a)},a.registerSpecialProp=function(t,e,i){Pt(t,{parser:function(t,s,r,n,a,o,l){var h=new vt(t,r,0,0,a,2,r,!1,i);return h.plugin=o,h.setRatio=e(t,s,n._tween,r),h},priority:i})},a.useSVGTransformAttr=!0;var St,kt="scaleX,scaleY,scaleZ,x,y,z,skewX,skewY,rotation,rotationX,rotationY,perspective,xPercent,yPercent".split(","),Rt=Q("transform"),At=H+"transform",Ct=Q("transformOrigin"),Dt=null!==Q("perspective"),Mt=V.Transform=function(){this.perspective=parseFloat(a.defaultTransformPerspective)||0,this.force3D=!(!1===a.defaultForce3D||!Dt)&&(a.defaultForce3D||"auto")},Ft=_gsScope.SVGElement,zt=function(t,e,i){var s,r=B.createElementNS("http://www.w3.org/2000/svg",t),n=/([a-z])([A-Z])/g;for(s in i)r.setAttributeNS(null,s.replace(n,"$1-$2").toLowerCase(),i[s]);return e.appendChild(r),r},Et=B.documentElement||{},It=function(){var t,e,i,s=d||/Android/i.test(q)&&!_gsScope.chrome;return B.createElementNS&&!s&&(t=zt("svg",Et),i=(e=zt("rect",t,{width:100,height:50,x:100})).getBoundingClientRect().width,e.style[Ct]="50% 50%",e.style[Rt]="scaleX(0.5)",s=i===e.getBoundingClientRect().width&&!(c&&Dt),Et.removeChild(t)),s}(),Xt=function(t,e,i,s,r,n){var o,l,h,_,u,f,c,p,d,m,g,y,v,T,x=t._gsTransform,b=Yt(t,!0);x&&(v=x.xOrigin,T=x.yOrigin),(!s||(o=s.split(" ")).length<2)&&(0===(c=t.getBBox()).x&&0===c.y&&c.width+c.height===0&&(c={x:parseFloat(t.hasAttribute("x")?t.getAttribute("x"):t.hasAttribute("cx")?t.getAttribute("cx"):0)||0,y:parseFloat(t.hasAttribute("y")?t.getAttribute("y"):t.hasAttribute("cy")?t.getAttribute("cy"):0)||0,width:0,height:0}),o=[(-1!==(e=ot(e).split(" "))[0].indexOf("%")?parseFloat(e[0])/100*c.width:parseFloat(e[0]))+c.x,(-1!==e[1].indexOf("%")?parseFloat(e[1])/100*c.height:parseFloat(e[1]))+c.y]),i.xOrigin=_=parseFloat(o[0]),i.yOrigin=u=parseFloat(o[1]),s&&b!==Bt&&(f=b[0],c=b[1],p=b[2],d=b[3],m=b[4],g=b[5],(y=f*d-c*p)&&(l=_*(d/y)+u*(-p/y)+(p*g-d*m)/y,h=_*(-c/y)+u*(f/y)-(f*g-c*m)/y,_=i.xOrigin=o[0]=l,u=i.yOrigin=o[1]=h)),x&&(n&&(i.xOffset=x.xOffset,i.yOffset=x.yOffset,x=i),r||!1!==r&&!1!==a.defaultSmoothOrigin?(l=_-v,h=u-T,x.xOffset+=l*b[0]+h*b[2]-l,x.yOffset+=l*b[1]+h*b[3]-h):x.xOffset=x.yOffset=0),n||t.setAttribute("data-svg-origin",o.join(" "))},Nt=function(t){var e,i=Y("svg",this.ownerSVGElement&&this.ownerSVGElement.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),s=this.parentNode,r=this.nextSibling,n=this.style.cssText;if(Et.appendChild(i),i.appendChild(this),this.style.display="block",t)try{e=this.getBBox(),this._originalGetBBox=this.getBBox,this.getBBox=Nt}catch(t){}else this._originalGetBBox&&(e=this._originalGetBBox());return r?s.insertBefore(this,r):s.appendChild(this),Et.removeChild(i),this.style.cssText=n,e},Lt=function(t){return!(!Ft||!t.getCTM||t.parentNode&&!t.ownerSVGElement||!function(t){try{return t.getBBox()}catch(e){return Nt.call(t,!0)}}(t))},Bt=[1,0,0,1,0,0],Yt=function(t,e){var i,s,r,n,a,o,l=t._gsTransform||new Mt,h=1e5,_=t.style;if(Rt?s=J(t,At,null,!0):t.currentStyle&&(s=(s=t.currentStyle.filter.match(M))&&4===s.length?[s[0].substr(4),Number(s[2].substr(4)),Number(s[1].substr(4)),s[3].substr(4),l.x||0,l.y||0].join(","):""),i=!s||"none"===s||"matrix(1, 0, 0, 1, 0, 0)"===s,!Rt||!(o=!K(t)||"none"===K(t).display)&&t.parentNode||(o&&(n=_.display,_.display="block"),t.parentNode||(a=1,Et.appendChild(t)),i=!(s=J(t,At,null,!0))||"none"===s||"matrix(1, 0, 0, 1, 0, 0)"===s,n?_.display=n:o&&Wt(_,"display"),a&&Et.removeChild(t)),(l.svg||t.getCTM&&Lt(t))&&(i&&-1!==(_[Rt]+"").indexOf("matrix")&&(s=_[Rt],i=0),r=t.getAttribute("transform"),i&&r&&(-1!==r.indexOf("matrix")?(s=r,i=0):-1!==r.indexOf("translate")&&(s="matrix(1,0,0,1,"+r.match(/(?:\-|\b)[\d\-\.e]+\b/gi).join(",")+")",i=0))),i)return Bt;for(r=(s||"").match(y)||[],bt=r.length;--bt>-1;)n=Number(r[bt]),r[bt]=(a=n-(n|=0))?(a*h+(0>a?-.5:.5)|0)/h+n:n;return e&&r.length>6?[r[0],r[1],r[4],r[5],r[12],r[13]]:r},jt=V.getTransform=function(t,i,s,r){if(t._gsTransform&&s&&!r)return t._gsTransform;var n,o,l,h,_,u,f=s&&t._gsTransform||new Mt,c=f.scaleX<0,p=2e-5,d=1e5,m=Dt&&(parseFloat(J(t,Ct,i,!1,"0 0 0").split(" ")[2])||f.zOrigin)||0,g=parseFloat(a.defaultTransformPerspective)||0;if(f.svg=!(!t.getCTM||!Lt(t)),f.svg&&(Xt(t,J(t,Ct,i,!1,"50% 50%")+"",f,t.getAttribute("data-svg-origin")),St=a.useSVGTransformAttr||It),(n=Yt(t))!==Bt){if(16===n.length){var y,v,T,x,b,w=n[0],P=n[1],O=n[2],S=n[3],k=n[4],R=n[5],A=n[6],C=n[7],D=n[8],M=n[9],F=n[10],z=n[12],E=n[13],I=n[14],N=n[11],L=Math.atan2(A,F);f.zOrigin&&(z=D*(I=-f.zOrigin)-n[12],E=M*I-n[13],I=F*I+f.zOrigin-n[14]),f.rotationX=L*X,L&&(y=k*(x=Math.cos(-L))+D*(b=Math.sin(-L)),v=R*x+M*b,T=A*x+F*b,D=k*-b+D*x,M=R*-b+M*x,F=A*-b+F*x,N=C*-b+N*x,k=y,R=v,A=T),L=Math.atan2(-O,F),f.rotationY=L*X,L&&(v=P*(x=Math.cos(-L))-M*(b=Math.sin(-L)),T=O*x-F*b,M=P*b+M*x,F=O*b+F*x,N=S*b+N*x,w=y=w*x-D*b,P=v,O=T),L=Math.atan2(P,w),f.rotation=L*X,L&&(y=w*(x=Math.cos(L))+P*(b=Math.sin(L)),v=k*x+R*b,T=D*x+M*b,P=P*x-w*b,R=R*x-k*b,M=M*x-D*b,w=y,k=v,D=T),f.rotationX&&Math.abs(f.rotationX)+Math.abs(f.rotation)>359.9&&(f.rotationX=f.rotation=0,f.rotationY=180-f.rotationY),L=Math.atan2(k,R),f.scaleX=(Math.sqrt(w*w+P*P+O*O)*d+.5|0)/d,f.scaleY=(Math.sqrt(R*R+A*A)*d+.5|0)/d,f.scaleZ=(Math.sqrt(D*D+M*M+F*F)*d+.5|0)/d,w/=f.scaleX,k/=f.scaleY,P/=f.scaleX,R/=f.scaleY,Math.abs(L)>p?(f.skewX=L*X,k=0,"simple"!==f.skewType&&(f.scaleY*=1/Math.cos(L))):f.skewX=0,f.perspective=N?1/(0>N?-N:N):0,f.x=z,f.y=E,f.z=I,f.svg&&(f.x-=f.xOrigin-(f.xOrigin*w-f.yOrigin*k),f.y-=f.yOrigin-(f.yOrigin*P-f.xOrigin*R))}else if(!Dt||r||!n.length||f.x!==n[4]||f.y!==n[5]||!f.rotationX&&!f.rotationY){var B=n.length>=6,Y=B?n[0]:1,j=n[1]||0,U=n[2]||0,V=B?n[3]:1;f.x=n[4]||0,f.y=n[5]||0,l=Math.sqrt(Y*Y+j*j),h=Math.sqrt(V*V+U*U),_=Y||j?Math.atan2(j,Y)*X:f.rotation||0,u=U||V?Math.atan2(U,V)*X+_:f.skewX||0,f.scaleX=l,f.scaleY=h,f.rotation=_,f.skewX=u,Dt&&(f.rotationX=f.rotationY=f.z=0,f.perspective=g,f.scaleZ=1),f.svg&&(f.x-=f.xOrigin-(f.xOrigin*Y+f.yOrigin*U),f.y-=f.yOrigin-(f.xOrigin*j+f.yOrigin*V))}for(o in Math.abs(f.skewX)>90&&Math.abs(f.skewX)<270&&(c?(f.scaleX*=-1,f.skewX+=f.rotation<=0?180:-180,f.rotation+=f.rotation<=0?180:-180):(f.scaleY*=-1,f.skewX+=f.skewX<=0?180:-180)),f.zOrigin=m,f)f[o]<p&&f[o]>-p&&(f[o]=0)}return s&&(t._gsTransform=f,f.svg&&(St&&t.style[Rt]?e.delayedCall(.001,function(){Wt(t.style,Rt)}):!St&&t.getAttribute("transform")&&e.delayedCall(.001,function(){t.removeAttribute("transform")}))),f},Ut=function(t){var e,i,s=this.data,r=-s.rotation*I,n=r+s.skewX*I,a=1e5,o=(Math.cos(r)*s.scaleX*a|0)/a,l=(Math.sin(r)*s.scaleX*a|0)/a,h=(Math.sin(n)*-s.scaleY*a|0)/a,_=(Math.cos(n)*s.scaleY*a|0)/a,u=this.t.style,f=this.t.currentStyle;if(f){i=l,l=-h,h=-i,e=f.filter,u.filter="";var c,p,m=this.t.offsetWidth,g=this.t.offsetHeight,y="absolute"!==f.position,v="progid:DXImageTransform.Microsoft.Matrix(M11="+o+", M12="+l+", M21="+h+", M22="+_,T=s.x+m*s.xPercent/100,x=s.y+g*s.yPercent/100;if(null!=s.ox&&(T+=(c=(s.oxp?m*s.ox*.01:s.ox)-m/2)-(c*o+(p=(s.oyp?g*s.oy*.01:s.oy)-g/2)*l),x+=p-(c*h+p*_)),v+=y?", Dx="+((c=m/2)-(c*o+(p=g/2)*l)+T)+", Dy="+(p-(c*h+p*_)+x)+")":", sizingMethod='auto expand')",-1!==e.indexOf("DXImageTransform.Microsoft.Matrix(")?u.filter=e.replace(F,v):u.filter=v+" "+e,(0===t||1===t)&&1===o&&0===l&&0===h&&1===_&&(y&&-1===v.indexOf("Dx=0, Dy=0")||w.test(e)&&100!==parseFloat(RegExp.$1)||-1===e.indexOf(e.indexOf("Alpha"))&&u.removeAttribute("filter")),!y){var P,O,S,k=8>d?1:-1;for(c=s.ieOffsetX||0,p=s.ieOffsetY||0,s.ieOffsetX=Math.round((m-((0>o?-o:o)*m+(0>l?-l:l)*g))/2+T),s.ieOffsetY=Math.round((g-((0>_?-_:_)*g+(0>h?-h:h)*m))/2+x),bt=0;4>bt;bt++)S=(i=-1!==(P=f[O=nt[bt]]).indexOf("px")?parseFloat(P):tt(this.t,O,parseFloat(P),P.replace(b,""))||0)!==s[O]?2>bt?-s.ieOffsetX:-s.ieOffsetY:2>bt?c-s.ieOffsetX:p-s.ieOffsetY,u[O]=(s[O]=Math.round(i-S*(0===bt||2===bt?1:k)))+"px"}}},Vt=V.set3DTransformRatio=V.setTransformRatio=function(t){var e,i,s,r,n,a,o,l,h,_,u,f,p,d,m,g,y,v,T,x,b,w,P,O=this.data,S=this.t.style,k=O.rotation,R=O.rotationX,A=O.rotationY,C=O.scaleX,D=O.scaleY,M=O.scaleZ,F=O.x,z=O.y,E=O.z,X=O.svg,N=O.perspective,L=O.force3D,B=O.skewY,Y=O.skewX;if(B&&(Y+=B,k+=B),!((1!==t&&0!==t||"auto"!==L||this.tween._totalTime!==this.tween._totalDuration&&this.tween._totalTime)&&L||E||N||A||R||1!==M)||St&&X||!Dt)k||Y||X?(k*=I,w=Y*I,P=1e5,i=Math.cos(k)*C,n=Math.sin(k)*C,s=Math.sin(k-w)*-D,a=Math.cos(k-w)*D,w&&"simple"===O.skewType&&(e=Math.tan(w-B*I),s*=e=Math.sqrt(1+e*e),a*=e,B&&(e=Math.tan(B*I),i*=e=Math.sqrt(1+e*e),n*=e)),X&&(F+=O.xOrigin-(O.xOrigin*i+O.yOrigin*s)+O.xOffset,z+=O.yOrigin-(O.xOrigin*n+O.yOrigin*a)+O.yOffset,St&&(O.xPercent||O.yPercent)&&(m=this.t.getBBox(),F+=.01*O.xPercent*m.width,z+=.01*O.yPercent*m.height),(m=1e-6)>F&&F>-m&&(F=0),m>z&&z>-m&&(z=0)),T=(i*P|0)/P+","+(n*P|0)/P+","+(s*P|0)/P+","+(a*P|0)/P+","+F+","+z+")",X&&St?this.t.setAttribute("transform","matrix("+T):S[Rt]=(O.xPercent||O.yPercent?"translate("+O.xPercent+"%,"+O.yPercent+"%) matrix(":"matrix(")+T):S[Rt]=(O.xPercent||O.yPercent?"translate("+O.xPercent+"%,"+O.yPercent+"%) matrix(":"matrix(")+C+",0,0,"+D+","+F+","+z+")";else{if(c&&((m=1e-4)>C&&C>-m&&(C=M=2e-5),m>D&&D>-m&&(D=M=2e-5),!N||O.z||O.rotationX||O.rotationY||(N=0)),k||Y)k*=I,g=i=Math.cos(k),y=n=Math.sin(k),Y&&(k-=Y*I,g=Math.cos(k),y=Math.sin(k),"simple"===O.skewType&&(e=Math.tan((Y-B)*I),g*=e=Math.sqrt(1+e*e),y*=e,O.skewY&&(e=Math.tan(B*I),i*=e=Math.sqrt(1+e*e),n*=e))),s=-y,a=g;else{if(!(A||R||1!==M||N||X))return void(S[Rt]=(O.xPercent||O.yPercent?"translate("+O.xPercent+"%,"+O.yPercent+"%) translate3d(":"translate3d(")+F+"px,"+z+"px,"+E+"px)"+(1!==C||1!==D?" scale("+C+","+D+")":""));i=a=1,s=n=0}_=1,r=o=l=h=u=f=0,p=N?-1/N:0,d=O.zOrigin,m=1e-6,x=",",b="0",(k=A*I)&&(g=Math.cos(k),l=-(y=Math.sin(k)),u=p*-y,r=i*y,o=n*y,_=g,p*=g,i*=g,n*=g),(k=R*I)&&(e=s*(g=Math.cos(k))+r*(y=Math.sin(k)),v=a*g+o*y,h=_*y,f=p*y,r=s*-y+r*g,o=a*-y+o*g,_*=g,p*=g,s=e,a=v),1!==M&&(r*=M,o*=M,_*=M,p*=M),1!==D&&(s*=D,a*=D,h*=D,f*=D),1!==C&&(i*=C,n*=C,l*=C,u*=C),(d||X)&&(d&&(F+=r*-d,z+=o*-d,E+=_*-d+d),X&&(F+=O.xOrigin-(O.xOrigin*i+O.yOrigin*s)+O.xOffset,z+=O.yOrigin-(O.xOrigin*n+O.yOrigin*a)+O.yOffset),m>F&&F>-m&&(F=b),m>z&&z>-m&&(z=b),m>E&&E>-m&&(E=0)),T=O.xPercent||O.yPercent?"translate("+O.xPercent+"%,"+O.yPercent+"%) matrix3d(":"matrix3d(",T+=(m>i&&i>-m?b:i)+x+(m>n&&n>-m?b:n)+x+(m>l&&l>-m?b:l),T+=x+(m>u&&u>-m?b:u)+x+(m>s&&s>-m?b:s)+x+(m>a&&a>-m?b:a),R||A||1!==M?(T+=x+(m>h&&h>-m?b:h)+x+(m>f&&f>-m?b:f)+x+(m>r&&r>-m?b:r),T+=x+(m>o&&o>-m?b:o)+x+(m>_&&_>-m?b:_)+x+(m>p&&p>-m?b:p)+x):T+=",0,0,0,0,1,0,",T+=F+x+z+x+E+x+(N?1+-E/N:1)+")",S[Rt]=T}};(h=Mt.prototype).x=h.y=h.z=h.skewX=h.skewY=h.rotation=h.rotationX=h.rotationY=h.zOrigin=h.xPercent=h.yPercent=h.xOffset=h.yOffset=0,h.scaleX=h.scaleY=h.scaleZ=1,Pt("transform,scale,scaleX,scaleY,scaleZ,x,y,z,rotation,rotationX,rotationY,rotationZ,skewX,skewY,shortRotation,shortRotationX,shortRotationY,shortRotationZ,transformOrigin,svgOrigin,transformPerspective,directionalRotation,parseTransform,force3D,skewType,xPercent,yPercent,smoothOrigin",{parser:function(t,e,i,s,n,o,l){if(s._lastParsedTransform===l)return n;s._lastParsedTransform=l;var h,_=l.scale&&"function"==typeof l.scale?l.scale:0;"function"==typeof l[i]&&(h=l[i],l[i]=e),_&&(l.scale=_(g,t));var u,f,c,p,d,y,v,T,x,b=t._gsTransform,w=t.style,P=1e-6,O=kt.length,S=l,k={},R="transformOrigin",A=jt(t,r,!0,S.parseTransform),C=S.transform&&("function"==typeof S.transform?S.transform(g,m):S.transform);if(A.skewType=S.skewType||A.skewType||a.defaultSkewType,s._transform=A,C&&"string"==typeof C&&Rt)(f=j.style)[Rt]=C,f.display="block",f.position="absolute",B.body.appendChild(j),u=jt(j,null,!1),"simple"===A.skewType&&(u.scaleY*=Math.cos(u.skewX*I)),A.svg&&(y=A.xOrigin,v=A.yOrigin,u.x-=A.xOffset,u.y-=A.yOffset,(S.transformOrigin||S.svgOrigin)&&(C={},Xt(t,ot(S.transformOrigin),C,S.svgOrigin,S.smoothOrigin,!0),y=C.xOrigin,v=C.yOrigin,u.x-=C.xOffset-A.xOffset,u.y-=C.yOffset-A.yOffset),(y||v)&&(T=Yt(j,!0),u.x-=y-(y*T[0]+v*T[2]),u.y-=v-(y*T[1]+v*T[3]))),B.body.removeChild(j),u.perspective||(u.perspective=A.perspective),null!=S.xPercent&&(u.xPercent=ht(S.xPercent,A.xPercent)),null!=S.yPercent&&(u.yPercent=ht(S.yPercent,A.yPercent));else if("object"==typeof S){if(u={scaleX:ht(null!=S.scaleX?S.scaleX:S.scale,A.scaleX),scaleY:ht(null!=S.scaleY?S.scaleY:S.scale,A.scaleY),scaleZ:ht(S.scaleZ,A.scaleZ),x:ht(S.x,A.x),y:ht(S.y,A.y),z:ht(S.z,A.z),xPercent:ht(S.xPercent,A.xPercent),yPercent:ht(S.yPercent,A.yPercent),perspective:ht(S.transformPerspective,A.perspective)},null!=(d=S.directionalRotation))if("object"==typeof d)for(f in d)S[f]=d[f];else S.rotation=d;"string"==typeof S.x&&-1!==S.x.indexOf("%")&&(u.x=0,u.xPercent=ht(S.x,A.xPercent)),"string"==typeof S.y&&-1!==S.y.indexOf("%")&&(u.y=0,u.yPercent=ht(S.y,A.yPercent)),u.rotation=_t("rotation"in S?S.rotation:"shortRotation"in S?S.shortRotation+"_short":"rotationZ"in S?S.rotationZ:A.rotation,A.rotation,"rotation",k),Dt&&(u.rotationX=_t("rotationX"in S?S.rotationX:"shortRotationX"in S?S.shortRotationX+"_short":A.rotationX||0,A.rotationX,"rotationX",k),u.rotationY=_t("rotationY"in S?S.rotationY:"shortRotationY"in S?S.shortRotationY+"_short":A.rotationY||0,A.rotationY,"rotationY",k)),u.skewX=_t(S.skewX,A.skewX),u.skewY=_t(S.skewY,A.skewY)}for(Dt&&null!=S.force3D&&(A.force3D=S.force3D,p=!0),(c=A.force3D||A.z||A.rotationX||A.rotationY||u.z||u.rotationX||u.rotationY||u.perspective)||null==S.scale||(u.scaleZ=1);--O>-1;)((C=u[x=kt[O]]-A[x])>P||-P>C||null!=S[x]||null!=N[x])&&(p=!0,n=new vt(A,x,A[x],C,n),x in k&&(n.e=k[x]),n.xs0=0,n.plugin=o,s._overwriteProps.push(n.n));return C=S.transformOrigin,A.svg&&(C||S.svgOrigin)&&(y=A.xOffset,v=A.yOffset,Xt(t,ot(C),u,S.svgOrigin,S.smoothOrigin),n=Tt(A,"xOrigin",(b?A:u).xOrigin,u.xOrigin,n,R),n=Tt(A,"yOrigin",(b?A:u).yOrigin,u.yOrigin,n,R),(y!==A.xOffset||v!==A.yOffset)&&(n=Tt(A,"xOffset",b?y:A.xOffset,A.xOffset,n,R),n=Tt(A,"yOffset",b?v:A.yOffset,A.yOffset,n,R)),C="0px 0px"),(C||Dt&&c&&A.zOrigin)&&(Rt?(p=!0,x=Ct,C=(C||J(t,x,r,!1,"50% 50%"))+"",(n=new vt(w,x,0,0,n,-1,R)).b=w[x],n.plugin=o,Dt?(f=A.zOrigin,C=C.split(" "),A.zOrigin=(C.length>2&&(0===f||"0px"!==C[2])?parseFloat(C[2]):f)||0,n.xs0=n.e=C[0]+" "+(C[1]||"50%")+" 0px",(n=new vt(A,"zOrigin",0,0,n,-1,n.n)).b=f,n.xs0=n.e=A.zOrigin):n.xs0=n.e=C):ot(C+"",A)),p&&(s._transformType=A.svg&&St||!c&&3!==this._transformType?2:3),h&&(l[i]=h),_&&(l.scale=_),n},prefix:!0}),Pt("boxShadow",{defaultValue:"0px 0px 0px 0px #999",prefix:!0,color:!0,multi:!0,keyword:"inset"}),Pt("borderRadius",{defaultValue:"0px",parser:function(t,e,i,n,a,o){e=this.format(e);var l,h,_,u,f,c,p,d,m,g,y,v,T,x,b,w,P=["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"],O=t.style;for(m=parseFloat(t.offsetWidth),g=parseFloat(t.offsetHeight),l=e.split(" "),h=0;h<P.length;h++)this.p.indexOf("border")&&(P[h]=Q(P[h])),-1!==(f=u=J(t,P[h],r,!1,"0px")).indexOf(" ")&&(u=f.split(" "),f=u[0],u=u[1]),c=_=l[h],p=parseFloat(f),v=f.substr((p+"").length),(T="="===c.charAt(1))?(d=parseInt(c.charAt(0)+"1",10),c=c.substr(2),d*=parseFloat(c),y=c.substr((d+"").length-(0>d?1:0))||""):(d=parseFloat(c),y=c.substr((d+"").length)),""===y&&(y=s[i]||v),y!==v&&(x=tt(t,"borderLeft",p,v),b=tt(t,"borderTop",p,v),"%"===y?(f=x/m*100+"%",u=b/g*100+"%"):"em"===y?(f=x/(w=tt(t,"borderLeft",1,"em"))+"em",u=b/w+"em"):(f=x+"px",u=b+"px"),T&&(c=parseFloat(f)+d+y,_=parseFloat(u)+d+y)),a=xt(O,P[h],f+" "+u,c+" "+_,!1,"0px",a);return a},prefix:!0,formatter:mt("0px 0px 0px 0px",!1,!0)}),Pt("borderBottomLeftRadius,borderBottomRightRadius,borderTopLeftRadius,borderTopRightRadius",{defaultValue:"0px",parser:function(t,e,i,s,n,a){return xt(t.style,i,this.format(J(t,i,r,!1,"0px 0px")),this.format(e),!1,"0px",n)},prefix:!0,formatter:mt("0px 0px",!1,!0)}),Pt("backgroundPosition",{defaultValue:"0 0",parser:function(t,e,i,s,n,a){var o,l,h,_,u,f,c="background-position",p=r||K(t,null),m=this.format((p?d?p.getPropertyValue(c+"-x")+" "+p.getPropertyValue(c+"-y"):p.getPropertyValue(c):t.currentStyle.backgroundPositionX+" "+t.currentStyle.backgroundPositionY)||"0 0"),g=this.format(e);if(-1!==m.indexOf("%")!=(-1!==g.indexOf("%"))&&g.split(",").length<2&&(f=J(t,"backgroundImage").replace(A,""))&&"none"!==f){for(o=m.split(" "),l=g.split(" "),U.setAttribute("src",f),h=2;--h>-1;)(_=-1!==(m=o[h]).indexOf("%"))!=(-1!==l[h].indexOf("%"))&&(u=0===h?t.offsetWidth-U.width:t.offsetHeight-U.height,o[h]=_?parseFloat(m)/100*u+"px":parseFloat(m)/u*100+"%");m=o.join(" ")}return this.parseComplex(t.style,m,g,n,a)},formatter:ot}),Pt("backgroundSize",{defaultValue:"0 0",formatter:function(t){return ot(-1===(t+="").indexOf(" ")?t+" "+t:t)}}),Pt("perspective",{defaultValue:"0px",prefix:!0}),Pt("perspectiveOrigin",{defaultValue:"50% 50%",prefix:!0}),Pt("transformStyle",{prefix:!0}),Pt("backfaceVisibility",{prefix:!0}),Pt("userSelect",{prefix:!0}),Pt("margin",{parser:gt("marginTop,marginRight,marginBottom,marginLeft")}),Pt("padding",{parser:gt("paddingTop,paddingRight,paddingBottom,paddingLeft")}),Pt("clip",{defaultValue:"rect(0px,0px,0px,0px)",parser:function(t,e,i,s,n,a){var o,l,h;return 9>d?(l=t.currentStyle,h=8>d?" ":",",o="rect("+l.clipTop+h+l.clipRight+h+l.clipBottom+h+l.clipLeft+")",e=this.format(e).split(",").join(h)):(o=this.format(J(t,this.p,r,!1,this.dflt)),e=this.format(e)),this.parseComplex(t.style,o,e,n,a)}}),Pt("textShadow",{defaultValue:"0px 0px 0px #999",color:!0,multi:!0}),Pt("autoRound,strictUnits",{parser:function(t,e,i,s,r){return r}}),Pt("border",{defaultValue:"0px solid #000",parser:function(t,e,i,s,n,a){var o=J(t,"borderTopWidth",r,!1,"0px"),l=this.format(e).split(" "),h=l[0].replace(b,"");return"px"!==h&&(o=parseFloat(o)/tt(t,"borderTopWidth",1,h)+h),this.parseComplex(t.style,this.format(o+" "+J(t,"borderTopStyle",r,!1,"solid")+" "+J(t,"borderTopColor",r,!1,"#000")),l.join(" "),n,a)},color:!0,formatter:function(t){var e=t.split(" ");return e[0]+" "+(e[1]||"solid")+" "+(t.match(dt)||["#000"])[0]}}),Pt("borderWidth",{parser:gt("borderTopWidth,borderRightWidth,borderBottomWidth,borderLeftWidth")}),Pt("float,cssFloat,styleFloat",{parser:function(t,e,i,s,r,n){var a=t.style,o="cssFloat"in a?"cssFloat":"styleFloat";return new vt(a,o,0,0,r,-1,i,!1,0,a[o],e)}});var qt=function(t){var e,i=this.t,s=i.filter||J(this.data,"filter")||"",r=this.s+this.c*t|0;100===r&&(-1===s.indexOf("atrix(")&&-1===s.indexOf("radient(")&&-1===s.indexOf("oader(")?(i.removeAttribute("filter"),e=!J(this.data,"filter")):(i.filter=s.replace(O,""),e=!0)),e||(this.xn1&&(i.filter=s=s||"alpha(opacity="+r+")"),-1===s.indexOf("pacity")?0===r&&this.xn1||(i.filter=s+" alpha(opacity="+r+")"):i.filter=s.replace(w,"opacity="+r))};Pt("opacity,alpha,autoAlpha",{defaultValue:"1",parser:function(t,e,i,s,n,a){var o=parseFloat(J(t,"opacity",r,!1,"1")),l=t.style,h="autoAlpha"===i;return"string"==typeof e&&"="===e.charAt(1)&&(e=("-"===e.charAt(0)?-1:1)*parseFloat(e.substr(2))+o),h&&1===o&&"hidden"===J(t,"visibility",r)&&0!==e&&(o=0),W?n=new vt(l,"opacity",o,e-o,n):((n=new vt(l,"opacity",100*o,100*(e-o),n)).xn1=h?1:0,l.zoom=1,n.type=2,n.b="alpha(opacity="+n.s+")",n.e="alpha(opacity="+(n.s+n.c)+")",n.data=t,n.plugin=a,n.setRatio=qt),h&&((n=new vt(l,"visibility",0,0,n,-1,null,!1,0,0!==o?"inherit":"hidden",0===e?"hidden":"inherit")).xs0="inherit",s._overwriteProps.push(n.n),s._overwriteProps.push(i)),n}});var Wt=function(t,e){e&&(t.removeProperty?(("ms"===e.substr(0,2)||"webkit"===e.substr(0,6))&&(e="-"+e),t.removeProperty(e.replace(k,"-$1").toLowerCase())):t.removeAttribute(e))},Gt=function(t){if(this.t._gsClassPT=this,1===t||0===t){this.t.setAttribute("class",0===t?this.b:this.e);for(var e=this.data,i=this.t.style;e;)e.v?i[e.p]=e.v:Wt(i,e.p),e=e._next;1===t&&this.t._gsClassPT===this&&(this.t._gsClassPT=null)}else this.t.getAttribute("class")!==this.e&&this.t.setAttribute("class",this.e)};Pt("className",{parser:function(t,e,s,n,a,o,l){var h,_,u,f,c,p=t.getAttribute("class")||"",d=t.style.cssText;if((a=n._classNamePT=new vt(t,s,0,0,a,2)).setRatio=Gt,a.pr=-11,i=!0,a.b=p,_=it(t,r),u=t._gsClassPT){for(f={},c=u.data;c;)f[c.p]=1,c=c._next;u.setRatio(1)}return t._gsClassPT=a,a.e="="!==e.charAt(1)?e:p.replace(new RegExp("(?:\\s|^)"+e.substr(2)+"(?![\\w-])"),"")+("+"===e.charAt(0)?" "+e.substr(2):""),t.setAttribute("class",a.e),h=st(t,_,it(t),l,f),t.setAttribute("class",p),a.data=h.firstMPT,t.style.cssText=d,a.xfirst=n.parse(t,h.difs,a,o)}});var Zt=function(t){if((1===t||0===t)&&this.data._totalTime===this.data._totalDuration&&"isFromStart"!==this.data.data){var e,i,s,r,n,a=this.t.style,o=l.transform.parse;if("all"===this.e)a.cssText="",r=!0;else for(s=(e=this.e.split(" ").join("").split(",")).length;--s>-1;)i=e[s],l[i]&&(l[i].parse===o?r=!0:i="transformOrigin"===i?Ct:l[i].p),Wt(a,i);r&&(Wt(a,Rt),(n=this.t._gsTransform)&&(n.svg&&(this.t.removeAttribute("data-svg-origin"),this.t.removeAttribute("transform")),delete this.t._gsTransform))}};for(Pt("clearProps",{parser:function(t,e,s,r,n){return(n=new vt(t,s,0,0,n,2)).setRatio=Zt,n.e=e,n.pr=-10,n.data=r._tween,i=!0,n}}),h="bezier,throwProps,physicsProps,physics2D".split(","),bt=h.length;bt--;)Ot(h[bt]);(h=a.prototype)._firstPT=h._lastParsedTransform=h._transform=null,h._onInitTween=function(t,e,o,h){if(!t.nodeType)return!1;this._target=m=t,this._tween=o,this._vars=e,g=h,_=e.autoRound,i=!1,s=e.suffixMap||a.suffixMap,r=K(t,""),n=this._overwriteProps;var c,d,y,v,T,x,b,w,O,S=t.style;if(u&&""===S.zIndex&&("auto"===(c=J(t,"zIndex",r))||""===c)&&this._addLazySet(S,"zIndex",0),"string"==typeof e&&(v=S.cssText,c=it(t,r),S.cssText=v+";"+e,c=st(t,c,it(t)).difs,!W&&P.test(e)&&(c.opacity=parseFloat(RegExp.$1)),e=c,S.cssText=v),e.className?this._firstPT=d=l.className.parse(t,e.className,"className",this,null,null,e):this._firstPT=d=this.parse(t,e,null),this._transformType){for(O=3===this._transformType,Rt?f&&(u=!0,""===S.zIndex&&("auto"===(b=J(t,"zIndex",r))||""===b)&&this._addLazySet(S,"zIndex",0),p&&this._addLazySet(S,"WebkitBackfaceVisibility",this._vars.WebkitBackfaceVisibility||(O?"visible":"hidden"))):S.zoom=1,y=d;y&&y._next;)y=y._next;w=new vt(t,"transform",0,0,null,2),this._linkCSSP(w,null,y),w.setRatio=Rt?Vt:Ut,w.data=this._transform||jt(t,r,!0),w.tween=o,w.pr=-1,n.pop()}if(i){for(;d;){for(x=d._next,y=v;y&&y.pr>d.pr;)y=y._next;(d._prev=y?y._prev:T)?d._prev._next=d:v=d,(d._next=y)?y._prev=d:T=d,d=x}this._firstPT=v}return!0},h.parse=function(t,e,i,n){var a,o,h,u,f,c,p,d,y,v,T=t.style;for(a in e){if("function"==typeof(c=e[a])&&(c=c(g,m)),o=l[a])i=o.parse(t,c,a,this,i,n,e);else{if("--"===a.substr(0,2)){this._tween._propLookup[a]=this._addTween.call(this._tween,t.style,"setProperty",K(t).getPropertyValue(a)+"",c+"",a,!1,a);continue}f=J(t,a,r)+"",y="string"==typeof c,"color"===a||"fill"===a||"stroke"===a||-1!==a.indexOf("Color")||y&&S.test(c)?(y||(c=((c=ct(c)).length>3?"rgba(":"rgb(")+c.join(",")+")"),i=xt(T,a,f,c,!0,"transparent",i,0,n)):y&&E.test(c)?i=xt(T,a,f,c,!0,null,i,0,n):(p=(h=parseFloat(f))||0===h?f.substr((h+"").length):"",(""===f||"auto"===f)&&("width"===a||"height"===a?(h=at(t,a,r),p="px"):"left"===a||"top"===a?(h=et(t,a,r),p="px"):(h="opacity"!==a?0:1,p="")),(v=y&&"="===c.charAt(1))?(u=parseInt(c.charAt(0)+"1",10),c=c.substr(2),u*=parseFloat(c),d=c.replace(b,"")):(u=parseFloat(c),d=y?c.replace(b,""):""),""===d&&(d=a in s?s[a]:p),c=u||0===u?(v?u+h:u)+d:e[a],p!==d&&(""!==d||"lineHeight"===a)&&(u||0===u)&&h&&(h=tt(t,a,h,p),"%"===d?(h/=tt(t,a,100,"%")/100,!0!==e.strictUnits&&(f=h+"%")):"em"===d||"rem"===d||"vw"===d||"vh"===d?h/=tt(t,a,1,d):"px"!==d&&(u=tt(t,a,u,d),d="px"),v&&(u||0===u)&&(c=u+h+d)),v&&(u+=h),!h&&0!==h||!u&&0!==u?void 0!==T[a]&&(c||c+""!="NaN"&&null!=c)?(i=new vt(T,a,u||h||0,0,i,-1,a,!1,0,f,c)).xs0="none"!==c||"display"!==a&&-1===a.indexOf("Style")?c:f:Z("invalid "+a+" tween value: "+e[a]):(i=new vt(T,a,h,u-h,i,0,a,!1!==_&&("px"===d||"zIndex"===a),0,f,c)).xs0=d)}n&&i&&!i.plugin&&(i.plugin=n)}return i},h.setRatio=function(t){var e,i,s,r=this._firstPT,n=1e-6;if(1!==t||this._tween._time!==this._tween._duration&&0!==this._tween._time)if(t||this._tween._time!==this._tween._duration&&0!==this._tween._time||-1e-6===this._tween._rawPrevTime)for(;r;){if(e=r.c*t+r.s,r.r?e=Math.round(e):n>e&&e>-n&&(e=0),r.type)if(1===r.type)if(2===(s=r.l))r.t[r.p]=r.xs0+e+r.xs1+r.xn1+r.xs2;else if(3===s)r.t[r.p]=r.xs0+e+r.xs1+r.xn1+r.xs2+r.xn2+r.xs3;else if(4===s)r.t[r.p]=r.xs0+e+r.xs1+r.xn1+r.xs2+r.xn2+r.xs3+r.xn3+r.xs4;else if(5===s)r.t[r.p]=r.xs0+e+r.xs1+r.xn1+r.xs2+r.xn2+r.xs3+r.xn3+r.xs4+r.xn4+r.xs5;else{for(i=r.xs0+e+r.xs1,s=1;s<r.l;s++)i+=r["xn"+s]+r["xs"+(s+1)];r.t[r.p]=i}else-1===r.type?r.t[r.p]=r.xs0:r.setRatio&&r.setRatio(t);else r.t[r.p]=e+r.xs0;r=r._next}else for(;r;)2!==r.type?r.t[r.p]=r.b:r.setRatio(t),r=r._next;else for(;r;){if(2!==r.type)if(r.r&&-1!==r.type)if(e=Math.round(r.s+r.c),r.type){if(1===r.type){for(s=r.l,i=r.xs0+e+r.xs1,s=1;s<r.l;s++)i+=r["xn"+s]+r["xs"+(s+1)];r.t[r.p]=i}}else r.t[r.p]=e+r.xs0;else r.t[r.p]=r.e;else r.setRatio(t);r=r._next}},h._enableTransforms=function(t){this._transform=this._transform||jt(this._target,r,!0),this._transformType=this._transform.svg&&St||!t&&3!==this._transformType?2:3};var Ht=function(t){this.t[this.p]=this.e,this.data._linkCSSP(this,this._next,null,!0)};h._addLazySet=function(t,e,i){var s=this._firstPT=new vt(t,e,0,0,this._firstPT,2);s.e=i,s.setRatio=Ht,s.data=this},h._linkCSSP=function(t,e,i,s){return t&&(e&&(e._prev=t),t._next&&(t._next._prev=t._prev),t._prev?t._prev._next=t._next:this._firstPT===t&&(this._firstPT=t._next,s=!0),i?i._next=t:s||null!==this._firstPT||(this._firstPT=t),t._next=e,t._prev=i),t},h._mod=function(t){for(var e=this._firstPT;e;)"function"==typeof t[e.p]&&t[e.p]===Math.round&&(e.r=1),e=e._next},h._kill=function(e){var i,s,r,n=e;if(e.autoAlpha||e.alpha){for(s in n={},e)n[s]=e[s];n.opacity=1,n.autoAlpha&&(n.visibility=1)}for(e.className&&(i=this._classNamePT)&&((r=i.xfirst)&&r._prev?this._linkCSSP(r._prev,i._next,r._prev._prev):r===this._firstPT&&(this._firstPT=i._next),i._next&&this._linkCSSP(i._next,i._next._next,r._prev),this._classNamePT=null),i=this._firstPT;i;)i.plugin&&i.plugin!==s&&i.plugin._kill&&(i.plugin._kill(e),s=i.plugin),i=i._next;return t.prototype._kill.call(this,n)};var $t=function(t,e,i){var s,r,n,a;if(t.slice)for(r=t.length;--r>-1;)$t(t[r],e,i);else for(r=(s=t.childNodes).length;--r>-1;)a=(n=s[r]).type,n.style&&(e.push(it(n)),i&&i.push(n)),1!==a&&9!==a&&11!==a||!n.childNodes.length||$t(n,e,i)};return a.cascadeTo=function(t,i,s){var r,n,a,o,l=e.to(t,i,s),h=[l],_=[],u=[],f=[],c=e._internals.reservedProps;for(t=l._targets||l.target,$t(t,_,f),l.render(i,!0,!0),$t(t,u),l.render(0,!0,!0),l._enabled(!0),r=f.length;--r>-1;)if((n=st(f[r],_[r],u[r])).firstMPT){for(a in n=n.difs,s)c[a]&&(n[a]=s[a]);for(a in o={},n)o[a]=_[r][a];h.push(e.fromTo(f[r],i,o,n))}return h},t.activate([a]),a},!0),function(){var t=_gsScope._gsDefine.plugin({propName:"roundProps",version:"1.6.0",priority:-1,API:2,init:function(t,e,i){return this._tween=i,!0}}),e=function(t){for(;t;)t.f||t.blob||(t.m=Math.round),t=t._next},i=t.prototype;i._onInitAllProps=function(){for(var t,i,s,r=this._tween,n=r.vars.roundProps.join?r.vars.roundProps:r.vars.roundProps.split(","),a=n.length,o={},l=r._propLookup.roundProps;--a>-1;)o[n[a]]=Math.round;for(a=n.length;--a>-1;)for(t=n[a],i=r._firstPT;i;)s=i._next,i.pg?i.t._mod(o):i.n===t&&(2===i.f&&i.t?e(i.t._firstPT):(this._add(i.t,t,i.s,i.c),s&&(s._prev=i._prev),i._prev?i._prev._next=s:r._firstPT===i&&(r._firstPT=s),i._next=i._prev=null,r._propLookup[t]=l)),i=s;return!1},i._add=function(t,e,i,s){this._addTween(t,e,i,i+s,e,Math.round),this._overwriteProps.push(e)}}(),_gsScope._gsDefine.plugin({propName:"attr",API:2,version:"0.6.1",init:function(t,e,i,s){var r,n;if("function"!=typeof t.setAttribute)return!1;for(r in e)"function"==typeof(n=e[r])&&(n=n(s,t)),this._addTween(t,"setAttribute",t.getAttribute(r)+"",n+"",r,!1,r),this._overwriteProps.push(r);return!0}}),_gsScope._gsDefine.plugin({propName:"directionalRotation",version:"0.3.1",API:2,init:function(t,e,i,s){"object"!=typeof e&&(e={rotation:e}),this.finals={};var r,n,a,o,l,h,_=!0===e.useRadians?2*Math.PI:360,u=1e-6;for(r in e)"useRadians"!==r&&("function"==typeof(o=e[r])&&(o=o(s,t)),n=(h=(o+"").split("_"))[0],a=parseFloat("function"!=typeof t[r]?t[r]:t[r.indexOf("set")||"function"!=typeof t["get"+r.substr(3)]?r:"get"+r.substr(3)]()),l=(o=this.finals[r]="string"==typeof n&&"="===n.charAt(1)?a+parseInt(n.charAt(0)+"1",10)*Number(n.substr(2)):Number(n)||0)-a,h.length&&(-1!==(n=h.join("_")).indexOf("short")&&(l%=_)!==l%(_/2)&&(l=0>l?l+_:l-_),-1!==n.indexOf("_cw")&&0>l?l=(l+9999999999*_)%_-(l/_|0)*_:-1!==n.indexOf("ccw")&&l>0&&(l=(l-9999999999*_)%_-(l/_|0)*_)),(l>u||-u>l)&&(this._addTween(t,r,a,a+l,r),this._overwriteProps.push(r)));return!0},set:function(t){var e;if(1!==t)this._super.setRatio.call(this,t);else for(e=this._firstPT;e;)e.f?e.t[e.p](this.finals[e.p]):e.t[e.p]=this.finals[e.p],e=e._next}})._autoCSS=!0,_gsScope._gsDefine("easing.Back",["easing.Ease"],function(t){var e,i,s,r=_gsScope.GreenSockGlobals||_gsScope,n=r.com.greensock,a=2*Math.PI,o=Math.PI/2,l=n._class,h=function(e,i){var s=l("easing."+e,function(){},!0),r=s.prototype=new t;return r.constructor=s,r.getRatio=i,s},_=t.register||function(){},u=function(t,e,i,s,r){var n=l("easing."+t,{easeOut:new e,easeIn:new i,easeInOut:new s},!0);return _(n,t),n},f=function(t,e,i){this.t=t,this.v=e,i&&(this.next=i,i.prev=this,this.c=i.v-e,this.gap=i.t-t)},c=function(e,i){var s=l("easing."+e,function(t){this._p1=t||0===t?t:1.70158,this._p2=1.525*this._p1},!0),r=s.prototype=new t;return r.constructor=s,r.getRatio=i,r.config=function(t){return new s(t)},s},p=u("Back",c("BackOut",function(t){return(t-=1)*t*((this._p1+1)*t+this._p1)+1}),c("BackIn",function(t){return t*t*((this._p1+1)*t-this._p1)}),c("BackInOut",function(t){return(t*=2)<1?.5*t*t*((this._p2+1)*t-this._p2):.5*((t-=2)*t*((this._p2+1)*t+this._p2)+2)})),d=l("easing.SlowMo",function(t,e,i){e=e||0===e?e:.7,null==t?t=.7:t>1&&(t=1),this._p=1!==t?e:0,this._p1=(1-t)/2,this._p2=t,this._p3=this._p1+this._p2,this._calcEnd=!0===i},!0),m=d.prototype=new t;return m.constructor=d,m.getRatio=function(t){var e=t+(.5-t)*this._p;return t<this._p1?this._calcEnd?1-(t=1-t/this._p1)*t:e-(t=1-t/this._p1)*t*t*t*e:t>this._p3?this._calcEnd?1===t?0:1-(t=(t-this._p3)/this._p1)*t:e+(t-e)*(t=(t-this._p3)/this._p1)*t*t*t:this._calcEnd?1:e},d.ease=new d(.7,.7),m.config=d.config=function(t,e,i){return new d(t,e,i)},e=l("easing.SteppedEase",function(t,e){t=t||1,this._p1=1/t,this._p2=t+(e?0:1),this._p3=e?1:0},!0),(m=e.prototype=new t).constructor=e,m.getRatio=function(t){return 0>t?t=0:t>=1&&(t=.999999999),((this._p2*t|0)+this._p3)*this._p1},m.config=e.config=function(t,i){return new e(t,i)},i=l("easing.RoughEase",function(e){for(var i,s,r,n,a,o,l=(e=e||{}).taper||"none",h=[],_=0,u=0|(e.points||20),c=u,p=!1!==e.randomize,d=!0===e.clamp,m=e.template instanceof t?e.template:null,g="number"==typeof e.strength?.4*e.strength:.4;--c>-1;)i=p?Math.random():1/u*c,s=m?m.getRatio(i):i,r="none"===l?g:"out"===l?(n=1-i)*n*g:"in"===l?i*i*g:.5>i?(n=2*i)*n*.5*g:(n=2*(1-i))*n*.5*g,p?s+=Math.random()*r-.5*r:c%2?s+=.5*r:s-=.5*r,d&&(s>1?s=1:0>s&&(s=0)),h[_++]={x:i,y:s};for(h.sort(function(t,e){return t.x-e.x}),o=new f(1,1,null),c=u;--c>-1;)a=h[c],o=new f(a.x,a.y,o);this._prev=new f(0,0,0!==o.t?o:o.next)},!0),(m=i.prototype=new t).constructor=i,m.getRatio=function(t){var e=this._prev;if(t>e.t){for(;e.next&&t>=e.t;)e=e.next;e=e.prev}else for(;e.prev&&t<=e.t;)e=e.prev;return this._prev=e,e.v+(t-e.t)/e.gap*e.c},m.config=function(t){return new i(t)},i.ease=new i,u("Bounce",h("BounceOut",function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375}),h("BounceIn",function(t){return(t=1-t)<1/2.75?1-7.5625*t*t:2/2.75>t?1-(7.5625*(t-=1.5/2.75)*t+.75):2.5/2.75>t?1-(7.5625*(t-=2.25/2.75)*t+.9375):1-(7.5625*(t-=2.625/2.75)*t+.984375)}),h("BounceInOut",function(t){var e=.5>t;return t=1/2.75>(t=e?1-2*t:2*t-1)?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375,e?.5*(1-t):.5*t+.5})),u("Circ",h("CircOut",function(t){return Math.sqrt(1-(t-=1)*t)}),h("CircIn",function(t){return-(Math.sqrt(1-t*t)-1)}),h("CircInOut",function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)})),s=function(e,i,s){var r=l("easing."+e,function(t,e){this._p1=t>=1?t:1,this._p2=(e||s)/(1>t?t:1),this._p3=this._p2/a*(Math.asin(1/this._p1)||0),this._p2=a/this._p2},!0),n=r.prototype=new t;return n.constructor=r,n.getRatio=i,n.config=function(t,e){return new r(t,e)},r},u("Elastic",s("ElasticOut",function(t){return this._p1*Math.pow(2,-10*t)*Math.sin((t-this._p3)*this._p2)+1},.3),s("ElasticIn",function(t){return-this._p1*Math.pow(2,10*(t-=1))*Math.sin((t-this._p3)*this._p2)},.3),s("ElasticInOut",function(t){return(t*=2)<1?this._p1*Math.pow(2,10*(t-=1))*Math.sin((t-this._p3)*this._p2)*-.5:this._p1*Math.pow(2,-10*(t-=1))*Math.sin((t-this._p3)*this._p2)*.5+1},.45)),u("Expo",h("ExpoOut",function(t){return 1-Math.pow(2,-10*t)}),h("ExpoIn",function(t){return Math.pow(2,10*(t-1))-.001}),h("ExpoInOut",function(t){return(t*=2)<1?.5*Math.pow(2,10*(t-1)):.5*(2-Math.pow(2,-10*(t-1)))})),u("Sine",h("SineOut",function(t){return Math.sin(t*o)}),h("SineIn",function(t){return 1-Math.cos(t*o)}),h("SineInOut",function(t){return-.5*(Math.cos(Math.PI*t)-1)})),l("easing.EaseLookup",{find:function(e){return t.map[e]}},!0),_(r.SlowMo,"SlowMo","ease,"),_(i,"RoughEase","ease,"),_(e,"SteppedEase","ease,"),p},!0)}),_gsScope._gsDefine&&_gsScope._gsQueue.pop()(),function(t,e){"use strict";var i={},s=t.document,r=t.GreenSockGlobals=t.GreenSockGlobals||t;if(!r.TweenLite){var n,a,o,l,h,_=function(t){var e,i=t.split("."),s=r;for(e=0;e<i.length;e++)s[i[e]]=s=s[i[e]]||{};return s},u=_("com.greensock"),f=1e-10,c=function(t){var e,i=[],s=t.length;for(e=0;e!==s;i.push(t[e++]));return i},p=function(){},d=function(){var t=Object.prototype.toString,e=t.call([]);return function(i){return null!=i&&(i instanceof Array||"object"==typeof i&&!!i.push&&t.call(i)===e)}}(),m={},g=function(s,n,a,o){this.sc=m[s]?m[s].sc:[],m[s]=this,this.gsClass=null,this.func=a;var l=[];this.check=function(h){for(var u,f,c,p,d=n.length,y=d;--d>-1;)(u=m[n[d]]||new g(n[d],[])).gsClass?(l[d]=u.gsClass,y--):h&&u.sc.push(this);if(0===y&&a){if(c=(f=("com.greensock."+s).split(".")).pop(),p=_(f.join("."))[c]=this.gsClass=a.apply(a,l),o)if(r[c]=i[c]=p,"undefined"!=typeof module&&module.exports)if(s===e)for(d in module.exports=i[e]=p,i)p[d]=i[d];else i[e]&&(i[e][c]=p);else"function"==typeof define&&define.amd&&define((t.GreenSockAMDPath?t.GreenSockAMDPath+"/":"")+s.split(".").pop(),[],function(){return p});for(d=0;d<this.sc.length;d++)this.sc[d].check()}},this.check(!0)},y=t._gsDefine=function(t,e,i,s){return new g(t,e,i,s)},v=u._class=function(t,e,i){return e=e||function(){},y(t,[],function(){return e},i),e};y.globals=r;var T=[0,0,1,1],x=v("easing.Ease",function(t,e,i,s){this._func=t,this._type=i||0,this._power=s||0,this._params=e?T.concat(e):T},!0),b=x.map={},w=x.register=function(t,e,i,s){for(var r,n,a,o,l=e.split(","),h=l.length,_=(i||"easeIn,easeOut,easeInOut").split(",");--h>-1;)for(n=l[h],r=s?v("easing."+n,null,!0):u.easing[n]||{},a=_.length;--a>-1;)o=_[a],b[n+"."+o]=b[o+n]=r[o]=t.getRatio?t:t[o]||new t};for((o=x.prototype)._calcEnd=!1,o.getRatio=function(t){if(this._func)return this._params[0]=t,this._func.apply(null,this._params);var e=this._type,i=this._power,s=1===e?1-t:2===e?t:.5>t?2*t:2*(1-t);return 1===i?s*=s:2===i?s*=s*s:3===i?s*=s*s*s:4===i&&(s*=s*s*s*s),1===e?1-s:2===e?s:.5>t?s/2:1-s/2},a=(n=["Linear","Quad","Cubic","Quart","Quint,Strong"]).length;--a>-1;)o=n[a]+",Power"+a,w(new x(null,null,1,a),o,"easeOut",!0),w(new x(null,null,2,a),o,"easeIn"+(0===a?",easeNone":"")),w(new x(null,null,3,a),o,"easeInOut");b.linear=u.easing.Linear.easeIn,b.swing=u.easing.Quad.easeInOut;var P=v("events.EventDispatcher",function(t){this._listeners={},this._eventTarget=t||this});(o=P.prototype).addEventListener=function(t,e,i,s,r){r=r||0;var n,a,o=this._listeners[t],_=0;for(this!==l||h||l.wake(),null==o&&(this._listeners[t]=o=[]),a=o.length;--a>-1;)(n=o[a]).c===e&&n.s===i?o.splice(a,1):0===_&&n.pr<r&&(_=a+1);o.splice(_,0,{c:e,s:i,up:s,pr:r})},o.removeEventListener=function(t,e){var i,s=this._listeners[t];if(s)for(i=s.length;--i>-1;)if(s[i].c===e)return void s.splice(i,1)},o.dispatchEvent=function(t){var e,i,s,r=this._listeners[t];if(r)for((e=r.length)>1&&(r=r.slice(0)),i=this._eventTarget;--e>-1;)(s=r[e])&&(s.up?s.c.call(s.s||i,{type:t,target:i}):s.c.call(s.s||i))};var O=t.requestAnimationFrame,S=t.cancelAnimationFrame,k=Date.now||function(){return(new Date).getTime()},R=k();for(a=(n=["ms","moz","webkit","o"]).length;--a>-1&&!O;)O=t[n[a]+"RequestAnimationFrame"],S=t[n[a]+"CancelAnimationFrame"]||t[n[a]+"CancelRequestAnimationFrame"];v("Ticker",function(t,e){var i,r,n,a,o,_=this,u=k(),c=!(!1===e||!O)&&"auto",d=500,m=33,g=function(t){var e,s,l=k()-R;l>d&&(u+=l-m),R+=l,_.time=(R-u)/1e3,e=_.time-o,(!i||e>0||!0===t)&&(_.frame++,o+=e+(e>=a?.004:a-e),s=!0),!0!==t&&(n=r(g)),s&&_.dispatchEvent("tick")};P.call(_),_.time=_.frame=0,_.tick=function(){g(!0)},_.lagSmoothing=function(t,e){return arguments.length?(d=t||1/f,void(m=Math.min(e,d,0))):1/f>d},_.sleep=function(){null!=n&&(c&&S?S(n):clearTimeout(n),r=p,n=null,_===l&&(h=!1))},_.wake=function(t){null!==n?_.sleep():t?u+=-R+(R=k()):_.frame>10&&(R=k()-d+5),r=0===i?p:c&&O?O:function(t){return setTimeout(t,1e3*(o-_.time)+1|0)},_===l&&(h=!0),g(2)},_.fps=function(t){return arguments.length?(a=1/((i=t)||60),o=this.time+a,void _.wake()):i},_.useRAF=function(t){return arguments.length?(_.sleep(),c=t,void _.fps(i)):c},_.fps(t),setTimeout(function(){"auto"===c&&_.frame<5&&"hidden"!==s.visibilityState&&_.useRAF(!1)},1500)}),(o=u.Ticker.prototype=new u.events.EventDispatcher).constructor=u.Ticker;var A=v("core.Animation",function(t,e){if(this.vars=e=e||{},this._duration=this._totalDuration=t||0,this._delay=Number(e.delay)||0,this._timeScale=1,this._active=!0===e.immediateRender,this.data=e.data,this._reversed=!0===e.reversed,Z){h||l.wake();var i=this.vars.useFrames?G:Z;i.add(this,i._time),this.vars.paused&&this.paused(!0)}});l=A.ticker=new u.Ticker,(o=A.prototype)._dirty=o._gc=o._initted=o._paused=!1,o._totalTime=o._time=0,o._rawPrevTime=-1,o._next=o._last=o._onUpdate=o._timeline=o.timeline=null,o._paused=!1;var C=function(){h&&k()-R>2e3&&("hidden"!==s.visibilityState||!l.lagSmoothing())&&l.wake();var t=setTimeout(C,2e3);t.unref&&t.unref()};C(),o.play=function(t,e){return null!=t&&this.seek(t,e),this.reversed(!1).paused(!1)},o.pause=function(t,e){return null!=t&&this.seek(t,e),this.paused(!0)},o.resume=function(t,e){return null!=t&&this.seek(t,e),this.paused(!1)},o.seek=function(t,e){return this.totalTime(Number(t),!1!==e)},o.restart=function(t,e){return this.reversed(!1).paused(!1).totalTime(t?-this._delay:0,!1!==e,!0)},o.reverse=function(t,e){return null!=t&&this.seek(t||this.totalDuration(),e),this.reversed(!0).paused(!1)},o.render=function(t,e,i){},o.invalidate=function(){return this._time=this._totalTime=0,this._initted=this._gc=!1,this._rawPrevTime=-1,(this._gc||!this.timeline)&&this._enabled(!0),this},o.isActive=function(){var t,e=this._timeline,i=this._startTime;return!e||!this._gc&&!this._paused&&e.isActive()&&(t=e.rawTime(!0))>=i&&t<i+this.totalDuration()/this._timeScale-1e-7},o._enabled=function(t,e){return h||l.wake(),this._gc=!t,this._active=this.isActive(),!0!==e&&(t&&!this.timeline?this._timeline.add(this,this._startTime-this._delay):!t&&this.timeline&&this._timeline._remove(this,!0)),!1},o._kill=function(t,e){return this._enabled(!1,!1)},o.kill=function(t,e){return this._kill(t,e),this},o._uncache=function(t){for(var e=t?this:this.timeline;e;)e._dirty=!0,e=e.timeline;return this},o._swapSelfInParams=function(t){for(var e=t.length,i=t.concat();--e>-1;)"{self}"===t[e]&&(i[e]=this);return i},o._callback=function(t){var e=this.vars,i=e[t],s=e[t+"Params"],r=e[t+"Scope"]||e.callbackScope||this;switch(s?s.length:0){case 0:i.call(r);break;case 1:i.call(r,s[0]);break;case 2:i.call(r,s[0],s[1]);break;default:i.apply(r,s)}},o.eventCallback=function(t,e,i,s){if("on"===(t||"").substr(0,2)){var r=this.vars;if(1===arguments.length)return r[t];null==e?delete r[t]:(r[t]=e,r[t+"Params"]=d(i)&&-1!==i.join("").indexOf("{self}")?this._swapSelfInParams(i):i,r[t+"Scope"]=s),"onUpdate"===t&&(this._onUpdate=e)}return this},o.delay=function(t){return arguments.length?(this._timeline.smoothChildTiming&&this.startTime(this._startTime+t-this._delay),this._delay=t,this):this._delay},o.duration=function(t){return arguments.length?(this._duration=this._totalDuration=t,this._uncache(!0),this._timeline.smoothChildTiming&&this._time>0&&this._time<this._duration&&0!==t&&this.totalTime(this._totalTime*(t/this._duration),!0),this):(this._dirty=!1,this._duration)},o.totalDuration=function(t){return this._dirty=!1,arguments.length?this.duration(t):this._totalDuration},o.time=function(t,e){return arguments.length?(this._dirty&&this.totalDuration(),this.totalTime(t>this._duration?this._duration:t,e)):this._time},o.totalTime=function(t,e,i){if(h||l.wake(),!arguments.length)return this._totalTime;if(this._timeline){if(0>t&&!i&&(t+=this.totalDuration()),this._timeline.smoothChildTiming){this._dirty&&this.totalDuration();var s=this._totalDuration,r=this._timeline;if(t>s&&!i&&(t=s),this._startTime=(this._paused?this._pauseTime:r._time)-(this._reversed?s-t:t)/this._timeScale,r._dirty||this._uncache(!1),r._timeline)for(;r._timeline;)r._timeline._time!==(r._startTime+r._totalTime)/r._timeScale&&r.totalTime(r._totalTime,!0),r=r._timeline}this._gc&&this._enabled(!0,!1),(this._totalTime!==t||0===this._duration)&&(z.length&&$(),this.render(t,e,!1),z.length&&$())}return this},o.progress=o.totalProgress=function(t,e){var i=this.duration();return arguments.length?this.totalTime(i*t,e):i?this._time/i:this.ratio},o.startTime=function(t){return arguments.length?(t!==this._startTime&&(this._startTime=t,this.timeline&&this.timeline._sortChildren&&this.timeline.add(this,t-this._delay)),this):this._startTime},o.endTime=function(t){return this._startTime+(0!=t?this.totalDuration():this.duration())/this._timeScale},o.timeScale=function(t){if(!arguments.length)return this._timeScale;var e,i;for(t=t||f,this._timeline&&this._timeline.smoothChildTiming&&(i=(e=this._pauseTime)||0===e?e:this._timeline.totalTime(),this._startTime=i-(i-this._startTime)*this._timeScale/t),this._timeScale=t,i=this.timeline;i&&i.timeline;)i._dirty=!0,i.totalDuration(),i=i.timeline;return this},o.reversed=function(t){return arguments.length?(t!=this._reversed&&(this._reversed=t,this.totalTime(this._timeline&&!this._timeline.smoothChildTiming?this.totalDuration()-this._totalTime:this._totalTime,!0)),this):this._reversed},o.paused=function(t){if(!arguments.length)return this._paused;var e,i,s=this._timeline;return t!=this._paused&&s&&(h||t||l.wake(),i=(e=s.rawTime())-this._pauseTime,!t&&s.smoothChildTiming&&(this._startTime+=i,this._uncache(!1)),this._pauseTime=t?e:null,this._paused=t,this._active=this.isActive(),!t&&0!==i&&this._initted&&this.duration()&&(e=s.smoothChildTiming?this._totalTime:(e-this._startTime)/this._timeScale,this.render(e,e===this._totalTime,!0))),this._gc&&!t&&this._enabled(!0,!1),this};var D=v("core.SimpleTimeline",function(t){A.call(this,0,t),this.autoRemoveChildren=this.smoothChildTiming=!0});(o=D.prototype=new A).constructor=D,o.kill()._gc=!1,o._first=o._last=o._recent=null,o._sortChildren=!1,o.add=o.insert=function(t,e,i,s){var r,n;if(t._startTime=Number(e||0)+t._delay,t._paused&&this!==t._timeline&&(t._pauseTime=t._startTime+(this.rawTime()-t._startTime)/t._timeScale),t.timeline&&t.timeline._remove(t,!0),t.timeline=t._timeline=this,t._gc&&t._enabled(!0,!0),r=this._last,this._sortChildren)for(n=t._startTime;r&&r._startTime>n;)r=r._prev;return r?(t._next=r._next,r._next=t):(t._next=this._first,this._first=t),t._next?t._next._prev=t:this._last=t,t._prev=r,this._recent=t,this._timeline&&this._uncache(!0),this},o._remove=function(t,e){return t.timeline===this&&(e||t._enabled(!1,!0),t._prev?t._prev._next=t._next:this._first===t&&(this._first=t._next),t._next?t._next._prev=t._prev:this._last===t&&(this._last=t._prev),t._next=t._prev=t.timeline=null,t===this._recent&&(this._recent=this._last),this._timeline&&this._uncache(!0)),this},o.render=function(t,e,i){var s,r=this._first;for(this._totalTime=this._time=this._rawPrevTime=t;r;)s=r._next,(r._active||t>=r._startTime&&!r._paused&&!r._gc)&&(r._reversed?r.render((r._dirty?r.totalDuration():r._totalDuration)-(t-r._startTime)*r._timeScale,e,i):r.render((t-r._startTime)*r._timeScale,e,i)),r=s},o.rawTime=function(){return h||l.wake(),this._totalTime};var M=v("TweenLite",function(e,i,s){if(A.call(this,i,s),this.render=M.prototype.render,null==e)throw"Cannot tween a null target.";this.target=e="string"!=typeof e?e:M.selector(e)||e;var r,n,a,o=e.jquery||e.length&&e!==t&&e[0]&&(e[0]===t||e[0].nodeType&&e[0].style&&!e.nodeType),l=this.vars.overwrite;if(this._overwrite=l=null==l?W[M.defaultOverwrite]:"number"==typeof l?0|l:W[l],(o||e instanceof Array||e.push&&d(e))&&"number"!=typeof e[0])for(this._targets=a=c(e),this._propLookup=[],this._siblings=[],r=0;r<a.length;r++)(n=a[r])?"string"!=typeof n?n.length&&n!==t&&n[0]&&(n[0]===t||n[0].nodeType&&n[0].style&&!n.nodeType)?(a.splice(r--,1),this._targets=a=a.concat(c(n))):(this._siblings[r]=Q(n,this,!1),1===l&&this._siblings[r].length>1&&J(n,this,null,1,this._siblings[r])):"string"==typeof(n=a[r--]=M.selector(n))&&a.splice(r+1,1):a.splice(r--,1);else this._propLookup={},this._siblings=Q(e,this,!1),1===l&&this._siblings.length>1&&J(e,this,null,1,this._siblings);(this.vars.immediateRender||0===i&&0===this._delay&&!1!==this.vars.immediateRender)&&(this._time=-f,this.render(Math.min(0,-this._delay)))},!0),F=function(e){return e&&e.length&&e!==t&&e[0]&&(e[0]===t||e[0].nodeType&&e[0].style&&!e.nodeType)};(o=M.prototype=new A).constructor=M,o.kill()._gc=!1,o.ratio=0,o._firstPT=o._targets=o._overwrittenProps=o._startAt=null,o._notifyPluginsOfEnabled=o._lazy=!1,M.version="1.20.3",M.defaultEase=o._ease=new x(null,null,1,1),M.defaultOverwrite="auto",M.ticker=l,M.autoSleep=120,M.lagSmoothing=function(t,e){l.lagSmoothing(t,e)},M.selector=t.$||t.jQuery||function(e){var i=t.$||t.jQuery;return i?(M.selector=i,i(e)):void 0===s?e:s.querySelectorAll?s.querySelectorAll(e):s.getElementById("#"===e.charAt(0)?e.substr(1):e)};var z=[],E={},I=/(?:(-|-=|\+=)?\d*\.?\d*(?:e[\-+]?\d+)?)[0-9]/gi,X=/[\+-]=-?[\.\d]/,N=function(t){for(var e,i=this._firstPT,s=1e-6;i;)e=i.blob?1===t&&null!=this.end?this.end:t?this.join(""):this.start:i.c*t+i.s,i.m?e=i.m(e,this._target||i.t):s>e&&e>-s&&!i.blob&&(e=0),i.f?i.fp?i.t[i.p](i.fp,e):i.t[i.p](e):i.t[i.p]=e,i=i._next},L=function(t,e,i,s){var r,n,a,o,l,h,_,u=[],f=0,c="",p=0;for(u.start=t,u.end=e,t=u[0]=t+"",e=u[1]=e+"",i&&(i(u),t=u[0],e=u[1]),u.length=0,r=t.match(I)||[],n=e.match(I)||[],s&&(s._next=null,s.blob=1,u._firstPT=u._applyPT=s),l=n.length,o=0;l>o;o++)_=n[o],c+=(h=e.substr(f,e.indexOf(_,f)-f))||!o?h:",",f+=h.length,p?p=(p+1)%5:"rgba("===h.substr(-5)&&(p=1),_===r[o]||r.length<=o?c+=_:(c&&(u.push(c),c=""),a=parseFloat(r[o]),u.push(a),u._firstPT={_next:u._firstPT,t:u,p:u.length-1,s:a,c:("="===_.charAt(1)?parseInt(_.charAt(0)+"1",10)*parseFloat(_.substr(2)):parseFloat(_)-a)||0,f:0,m:p&&4>p?Math.round:0}),f+=_.length;return(c+=e.substr(f))&&u.push(c),u.setRatio=N,X.test(e)&&(u.end=null),u},B=function(t,e,i,s,r,n,a,o,l){"function"==typeof s&&(s=s(l||0,t));var h=typeof t[e],_="function"!==h?"":e.indexOf("set")||"function"!=typeof t["get"+e.substr(3)]?e:"get"+e.substr(3),u="get"!==i?i:_?a?t[_](a):t[_]():t[e],f="string"==typeof s&&"="===s.charAt(1),c={t,p:e,s:u,f:"function"===h,pg:0,n:r||e,m:n?"function"==typeof n?n:Math.round:0,pr:0,c:f?parseInt(s.charAt(0)+"1",10)*parseFloat(s.substr(2)):parseFloat(s)-u||0};return("number"!=typeof u||"number"!=typeof s&&!f)&&(a||isNaN(u)||!f&&isNaN(s)||"boolean"==typeof u||"boolean"==typeof s?(c.fp=a,c={t:L(u,f?parseFloat(c.s)+c.c:s,o||M.defaultStringFilter,c),p:"setRatio",s:0,c:1,f:2,pg:0,n:r||e,pr:0,m:0}):(c.s=parseFloat(u),f||(c.c=parseFloat(s)-c.s||0))),c.c?((c._next=this._firstPT)&&(c._next._prev=c),this._firstPT=c,c):void 0},Y=M._internals={isArray:d,isSelector:F,lazyTweens:z,blobDif:L},j=M._plugins={},U=Y.tweenLookup={},V=0,q=Y.reservedProps={ease:1,delay:1,overwrite:1,onComplete:1,onCompleteParams:1,onCompleteScope:1,useFrames:1,runBackwards:1,startAt:1,onUpdate:1,onUpdateParams:1,onUpdateScope:1,onStart:1,onStartParams:1,onStartScope:1,onReverseComplete:1,onReverseCompleteParams:1,onReverseCompleteScope:1,onRepeat:1,onRepeatParams:1,onRepeatScope:1,easeParams:1,yoyo:1,immediateRender:1,repeat:1,repeatDelay:1,data:1,paused:1,reversed:1,autoCSS:1,lazy:1,onOverwrite:1,callbackScope:1,stringFilter:1,id:1,yoyoEase:1},W={none:0,all:1,auto:2,concurrent:3,allOnStart:4,preexisting:5,true:1,false:0},G=A._rootFramesTimeline=new D,Z=A._rootTimeline=new D,H=30,$=Y.lazyRender=function(){var t,e=z.length;for(E={};--e>-1;)(t=z[e])&&!1!==t._lazy&&(t.render(t._lazy[0],t._lazy[1],!0),t._lazy=!1);z.length=0};Z._startTime=l.time,G._startTime=l.frame,Z._active=G._active=!0,setTimeout($,1),A._updateRoot=M.render=function(){var t,e,i;if(z.length&&$(),Z.render((l.time-Z._startTime)*Z._timeScale,!1,!1),G.render((l.frame-G._startTime)*G._timeScale,!1,!1),z.length&&$(),l.frame>=H){for(i in H=l.frame+(parseInt(M.autoSleep,10)||120),U){for(t=(e=U[i].tweens).length;--t>-1;)e[t]._gc&&e.splice(t,1);0===e.length&&delete U[i]}if((!(i=Z._first)||i._paused)&&M.autoSleep&&!G._first&&1===l._listeners.tick.length){for(;i&&i._paused;)i=i._next;i||l.sleep()}}},l.addEventListener("tick",A._updateRoot);var Q=function(t,e,i){var s,r,n=t._gsTweenID;if(U[n||(t._gsTweenID=n="t"+V++)]||(U[n]={target:t,tweens:[]}),e&&((s=U[n].tweens)[r=s.length]=e,i))for(;--r>-1;)s[r]===e&&s.splice(r,1);return U[n].tweens},K=function(t,e,i,s){var r,n,a=t.vars.onOverwrite;return a&&(r=a(t,e,i,s)),(a=M.onOverwrite)&&(n=a(t,e,i,s)),!1!==r&&!1!==n},J=function(t,e,i,s,r){var n,a,o,l;if(1===s||s>=4){for(l=r.length,n=0;l>n;n++)if((o=r[n])!==e)o._gc||o._kill(null,t,e)&&(a=!0);else if(5===s)break;return a}var h,_=e._startTime+f,u=[],c=0,p=0===e._duration;for(n=r.length;--n>-1;)(o=r[n])===e||o._gc||o._paused||(o._timeline!==e._timeline?(h=h||tt(e,0,p),0===tt(o,h,p)&&(u[c++]=o)):o._startTime<=_&&o._startTime+o.totalDuration()/o._timeScale>_&&((p||!o._initted)&&_-o._startTime<=2e-10||(u[c++]=o)));for(n=c;--n>-1;)if(o=u[n],2===s&&o._kill(i,t,e)&&(a=!0),2!==s||!o._firstPT&&o._initted){if(2!==s&&!K(o,e))continue;o._enabled(!1,!1)&&(a=!0)}return a},tt=function(t,e,i){for(var s=t._timeline,r=s._timeScale,n=t._startTime;s._timeline;){if(n+=s._startTime,r*=s._timeScale,s._paused)return-100;s=s._timeline}return(n/=r)>e?n-e:i&&n===e||!t._initted&&2*f>n-e?f:(n+=t.totalDuration()/t._timeScale/r)>e+f?0:n-e-f};o._init=function(){var t,e,i,s,r,n,a=this.vars,o=this._overwrittenProps,l=this._duration,h=!!a.immediateRender,_=a.ease;if(a.startAt){for(s in this._startAt&&(this._startAt.render(-1,!0),this._startAt.kill()),r={},a.startAt)r[s]=a.startAt[s];if(r.data="isStart",r.overwrite=!1,r.immediateRender=!0,r.lazy=h&&!1!==a.lazy,r.startAt=r.delay=null,r.onUpdate=a.onUpdate,r.onUpdateParams=a.onUpdateParams,r.onUpdateScope=a.onUpdateScope||a.callbackScope||this,this._startAt=M.to(this.target,0,r),h)if(this._time>0)this._startAt=null;else if(0!==l)return}else if(a.runBackwards&&0!==l)if(this._startAt)this._startAt.render(-1,!0),this._startAt.kill(),this._startAt=null;else{for(s in 0!==this._time&&(h=!1),i={},a)q[s]&&"autoCSS"!==s||(i[s]=a[s]);if(i.overwrite=0,i.data="isFromStart",i.lazy=h&&!1!==a.lazy,i.immediateRender=h,this._startAt=M.to(this.target,0,i),h){if(0===this._time)return}else this._startAt._init(),this._startAt._enabled(!1),this.vars.immediateRender&&(this._startAt=null)}if(this._ease=_=_?_ instanceof x?_:"function"==typeof _?new x(_,a.easeParams):b[_]||M.defaultEase:M.defaultEase,a.easeParams instanceof Array&&_.config&&(this._ease=_.config.apply(_,a.easeParams)),this._easeType=this._ease._type,this._easePower=this._ease._power,this._firstPT=null,this._targets)for(n=this._targets.length,t=0;n>t;t++)this._initProps(this._targets[t],this._propLookup[t]={},this._siblings[t],o?o[t]:null,t)&&(e=!0);else e=this._initProps(this.target,this._propLookup,this._siblings,o,0);if(e&&M._onPluginEvent("_onInitAllProps",this),o&&(this._firstPT||"function"!=typeof this.target&&this._enabled(!1,!1)),a.runBackwards)for(i=this._firstPT;i;)i.s+=i.c,i.c=-i.c,i=i._next;this._onUpdate=a.onUpdate,this._initted=!0},o._initProps=function(e,i,s,r,n){var a,o,l,h,_,u;if(null==e)return!1;for(a in E[e._gsTweenID]&&$(),this.vars.css||e.style&&e!==t&&e.nodeType&&j.css&&!1!==this.vars.autoCSS&&function(t,e){var i,s={};for(i in t)q[i]||i in e&&"transform"!==i&&"x"!==i&&"y"!==i&&"width"!==i&&"height"!==i&&"className"!==i&&"border"!==i||!(!j[i]||j[i]&&j[i]._autoCSS)||(s[i]=t[i],delete t[i]);t.css=s}(this.vars,e),this.vars)if(u=this.vars[a],q[a])u&&(u instanceof Array||u.push&&d(u))&&-1!==u.join("").indexOf("{self}")&&(this.vars[a]=u=this._swapSelfInParams(u,this));else if(j[a]&&(h=new j[a])._onInitTween(e,this.vars[a],this,n)){for(this._firstPT=_={_next:this._firstPT,t:h,p:"setRatio",s:0,c:1,f:1,n:a,pg:1,pr:h._priority,m:0},o=h._overwriteProps.length;--o>-1;)i[h._overwriteProps[o]]=this._firstPT;(h._priority||h._onInitAllProps)&&(l=!0),(h._onDisable||h._onEnable)&&(this._notifyPluginsOfEnabled=!0),_._next&&(_._next._prev=_)}else i[a]=B.call(this,e,a,"get",u,a,0,null,this.vars.stringFilter,n);return r&&this._kill(r,e)?this._initProps(e,i,s,r,n):this._overwrite>1&&this._firstPT&&s.length>1&&J(e,this,i,this._overwrite,s)?(this._kill(i,e),this._initProps(e,i,s,r,n)):(this._firstPT&&(!1!==this.vars.lazy&&this._duration||this.vars.lazy&&!this._duration)&&(E[e._gsTweenID]=!0),l)},o.render=function(t,e,i){var s,r,n,a,o=this._time,l=this._duration,h=this._rawPrevTime;if(t>=l-1e-7&&t>=0)this._totalTime=this._time=l,this.ratio=this._ease._calcEnd?this._ease.getRatio(1):1,this._reversed||(s=!0,r="onComplete",i=i||this._timeline.autoRemoveChildren),0===l&&(this._initted||!this.vars.lazy||i)&&(this._startTime===this._timeline._duration&&(t=0),(0>h||0>=t&&t>=-1e-7||h===f&&"isPause"!==this.data)&&h!==t&&(i=!0,h>f&&(r="onReverseComplete")),this._rawPrevTime=a=!e||t||h===t?t:f);else if(1e-7>t)this._totalTime=this._time=0,this.ratio=this._ease._calcEnd?this._ease.getRatio(0):0,(0!==o||0===l&&h>0)&&(r="onReverseComplete",s=this._reversed),0>t&&(this._active=!1,0===l&&(this._initted||!this.vars.lazy||i)&&(h>=0&&(h!==f||"isPause"!==this.data)&&(i=!0),this._rawPrevTime=a=!e||t||h===t?t:f)),(!this._initted||this._startAt&&this._startAt.progress())&&(i=!0);else if(this._totalTime=this._time=t,this._easeType){var _=t/l,u=this._easeType,c=this._easePower;(1===u||3===u&&_>=.5)&&(_=1-_),3===u&&(_*=2),1===c?_*=_:2===c?_*=_*_:3===c?_*=_*_*_:4===c&&(_*=_*_*_*_),this.ratio=1===u?1-_:2===u?_:.5>t/l?_/2:1-_/2}else this.ratio=this._ease.getRatio(t/l);if(this._time!==o||i){if(!this._initted){if(this._init(),!this._initted||this._gc)return;if(!i&&this._firstPT&&(!1!==this.vars.lazy&&this._duration||this.vars.lazy&&!this._duration))return this._time=this._totalTime=o,this._rawPrevTime=h,z.push(this),void(this._lazy=[t,e]);this._time&&!s?this.ratio=this._ease.getRatio(this._time/l):s&&this._ease._calcEnd&&(this.ratio=this._ease.getRatio(0===this._time?0:1))}for(!1!==this._lazy&&(this._lazy=!1),this._active||!this._paused&&this._time!==o&&t>=0&&(this._active=!0),0===o&&(this._startAt&&(t>=0?this._startAt.render(t,!0,i):r||(r="_dummyGS")),this.vars.onStart&&(0!==this._time||0===l)&&(e||this._callback("onStart"))),n=this._firstPT;n;)n.f?n.t[n.p](n.c*this.ratio+n.s):n.t[n.p]=n.c*this.ratio+n.s,n=n._next;this._onUpdate&&(0>t&&this._startAt&&-1e-4!==t&&this._startAt.render(t,!0,i),e||(this._time!==o||s||i)&&this._callback("onUpdate")),r&&(!this._gc||i)&&(0>t&&this._startAt&&!this._onUpdate&&-1e-4!==t&&this._startAt.render(t,!0,i),s&&(this._timeline.autoRemoveChildren&&this._enabled(!1,!1),this._active=!1),!e&&this.vars[r]&&this._callback(r),0===l&&this._rawPrevTime===f&&a!==f&&(this._rawPrevTime=0))}},o._kill=function(t,e,i){if("all"===t&&(t=null),null==t&&(null==e||e===this.target))return this._lazy=!1,this._enabled(!1,!1);e="string"!=typeof e?e||this._targets||this.target:M.selector(e)||e;var s,r,n,a,o,l,h,_,u,f=i&&this._time&&i._startTime===this._startTime&&this._timeline===i._timeline;if((d(e)||F(e))&&"number"!=typeof e[0])for(s=e.length;--s>-1;)this._kill(t,e[s],i)&&(l=!0);else{if(this._targets){for(s=this._targets.length;--s>-1;)if(e===this._targets[s]){o=this._propLookup[s]||{},this._overwrittenProps=this._overwrittenProps||[],r=this._overwrittenProps[s]=t?this._overwrittenProps[s]||{}:"all";break}}else{if(e!==this.target)return!1;o=this._propLookup,r=this._overwrittenProps=t?this._overwrittenProps||{}:"all"}if(o){if(h=t||o,_=t!==r&&"all"!==r&&t!==o&&("object"!=typeof t||!t._tempKill),i&&(M.onOverwrite||this.vars.onOverwrite)){for(n in h)o[n]&&(u||(u=[]),u.push(n));if((u||!t)&&!K(this,i,e,u))return!1}for(n in h)(a=o[n])&&(f&&(a.f?a.t[a.p](a.s):a.t[a.p]=a.s,l=!0),a.pg&&a.t._kill(h)&&(l=!0),a.pg&&0!==a.t._overwriteProps.length||(a._prev?a._prev._next=a._next:a===this._firstPT&&(this._firstPT=a._next),a._next&&(a._next._prev=a._prev),a._next=a._prev=null),delete o[n]),_&&(r[n]=1);!this._firstPT&&this._initted&&this._enabled(!1,!1)}}return l},o.invalidate=function(){return this._notifyPluginsOfEnabled&&M._onPluginEvent("_onDisable",this),this._firstPT=this._overwrittenProps=this._startAt=this._onUpdate=null,this._notifyPluginsOfEnabled=this._active=this._lazy=!1,this._propLookup=this._targets?{}:[],A.prototype.invalidate.call(this),this.vars.immediateRender&&(this._time=-f,this.render(Math.min(0,-this._delay))),this},o._enabled=function(t,e){if(h||l.wake(),t&&this._gc){var i,s=this._targets;if(s)for(i=s.length;--i>-1;)this._siblings[i]=Q(s[i],this,!0);else this._siblings=Q(this.target,this,!0)}return A.prototype._enabled.call(this,t,e),!(!this._notifyPluginsOfEnabled||!this._firstPT)&&M._onPluginEvent(t?"_onEnable":"_onDisable",this)},M.to=function(t,e,i){return new M(t,e,i)},M.from=function(t,e,i){return i.runBackwards=!0,i.immediateRender=0!=i.immediateRender,new M(t,e,i)},M.fromTo=function(t,e,i,s){return s.startAt=i,s.immediateRender=0!=s.immediateRender&&0!=i.immediateRender,new M(t,e,s)},M.delayedCall=function(t,e,i,s,r){return new M(e,0,{delay:t,onComplete:e,onCompleteParams:i,callbackScope:s,onReverseComplete:e,onReverseCompleteParams:i,immediateRender:!1,lazy:!1,useFrames:r,overwrite:0})},M.set=function(t,e){return new M(t,0,e)},M.getTweensOf=function(t,e){if(null==t)return[];var i,s,r,n;if(t="string"!=typeof t?t:M.selector(t)||t,(d(t)||F(t))&&"number"!=typeof t[0]){for(i=t.length,s=[];--i>-1;)s=s.concat(M.getTweensOf(t[i],e));for(i=s.length;--i>-1;)for(n=s[i],r=i;--r>-1;)n===s[r]&&s.splice(i,1)}else if(t._gsTweenID)for(i=(s=Q(t).concat()).length;--i>-1;)(s[i]._gc||e&&!s[i].isActive())&&s.splice(i,1);return s||[]},M.killTweensOf=M.killDelayedCallsTo=function(t,e,i){"object"==typeof e&&(i=e,e=!1);for(var s=M.getTweensOf(t,e),r=s.length;--r>-1;)s[r]._kill(i,t)};var et=v("plugins.TweenPlugin",function(t,e){this._overwriteProps=(t||"").split(","),this._propName=this._overwriteProps[0],this._priority=e||0,this._super=et.prototype},!0);if(o=et.prototype,et.version="1.19.0",et.API=2,o._firstPT=null,o._addTween=B,o.setRatio=N,o._kill=function(t){var e,i=this._overwriteProps,s=this._firstPT;if(null!=t[this._propName])this._overwriteProps=[];else for(e=i.length;--e>-1;)null!=t[i[e]]&&i.splice(e,1);for(;s;)null!=t[s.n]&&(s._next&&(s._next._prev=s._prev),s._prev?(s._prev._next=s._next,s._prev=null):this._firstPT===s&&(this._firstPT=s._next)),s=s._next;return!1},o._mod=o._roundProps=function(t){for(var e,i=this._firstPT;i;)(e=t[this._propName]||null!=i.n&&t[i.n.split(this._propName+"_").join("")])&&"function"==typeof e&&(2===i.f?i.t._applyPT.m=e:i.m=e),i=i._next},M._onPluginEvent=function(t,e){var i,s,r,n,a,o=e._firstPT;if("_onInitAllProps"===t){for(;o;){for(a=o._next,s=r;s&&s.pr>o.pr;)s=s._next;(o._prev=s?s._prev:n)?o._prev._next=o:r=o,(o._next=s)?s._prev=o:n=o,o=a}o=e._firstPT=r}for(;o;)o.pg&&"function"==typeof o.t[t]&&o.t[t]()&&(i=!0),o=o._next;return i},et.activate=function(t){for(var e=t.length;--e>-1;)t[e].API===et.API&&(j[(new t[e])._propName]=t[e]);return!0},y.plugin=function(t){if(!(t&&t.propName&&t.init&&t.API))throw"illegal plugin definition.";var e,i=t.propName,s=t.priority||0,r=t.overwriteProps,n={init:"_onInitTween",set:"setRatio",kill:"_kill",round:"_mod",mod:"_mod",initAll:"_onInitAllProps"},a=v("plugins."+i.charAt(0).toUpperCase()+i.substr(1)+"Plugin",function(){et.call(this,i,s),this._overwriteProps=r||[]},!0===t.global),o=a.prototype=new et(i);for(e in o.constructor=a,a.API=t.API,n)"function"==typeof t[e]&&(o[n[e]]=t[e]);return a.version=t.version,et.activate([a]),a},n=t._gsQueue){for(a=0;a<n.length;a++)n[a]();for(o in m)m[o].func||t.console.log("GSAP encountered missing dependency: "+o)}h=!1}}("undefined"!=typeof module&&module.exports&&"undefined"!=typeof global?global:this||window,"TweenMax");